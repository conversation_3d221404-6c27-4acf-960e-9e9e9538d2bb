/**
 * Content Script Entry Point - WXT 规范化重构版本
 * 主要职责：协调各个模块的初始化和生命周期管理
 */

import { defineContentScript } from 'wxt/utils/define-content-script';

// 样式导入 - 保持原有CSS注入方式
import '../src/styles/highlight.css';
import '../src/styles/lucid-ui.css';
import '../src/styles/translate.css'; // 添加翻译样式
import '../src/styles/youtube-button.css'; // 添加YouTube按钮样式

// 统一模块导入
import { initializeLucidManagers, type LucidManagers } from '../src/content';
// 不再需要额外的日志系统导入
// 翻译测试工具（开发环境）
import { translateService } from '../src/features/translate';
// 翻译缓存管理器
import { translationCache } from '../src/features/translate/cache-manager';
// 新的轻量化扩展调试工具
import { setupExtensionDebug } from '../src/content/extension-debug';
// 新的Mock状态管理器
import { MockStateManager } from '../src/utils/mock-state-manager';
// 字幕翻译功能
import { directSubtitleManager } from '../src/features/subtitle-translation/DirectSubtitleManager';
// YouTube React集成（新版本）
import { initializeYouTubeReactIntegration, cleanupYouTubeReactIntegration } from '../src/content/youtube-react-integration';
// 新的轻量化翻译系统
import { 
  initializeTranslationSystem, 
  translateElement,
  translatePage, 
  clearPageTranslations,
  getTranslationStats 
} from '../src/index';
// Exclusion debugging is now handled by the exclusion manager

export default defineContentScript({
  matches: ['<all_urls>'],
  cssInjectionMode: 'manifest', // 高亮系统需要页面级CSS注入
  
  main() {
    const startTime = Date.now(); // 记录系统启动时间
    console.log('🚀 [lucid-system|STARTUP] Lucid系统初始化 - 全局调试函数 + 消息监听器 + 翻译事件监听器', {
      url: window.location.href,
      readyState: document.readyState,
      timestamp: startTime
    });

    // 🔧 调试：设置全局调试函数
    setupGlobalDebugFunctions();

    // 立即设置消息监听器，不等DOM
    setupMessageListeners();

    // 等待DOM就绪
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => initializeLucidSystem(startTime));
    } else {
      initializeLucidSystem(startTime);
    }

    /**
     * 🔧 调试：设置简化的全局调试函数
     */
    function setupGlobalDebugFunctions(): void {
      // 只保留缓存相关的调试函数，其他由 LucidDebug 处理
      const globalObj = window as any;
      
      // 清除翻译缓存的全局函数
      globalObj.clearTranslationCache = async () => {
        try {
          await translationCache.clear();
          console.log('✅ [cache-manager|INFO] 翻译缓存已清除！请重新翻译页面。');
          return true;
        } catch (error) {
          console.log('❌ [cache-manager|ERROR] 清除翻译缓存失败:', error);
          return false;
        }
      };
      
      // 获取缓存状态的全局函数
      globalObj.getCacheStats = () => {
        const stats = translationCache.getStats();
        console.log('✅ [cache-manager|INFO] 缓存统计:', stats);
        return stats;
      };
      
      // 暴露字幕管理器供调试使用
      globalObj.directSubtitleManager = directSubtitleManager;
      
      // 全局调试函数设置完成，无需额外日志
    }

    /**
     * 设置消息监听器
     */
    function setupMessageListeners(): void {
      // debugContent.info('Setting up message listeners...'); // 已在下面有了，避免重复
      // 消息监听器设置，无需额外日志
      
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('✅ [message-handler|INFO] Content Script received message:', {
          type: message.type,
          sender: sender.tab?.url || 'background',
          timestamp: Date.now()
        });
        
        console.log('🔧 [message-handler|DEBUG] Received message from background:', message.type);
        
        if (message.type === 'TRANSLATE_TEXT_REQUEST') {
          console.log('⚙️ [translation|PROCESSING] Processing TRANSLATE_TEXT_REQUEST');
          handleTranslateTextRequest(message.payload)
            .then(result => {
              console.log('✅ [translation|SUCCESS] Translation completed:', result);
              sendResponse(result);
            })
            .catch(error => {
              console.log('❌ [translation|ERROR] Translation failed:', error);
              sendResponse({ success: false, error: error.message });
            });
          return true; // 异步响应
        }

        if (message.type === 'TRANSLATE_PAGE_REQUEST') {
          console.log('⚙️ [translation|PROCESSING] Processing TRANSLATE_PAGE_REQUEST');
          handleTranslatePageRequest(message.payload)
            .then(result => {
              console.log('✅ [translation|SUCCESS] Page translation completed:', result);
              sendResponse(result);
            })
            .catch(error => {
              console.log('❌ [translation|ERROR] Page translation failed:', error);
              sendResponse({ success: false, error: error.message });
            });
          return true; // 异步响应
        }
        
        if (message.type === 'TOGGLE_LUCID_SLIDER') {
          console.log('✅ [ui-slider|INFO] Toggle Lucid Slider requested');
          // 这里可以添加滑动面板切换逻辑
          return true;
        }

        if (message.type === 'CLEAR_TRANSLATION_CACHE') {
          console.log('✅ [cache-manager|INFO] Clear translation cache requested');
          translationCache.clear()
            .then(() => {
              sendResponse({ success: true, message: '翻译缓存已清除' });
            })
            .catch(error => {
              sendResponse({ success: false, error: error.message });
            });
          return true; // 异步响应
        }

        // 新的轻量化翻译系统消息处理
        if (message.type === 'CLEAR_TRANSLATIONS') {
          const clearedCount = clearPageTranslations();
          console.log('✅ [translation|INFO] Cleared ${clearedCount} translations');
          return true;
        }

        if (message.type === 'GET_TRANSLATION_STATS') {
          const stats = getTranslationStats();
          console.log('✅ [translation|INFO] Translation stats requested', stats);
          sendResponse(stats);
          return true;
        }

        // 处理Mock翻译设置消息
        if (message.type === 'TOGGLE_MOCK_TRANSLATION') {
          console.log('✅ [mock-translation|INFO] Toggle mock translation requested');
          const currentState = localStorage.getItem('lucid-force-mock') === 'true';
          
          if (currentState) {
            localStorage.removeItem('lucid-force-mock');
            localStorage.removeItem('lucid-mock-return-original');
            showNotification('🔌 Mock翻译已禁用', '刷新页面生效', '#f44336');
          } else {
            localStorage.setItem('lucid-force-mock', 'true');
            showNotification('🔧 Mock翻译已启用', '刷新页面生效', '#4CAF50');
          }
          return true;
        }

        if (message.type === 'TOGGLE_ORIGINAL_MODE') {
          console.log('✅ [mock-translation|INFO] Toggle original mode requested');
          const currentState = localStorage.getItem('lucid-mock-return-original') === 'true';
          
          if (currentState) {
            localStorage.removeItem('lucid-mock-return-original');
            showNotification('❌ 返回原文模式已禁用', '现在会使用Mock翻译', '#FF9800');
          } else {
            localStorage.setItem('lucid-mock-return-original', 'true');
            showNotification('✅ 返回原文模式已启用', '翻译将返回原文', '#4CAF50');
          }
          return true;
        }

        if (message.type === 'MOCK_TRANSLATION_STATUS') {
          console.log('✅ [mock-translation|INFO] Mock translation status requested');
          const mockEnabled = localStorage.getItem('lucid-force-mock') === 'true';
          const originalMode = localStorage.getItem('lucid-mock-return-original') === 'true';
          
          showNotification(
            '📊 Mock翻译状态',
            `Mock翻译: ${mockEnabled ? '✅启用' : '❌禁用'}\n返回原文: ${originalMode ? '✅启用' : '❌禁用'}`,
            '#2196F3',
            5000
          );
          return true;
        }
      });
      
      // 消息监听器设置完成，无需额外日志
    }

    /**
     * 设置翻译页面事件监听器
     * 修复测试页面无法触发翻译的问题
     */
    function setupTranslationEventListeners(): void {
      // 翻译事件监听器设置，无需额外日志
      
      // 监听自定义翻译页面事件（来自测试页面）
      document.addEventListener('lucid-translate-page', async (event: Event) => {
        const customEvent = event as CustomEvent;
        console.log('✅ [translation-events|INFO] Received lucid-translate-page event', customEvent.detail);
        
        try {
          const detail = customEvent.detail || {};
          const { source = 'unknown', testMode = false, mode = 'default' } = detail;
          
          console.log('✅ [translation-pipeline|INFO] Starting page translation triggered by ${source}', { testMode, mode });
          
          let result;
          
          // 使用默认的轻量化翻译系统
          result = await translatePage();
          
          if (result.successCount > 0) {
            console.log('✅ [translation-pipeline|SUCCESS] Page translation completed successfully', {
              totalCount: result.totalCount,
              successCount: result.successCount,
              failureCount: result.failureCount,
              mode,
              source
            });
            
            // 发送完成事件
            document.dispatchEvent(new CustomEvent('lucid-translation-complete', {
              detail: {
                totalCount: result.totalCount,
                successCount: result.successCount,
                failureCount: result.failureCount,
                mode,
                source
              }
            }));
          } else {
            throw new Error('Translation failed - no successful translations');
          }
          
        } catch (error) {
          console.log('❌ [translation-pipeline|ERROR] Page translation failed:', error);
          
          // 发送失败事件
          document.dispatchEvent(new CustomEvent('lucid-translation-failed', {
            detail: {
              error: error instanceof Error ? error.message : 'Unknown error',
              source: customEvent.detail?.source || 'unknown'
            }
          }));
        }
      });
      
      console.log('✅ [translation-events|INFO] Translation event listeners setup completed');
    }

    /**
     * 处理翻译文本请求（新的轻量化系统）
     */
    async function handleTranslateTextRequest(payload: { text: string; from?: string; to?: string }): Promise<any> {
      const { text, from = 'auto', to = 'zh' } = payload;
      
      console.log('🔧 [translation|DEBUG] 开始右键翻译功能（轻量化系统）', { text, from, to });
      
      try {
        // 使用新的轻量化翻译系统进行文本翻译
        // 创建临时元素来使用translateElement API
        const tempElement = document.createElement('div');
        tempElement.textContent = text;
        tempElement.style.display = 'none';
        document.body.appendChild(tempElement);
        
        try {
          const result = await translateElement(tempElement, {
            format: 'text'
          });
          
          // 清理临时元素
          document.body.removeChild(tempElement);
          
          if (result.success && result.translatedText) {
            console.log('✅ [translation|SUCCESS] 轻量化翻译成功', {
              originalText: text.slice(0, 50),
              translatedText: result.translatedText.slice(0, 50),
              duration: result.duration
            });
            
            // 在页面上显示翻译结果
            displayTranslationResult(text, result.translatedText);
            
            return {
              translations: [result.translatedText],
              engine: 'lightweight-system',
              api: 'lucid-translation'
            };
          } else {
            throw new Error(result.error || 'Translation failed');
          }
        } catch (translationError) {
          // 确保清理临时元素
          if (tempElement.parentNode) {
            document.body.removeChild(tempElement);
          }
          throw translationError;
        }
      } catch (error) {
        console.log('❌ [translation|ERROR] 轻量化翻译失败:', error);
        
        // 显示错误信息
        displayTranslationError(text, error);
        
        throw error;
      }
    }

    /**
     * 确保Mock翻译系统准备就绪
     */
    async function ensureMockTranslationReady(): Promise<void> {
      try {
        await MockStateManager.ensureInitialized();
        console.log('🔧 [mock-translation|DEBUG] Mock翻译系统状态检查完成');
      } catch (error) {
        console.log('⚠️ [mock-translation|WARN] MockStateManager初始化失败，使用备用方案:', error);
        // 如果MockStateManager失败，回退到原有逻辑
        await forceInitializeMockTranslation();
      }
    }

    /**
     * 强制初始化Mock翻译系统
     */
    async function forceInitializeMockTranslation(): Promise<void> {
      try {
        console.log('✅ [mock-translation|INFO] 强制初始化Mock翻译系统...');
        
        // 动态导入mock-integration模块
        const mockIntegration = await import('../src/content/mock-integration');
        
        // 强制设置localStorage标志
        localStorage.setItem('lucid-force-mock', 'true');
        
        // 直接调用强制设置函数
        if (mockIntegration.forceSetupMockTranslation) {
          await mockIntegration.forceSetupMockTranslation();
          console.log('✅ [mock-translation|INFO] Mock翻译系统强制初始化完成');
        } else {
          // 如果模块函数不可用，创建简单的Mock系统
          await createSimpleMockSystem();
        }
      } catch (error) {
        console.log('❌ [mock-translation|ERROR] 强制初始化Mock翻译失败:', error);
        // 创建最简单的Mock系统作为最后的回退
        await createSimpleMockSystem();
      }
    }

    /**
     * 创建简单的Mock翻译系统（最后的回退）
     */
    async function createSimpleMockSystem(): Promise<void> {
      const globalObj = window as any;
      
      const simpleMockTranslate = async (text: string) => {
        // 简单的词汇映射
        const map: Record<string, string> = {
          'hello': '你好', 'world': '世界', 'test': '测试', 'page': '页面',
          'google': '谷歌', 'chrome': '浏览器', 'extension': '扩展',
          'favorite': '最喜欢的', 'our': '我们的', 'extensions': '扩展程序'
        };
        
        return text.toLowerCase().split(/\s+/).map(word => 
          map[word] || `[${word}译]`
        ).join(' ');
      };
      
      const simpleMockTranslatePage = async () => {
        console.log('🌐 [mock-translation|INFO] 使用简单Mock翻译系统...');
        
        const elements = document.querySelectorAll('h1, h2, h3, p, li');
        let translated = 0;
        
        for (let i = 0; i < Math.min(elements.length, 5); i++) {
          const element = elements[i] as HTMLElement;
          const text = element.textContent?.trim();
          
          if (text && text.length > 3 && text.length < 100) {
            const translation = await simpleMockTranslate(text);
            
            // 创建翻译显示
            const wrapper = document.createElement('div');
            wrapper.style.cssText = 'color: #2196F3; font-style: italic; margin-top: 4px; border-left: 3px solid #2196F3; padding-left: 8px; font-size: 0.9em;';
            wrapper.textContent = translation;
            wrapper.className = 'lucid-simple-translation';
            
            element.appendChild(wrapper);
            translated++;
          }
        }
        
        console.log('✅ [mock-translation|SUCCESS] 简单Mock翻译完成: ${translated} 个元素');
        return { translated, stats: { scan: { translatableNodes: translated } } };
      };
      
      // 设置全局对象
      globalObj.lucidExt = {
        translatePage: simpleMockTranslatePage,
        clearTranslations: () => {
          document.querySelectorAll('.lucid-simple-translation').forEach(el => el.remove());
          console.log('🧹 [mock-translation|INFO] 简单翻译已清除');
        },
        getStats: () => ({ message: '简单Mock翻译激活' })
      };
      
      globalObj.testPageTranslation = simpleMockTranslatePage;
      
      console.log('✅ [mock-translation|INFO] 简单Mock翻译系统创建完成');
    }

    /**
     * 检测函数是否为fallback函数（错误抛出占位符）
     */
    function isFallbackFunction(fn: Function): boolean {
      try {
        const fnString = fn.toString();
        return (
          fnString.includes('DOM injection system not available') ||
          fnString.includes('Mock translation service not available') ||
          fnString.includes('Test functions not available') ||
          fnString.includes('not available in production')
        );
      } catch {
        return false;
      }
    }

    /**
     * 处理整页翻译请求（新的轻量化系统）
     */
    async function handleTranslatePageRequest(payload: { targetLanguage?: string; testMode?: boolean }): Promise<any> {
      const { targetLanguage = 'zh-CN', testMode = false } = payload;
      
      console.log('⚙️ [translation|PROCESSING] 开始整页翻译（轻量化系统）', { targetLanguage, testMode });
      
      try {
        // 添加性能计时
        const performanceStartTime = performance.now();
        
        // 使用新的轻量化翻译系统
        const result = await translatePage();
        
        // 计算总性能时间
        const totalPerformanceTime = performance.now() - performanceStartTime;
        
        const successRate = (result.successCount / result.totalCount) * 100;
        
        console.log('✅ [translation|SUCCESS] 轻量化翻译完成', {
          totalCount: result.totalCount,
          successCount: result.successCount,
          failureCount: result.failureCount,
          successRate: successRate.toFixed(1) + '%',
          systemDuration: result.totalDuration, // 系统内部计时
          totalPerformanceTime: totalPerformanceTime.toFixed(2) + 'ms', // 完整性能计时
          speedImprovement: '预期比原系统快 ~18x (5ms vs 100ms delay)'
        });
        
        // 显示详细的测试结果（特别是测试模式下）
        if (testMode) {
          displayTestModeResult(result, successRate, totalPerformanceTime);
        } else {
          // 显示成功通知
          displayPageTranslationResult(result);
        }
        
        return result;
      } catch (error) {
        console.log('❌ [translation|ERROR] 轻量化翻译失败:', error);
        displayPageTranslationError(error);
        throw error;
      }
    }

    /**
     * 显示测试模式结果（详细统计）
     */
    function displayTestModeResult(result: any, successRate: number, totalPerformanceTime?: number): void {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${successRate >= 85 ? '#4CAF50' : successRate >= 70 ? '#FF9800' : '#f44336'};
        color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        max-width: 400px;
        word-wrap: break-word;
      `;
      
      const statusIcon = successRate >= 85 ? '🏆' : successRate >= 70 ? '⚠️' : '❌';
      const statusText = successRate >= 85 ? '成功达标!' : successRate >= 70 ? '接近目标' : '需要改进';
      const baseline = 40.5;
      const improvement = (successRate - baseline).toFixed(1);
      
      // 性能改进计算
      const previousTime = result.totalCount * 100; // 之前100ms延迟
      const currentTime = totalPerformanceTime || result.totalDuration;
      const speedImprovement = previousTime > 0 ? (previousTime / currentTime).toFixed(1) : 'N/A';
      
      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 12px; font-size: 16px;">
          ${statusIcon} 轻量化翻译系统测试结果
        </div>
        <div style="margin-bottom: 8px;">
          <strong>成功率:</strong> ${successRate.toFixed(1)}% 
          <span style="font-size: 12px;">(目标: 85%+)</span>
        </div>
        <div style="margin-bottom: 8px;">
          <strong>改进:</strong> +${improvement}% 
          <span style="font-size: 12px;">(基线: ${baseline}%)</span>
        </div>
        <div style="margin-bottom: 8px;">
          <strong>处理:</strong> ${result.totalCount} 个元素
        </div>
        <div style="margin-bottom: 8px;">
          <strong>成功:</strong> ${result.successCount} | <strong>失败:</strong> ${result.failureCount}
        </div>
        <div style="margin-bottom: 8px;">
          <strong>耗时:</strong> ${Math.round(currentTime)}ms 
          <span style="font-size: 12px;">(预期: ${previousTime}ms)</span>
        </div>
        <div style="margin-bottom: 8px;">
          <strong>性能提升:</strong> ${speedImprovement}x 更快 ⚡
        </div>
        <div style="font-size: 12px; margin-top: 10px; opacity: 0.9;">
          <strong>状态:</strong> ${statusText} | <strong>样式:</strong> 0.6透明度已应用
        </div>
      `;
      
      document.body.appendChild(notification);
      
      // 测试模式下显示更长时间（10秒）
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 10000);
      
      console.log('✅ [ui-notification|INFO] Test mode result displayed', { successRate, improvement });
    }

    /**
     * 显示页面翻译成功结果
     */
    function displayPageTranslationResult(result: any): void {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        max-width: 350px;
        word-wrap: break-word;
      `;
      
      const stats = result?.stats || result;
      const translatedCount = stats?.scan?.translatableNodes || stats?.translated || 'unknown';
      
      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px;">🌐 网页翻译完成</div>
        <div style="margin-bottom: 5px;">✅ 已翻译 ${translatedCount} 个元素</div>
        <div>💡 使用 Alt+V 切换显示模式</div>
      `;
      
      document.body.appendChild(notification);
      
      // 5秒后自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 5000);
      
      console.log('✅ [ui-notification|INFO] Page translation result displayed');
    }

    /**
     * 显示页面翻译错误
     */
    function displayPageTranslationError(error: any): void {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        max-width: 350px;
        word-wrap: break-word;
      `;
      
      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px;">❌ 网页翻译失败</div>
        <div style="margin-bottom: 5px;">错误: ${error.message || '未知错误'}</div>
        <div>💡 请检查控制台了解详情</div>
      `;
      
      document.body.appendChild(notification);
      
      // 5秒后自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 5000);
      
      console.log('❌ [ui-notification|ERROR] Page translation error displayed');
    }

    /**
     * 检测是否为测试环境或手动启用Mock翻译
     */
    function isTestEnvironment(): boolean {
      return (
        window.location.protocol === 'file:' ||
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        document.title.toLowerCase().includes('test') ||
        localStorage.getItem('lucid-use-mock') === 'true' ||
        localStorage.getItem('lucid-debug') === 'true'
      );
    }

    /**
     * 在页面上显示翻译结果
     */
    function displayTranslationResult(originalText: string, translation: string): void {
      // 创建一个简单的通知显示翻译结果
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        max-width: 300px;
        word-wrap: break-word;
      `;
      
      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px;">🌐 翻译结果</div>
        <div style="margin-bottom: 5px;"><strong>原文:</strong> ${originalText}</div>
        <div><strong>译文:</strong> ${translation}</div>
      `;
      
      document.body.appendChild(notification);
      
      // 3秒后自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 3000);
      
      console.log('✅ [ui-notification|INFO] Translation result displayed on page');
    }

    /**
     * 显示通知消息
     */
    function showNotification(title: string, message: string, color: string = '#4CAF50', duration: number = 3000): void {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${color};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        max-width: 350px;
        word-wrap: break-word;
        white-space: pre-line;
      `;
      
      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
        <div>${message}</div>
      `;
      
      document.body.appendChild(notification);
      
      // 自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, duration);
    }

    /**
     * 显示翻译错误信息
     */
    function displayTranslationError(originalText: string, error: any): void {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        max-width: 300px;
        word-wrap: break-word;
      `;
      
      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px;">❌ 翻译失败</div>
        <div style="margin-bottom: 5px;"><strong>文本:</strong> ${originalText}</div>
        <div><strong>错误:</strong> ${error.message || '未知错误'}</div>
      `;
      
      document.body.appendChild(notification);
      
      // 3秒后自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 3000);
      
      console.log('❌ [ui-notification|ERROR] Translation error displayed on page');
    }

    /**
     * 设置翻译引擎和视图控制器的集成
     * 确保在原文模式下不进行不必要的API调用
     */
    function setupTranslationIntegration(): void {
      try {
        // 导入视图控制器
        import('../src/content/view-controller').then(({ getViewModeController }) => {
          const viewController = getViewModeController();
          const engineManager = translateService.getEngineManager();
          
          // 将引擎管理器传递给视图控制器
          viewController.setEngineManager(engineManager);
          
          console.log('✅ [translation-integration|INFO] Translation integration setup completed', {
            engineManager: !!engineManager,
            viewController: !!viewController
          });
        }).catch(error => {
          console.log('❌ [translation-integration|ERROR] Failed to setup translation integration:', error);
        });
      } catch (error) {
        console.log('❌ [translation-integration|ERROR] Error in setupTranslationIntegration:', error);
      }
    }

    /**
     * 初始化 Lucid 系统的主函数
     */
    async function initializeLucidSystem(startTime: number): Promise<void> {
      try {
        console.log('🚀 [lucid-system|STARTUP] Lucid系统启动中...');
        console.group('🚀 [lucid-system|STARTUP] Lucid系统初始化');

        // 1. 初始化智能注入翻译系统
        console.log('⚙️ [translation-pipeline|INFO] 正在初始化翻译系统...');
        const translationManager = initializeTranslationSystem({
          debug: true,
          // 启用智能注入规则引擎
          enableSmartInjection: true,
          security: {
            maxLength: 15000,
            allowedTags: ['a', 'strong', 'b', 'em', 'i', 'u', 'span'],
            strictMode: false
          },
          performance: {
            batchSize: 10,
            concurrency: 3,
            debounceMs: 100
          }
        });

        console.log('✅ [translation-pipeline|STARTUP] 翻译系统已就绪');
        console.log('🔧 [translation-pipeline|DEBUG] Translation system stats:', translationManager.getStats());

        // 2. 设置翻译引擎和视图控制器集成
        console.log('🔌 [translation-integration|INFO] 正在配置翻译集成...');
        setupTranslationIntegration();
        console.log('✅ [translation-integration|STARTUP] 翻译集成已配置');

        // 3. 初始化各个管理器
        console.log('🖼️ [ui-manager|INFO] 正在初始化UI管理器...');
        const managers: LucidManagers = await initializeManagers();
        console.log('✅ [ui-manager|STARTUP] UI管理器已就绪');

        // 4. 设置清理逻辑
        console.log('🧹 [cleanup-handler|INFO] 正在配置清理处理器...');
        setupCleanupHandlers(managers);

        // 5. 加载翻译测试工具
        console.log('🔧 [debug-tools|INFO] 正在加载调试工具...');
        loadTranslateTestTools();
        setupExtensionDebug();

        // 6. 设置翻译页面事件监听器
        setupTranslationEventListeners();

        // 7. 初始化字幕翻译功能（仅在YouTube页面）
        if (window.location.hostname.includes('youtube.com')) {
          console.log('🎬 [youtube-subtitle|INFO] 检测到YouTube页面，初始化字幕翻译功能...');
          await initializeYouTubeReactIntegration();
          
          console.log('🎬 [youtube-debug|INFO] 加载YouTube调试工具...');
          loadYouTubeDebugTools();
        }

        console.groupEnd();
        const initTime = Date.now() - startTime;
        console.log(`🎉 [lucid-system|STARTUP] Lucid系统初始化完成! 耗时: ${initTime}ms`);
        
      } catch (error) {
        console.log('❌ [lucid-system|ERROR] Error initializing Lucid system:', error);
      }
    }

    /**
     * 初始化所有管理器（使用统一接口）
     */
    async function initializeManagers(): Promise<LucidManagers> {
      return await initializeLucidManagers();
    }

    /**
     * 加载精简的翻译测试工具
     */

    /**
     * 加载YouTube调试工具
     */
    function loadYouTubeDebugTools(): void {
      try {
        import('../src/debug/youtube-debug').then((debugModule) => {
          console.log('✅ [youtube-debug|INFO] YouTube调试工具加载成功');
          
          // 自动运行扩展状态检查
          setTimeout(() => {
            if (window.LucidYouTubeDebug) {
              window.LucidYouTubeDebug.checkExtensionStatus();
            }
          }, 2000);
          
        }).catch(error => {
          console.warn('⚠️ [youtube-debug|WARN] YouTube调试工具加载失败:', error);
        });
      } catch (error) {
        console.warn('⚠️ [youtube-debug|WARN] YouTube调试工具导入失败:', error);
      }
    }

    function loadTranslateTestTools(): void {
      try {
        const globalObj = window as any;
        
        // 只暴露核心调试功能到LucidDebug命名空间
        const existingLucidDebug = globalObj.LucidDebug || {};
        
        globalObj.LucidDebug = {
          ...existingLucidDebug,
          
          // 翻译测试
          translate: {
            test: async (text: string = 'Hello, World!') => {
              console.log('🔧 [translation-test|INFO] 开始翻译测试', { text });
              try {
                const result = await translateService.translateText(text, {
                  from: 'auto', to: 'zh'
                });
                console.log('✅ [translation-test|SUCCESS] 翻译测试成功', { text, result });
                return result;
              } catch (error) {
                console.log('❌ [translation-test|ERROR] 翻译测试失败', { text, error });
                throw error;
              }
            },
            
            status: () => {
              const engines = translateService.getAvailableEngines();
              const recommended = translateService.getRecommendedEngine();
              const stats = translateService.getServiceStats();
              console.log('✅ [translation-test|INFO] 翻译引擎状态', { engines, recommended, stats });
              return { engines, recommended, stats };
            },
            
            stats: getTranslationStats,
            clear: clearPageTranslations,
            service: translateService
          }
        };
        
        console.log('✅ [debug-tools|INFO] 精简翻译测试工具已加载到 LucidDebug.translate');
        
      } catch (error) {
        console.log('❌ [debug-tools|ERROR] 翻译测试工具加载失败:', error);
      }
    }

    /**
     * 设置清理处理器
     */
    function setupCleanupHandlers(managers: LucidManagers): void {
      // 页面卸载前清理所有管理器
      window.addEventListener('beforeunload', () => {
        try {
          managers.highlightManager.destroy();
          managers.tooltipManager.destroy();
          managers.sliderManager.destroy();
          managers.interactionHandlers.destroy();
          // debugFunctions.destroy() 已移除 - 调试功能已简化
          
          // 清理YouTube React集成
          if (window.location.hostname.includes('youtube.com')) {
            cleanupYouTubeReactIntegration();
          }
          
          console.log('✅ [cleanup-handler|INFO] All managers cleaned up successfully');
        } catch (error) {
          console.log('❌ [cleanup-handler|ERROR] Error during cleanup:', error);
        }
      });
    }
  },
});