<div id="container" class="style-scope ytd-player">
  <div
    class="html5-video-player ytp-transparent ytp-exp-bottom-control-flexbox ytp-modern-caption ytp-exp-ppp-update ytp-livebadge-color ytp-fit-cover-video ytp-fine-scrubbing-exp ytp-hide-info-bar ytp-autonav-endscreen-cancelled-state paused-mode"
    tabindex="-1"
    id="movie_player"
    data-version="/s/player/461f4c95/player_ias.vflset/en_US/base.js"
    aria-label="YouTube Video Player"
  >
    <div class="html5-video-container" data-layer="0" draggable="true">
      <video
        tabindex="-1"
        class="video-stream html5-main-video"
        controlslist="nodownload"
        src="blob:https://www.youtube.com/1ed27d29-0197-4d5b-aac8-edfd28ed25d2"
        style="width: 640px; height: 360px; left: 0px; top: 0px"
      ></video>
    </div>
    <div class="ytp-gradient-top" data-layer="1"></div>
    <div class="ytp-chrome-top" data-layer="1">
      <button
        class="ytp-playlist-menu-button ytp-button"
        title=""
        aria-owns="ytp-id-22"
        aria-haspopup="true"
        aria-label="Playlist"
        data-overlay-order="2"
        style="display: none"
      >
        <div class="ytp-playlist-menu-button-icon">
          <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
            <use class="ytp-svg-shadow" xlink:href="#ytp-id-23"></use>
            <path
              d="m 22.53,21.42 0,6.85 5.66,-3.42 -5.66,-3.42 0,0 z m -11.33,0 9.06,0 0,2.28 -9.06,0 0,-2.28 0,0 z m 0,-9.14 13.6,0 0,2.28 -13.6,0 0,-2.28 0,0 z m 0,4.57 13.6,0 0,2.28 -13.6,0 0,-2.28 0,0 z"
              fill="#fff"
              id="ytp-id-23"
            ></path>
          </svg>
        </div>
        <div class="ytp-playlist-menu-button-title"></div>
        <div class="ytp-playlist-menu-button-text"></div>
      </button>
      <div class="ytp-title-channel">
        <div class="ytp-title-beacon"></div>
        <a
          class="ytp-title-channel-logo"
          target="_blank"
          role="link"
          tabindex="0"
        ></a>
        <div class="ytp-title-expanded-overlay">
          <div class="ytp-title-expanded-heading">
            <div class="ytp-title-expanded-title"><a target="_blank"></a></div>
            <div class="ytp-title-expanded-subtitle"></div>
          </div>
        </div>
      </div>
      <div class="ytp-title">
        <div class="ytp-title-text">
          <a
            class="ytp-title-link yt-uix-sessionlink ytp-title-fullerscreen-link"
            target="_blank"
            data-sessionlink="feature=player-title"
            tabindex="-1"
            >Horizon Alpha - is it GPT 5</a
          >
          <div class="ytp-title-subtext">
            <a class="ytp-title-channel-name" target="_blank" href=""></a>
          </div>
        </div>
      </div>
      <div class="ytp-chrome-top-buttons">
        <button
          class="ytp-button ytp-search-button ytp-show-search-title"
          title=""
          data-tooltip-title="Search"
          data-tooltip-opaque="false"
          aria-label="Search"
          style="display: none"
        >
          <div class="ytp-search-icon">
            <svg height="100%" version="1.1" viewBox="0 0 24 24" width="100%">
              <path
                class="ytp-svg-fill"
                d="M21.24,19.83l-5.64-5.64C16.48,13.02,17,11.57,17,10c0-3.87-3.13-7-7-7s-7,3.13-7,7c0,3.87,3.13,7,7,7 c1.57,0,3.02-0.52,4.19-1.4l5.64,5.64L21.24,19.83z M5,10c0-2.76,2.24-5,5-5s5,2.24,5,5c0,2.76-2.24,5-5,5S5,12.76,5,10z"
              ></path>
            </svg>
          </div>
          <div class="ytp-search-title">Search</div></button
        ><button
          class="ytp-watch-later-button ytp-button"
          title=""
          data-tooltip-opaque="false"
          data-tooltip-title="Watch later"
          aria-label="Watch later"
        >
          <div class="ytp-watch-later-icon">
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-26"></use>
              <path
                class="ytp-svg-fill"
                d="M18,8 C12.47,8 8,12.47 8,18 C8,23.52 12.47,28 18,28 C23.52,28 28,23.52 28,18 C28,12.47 23.52,8 18,8 L18,8 Z M16,19.02 L16,12.00 L18,12.00 L18,17.86 L23.10,20.81 L22.10,22.54 L16,19.02 Z"
                id="ytp-id-26"
              ></path>
            </svg>
          </div>
          <div class="ytp-watch-later-title">Watch later</div></button
        ><button
          class="ytp-button ytp-share-button ytp-share-button-visible"
          title=""
          data-tooltip-title="Share"
          aria-haspopup="true"
          aria-owns="ytp-id-25"
          data-tooltip-opaque="false"
          aria-label="Share"
        >
          <div class="ytp-share-icon">
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-44"></use>
              <path
                class="ytp-svg-fill"
                d="m 20.20,14.19 0,-4.45 7.79,7.79 -7.79,7.79 0,-4.56 C 16.27,20.69 12.10,21.81 9.34,24.76 8.80,25.13 7.60,27.29 8.12,25.65 9.08,21.32 11.80,17.18 15.98,15.38 c 1.33,-0.60 2.76,-0.98 4.21,-1.19 z"
                id="ytp-id-44"
              ></path>
            </svg>
          </div>
          <div class="ytp-share-title">Share</div></button
        ><button
          class="ytp-button ytp-copylink-button"
          title=""
          data-tooltip-opaque="false"
          data-tooltip-title="Copy link"
          aria-label="Copy link"
          style="display: none"
        >
          <div class="ytp-copylink-icon">
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-45"></use>
              <path
                class="ytp-svg-fill"
                d="M21.9,8.3H11.3c-0.9,0-1.7,.8-1.7,1.7v12.3h1.7V10h10.6V8.3z M24.6,11.8h-9.7c-1,0-1.8,.8-1.8,1.8v12.3  c0,1,.8,1.8,1.8,1.8h9.7c1,0,1.8-0.8,1.8-1.8V13.5C26.3,12.6,25.5,11.8,24.6,11.8z M24.6,25.9h-9.7V13.5h9.7V25.9z"
                id="ytp-id-45"
              ></path>
            </svg>
          </div>
          <div class="ytp-copylink-title" aria-hidden="true">
            Copy link
          </div></button
        ><button
          class="ytp-button ytp-cards-button"
          aria-label="Show cards"
          aria-owns="iv-drawer"
          aria-haspopup="true"
          data-tooltip-opaque="false"
          style="display: none"
        >
          <span class="ytp-cards-button-icon-default"
            ><div class="ytp-cards-button-icon">
              <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
                <use class="ytp-svg-shadow" xlink:href="#ytp-id-3"></use>
                <path
                  class="ytp-svg-fill"
                  d="M18,8 C12.47,8 8,12.47 8,18 C8,23.52 12.47,28 18,28 C23.52,28 28,23.52 28,18 C28,12.47 23.52,8 18,8 L18,8 Z M17,16 L19,16 L19,24 L17,24 L17,16 Z M17,12 L19,12 L19,14 L17,14 L17,12 Z"
                  id="ytp-id-3"
                ></path>
              </svg>
            </div>
            <div class="ytp-cards-button-title">Info</div></span
          ><span class="ytp-cards-button-icon-shopping"
            ><div class="ytp-cards-button-icon">
              <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
                <path
                  class="ytp-svg-shadow"
                  d="M 27.99,18 A 9.99,9.99 0 1 1 8.00,18 9.99,9.99 0 1 1 27.99,18 z"
                ></path>
                <path
                  class="ytp-svg-fill"
                  d="M 18,8 C 12.47,8 8,12.47 8,18 8,23.52 12.47,28 18,28 23.52,28 28,23.52 28,18 28,12.47 23.52,8 18,8 z m -4.68,4 4.53,0 c .35,0 .70,.14 .93,.37 l 5.84,5.84 c .23,.23 .37,.58 .37,.93 0,.35 -0.13,.67 -0.37,.90 L 20.06,24.62 C 19.82,24.86 19.51,25 19.15,25 c -0.35,0 -0.70,-0.14 -0.93,-0.37 L 12.37,18.78 C 12.13,18.54 12,18.20 12,17.84 L 12,13.31 C 12,12.59 12.59,12 13.31,12 z m .96,1.31 c -0.53,0 -0.96,.42 -0.96,.96 0,.53 .42,.96 .96,.96 .53,0 .96,-0.42 .96,-0.96 0,-0.53 -0.42,-0.96 -0.96,-0.96 z"
                  fill-opacity="1"
                ></path>
                <path
                  class="ytp-svg-shadow-fill"
                  d="M 24.61,18.22 18.76,12.37 C 18.53,12.14 18.20,12 17.85,12 H 13.30 C 12.58,12 12,12.58 12,13.30 V 17.85 c 0,.35 .14,.68 .38,.92 l 5.84,5.85 c .23,.23 .55,.37 .91,.37 .35,0 .68,-0.14 .91,-0.38 L 24.61,20.06 C 24.85,19.83 25,19.50 25,19.15 25,18.79 24.85,18.46 24.61,18.22 z M 14.27,15.25 c -0.53,0 -0.97,-0.43 -0.97,-0.97 0,-0.53 .43,-0.97 .97,-0.97 .53,0 .97,.43 .97,.97 0,.53 -0.43,.97 -0.97,.97 z"
                  fill="#000"
                  fill-opacity="0.15"
                ></path>
              </svg>
            </div>
            <div class="ytp-cards-button-title">Shopping</div></span
          >
        </button>
        <div class="ytp-cards-teaser" style="display: none">
          <div class="ytp-cards-teaser-box"></div>
          <div class="ytp-cards-teaser-text">
            <span class="ytp-cards-teaser-label"></span>
          </div>
        </div>
        <button
          class="ytp-button ytp-overflow-button"
          title=""
          data-tooltip-title="More"
          aria-haspopup="true"
          aria-owns="ytp-id-29"
          aria-label="More"
        >
          <div class="ytp-overflow-icon">
            <svg height="100%" viewBox="-5 -5 36 36" width="100%">
              <path
                d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
                fill="#fff"
              ></path>
            </svg>
          </div>
        </button>
      </div>
    </div>
    <button
      class="ytp-unmute ytp-popup ytp-button ytp-unmute-animated ytp-unmute-shrink"
      data-layer="2"
      style="display: none"
    >
      <div class="ytp-unmute-inner">
        <div class="ytp-unmute-icon">
          <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
            <use class="ytp-svg-shadow" xlink:href="#ytp-id-2"></use>
            <path
              class="ytp-svg-fill"
              d="m 21.48,17.98 c 0,-1.77 -1.02,-3.29 -2.5,-4.03 v 2.21 l 2.45,2.45 c .03,-0.2 .05,-0.41 .05,-0.63 z m 2.5,0 c 0,.94 -0.2,1.82 -0.54,2.64 l 1.51,1.51 c .66,-1.24 1.03,-2.65 1.03,-4.15 0,-4.28 -2.99,-7.86 -7,-8.76 v 2.05 c 2.89,.86 5,3.54 5,6.71 z M 9.25,8.98 l -1.27,1.26 4.72,4.73 H 7.98 v 6 H 11.98 l 5,5 v -6.73 l 4.25,4.25 c -0.67,.52 -1.42,.93 -2.25,1.18 v 2.06 c 1.38,-0.31 2.63,-0.95 3.69,-1.81 l 2.04,2.05 1.27,-1.27 -9,-9 -7.72,-7.72 z m 7.72,.99 -2.09,2.08 2.09,2.09 V 9.98 z"
              id="ytp-id-2"
            ></path>
          </svg>
        </div>
        <div class="ytp-unmute-text">Tap to unmute</div>
        <div class="ytp-unmute-box"></div>
      </div>
    </button>
    <div class="ytp-gated-actions-overlay" data-layer="4" style="display: none">
      <div class="ytp-gated-actions-overlay-background">
        <div class="ytp-gated-actions-overlay-background-overlay"></div>
      </div>
      <button
        class="ytp-gated-actions-overlay-miniplayer-close-button ytp-button"
        aria-label="Close"
      >
        <svg height="100%" viewBox="0 0 24 24" width="100%">
          <path
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            fill="#fff"
          ></path>
        </svg>
      </button>
      <div class="ytp-gated-actions-overlay-bar">
        <div class="ytp-gated-actions-overlay-text-container">
          <div class="ytp-gated-actions-overlay-title"></div>
          <div class="ytp-gated-actions-overlay-subtitle"></div>
        </div>
        <div class="ytp-gated-actions-overlay-button-container"></div>
      </div>
    </div>
    <div
      class="ytp-overlay ytp-speedmaster-overlay"
      data-layer="4"
      style="display: none"
    >
      <div class="ytp-speedmaster-user-edu ytp-speedmaster-has-icon">
        <div class="ytp-speedmaster-label">2x</div>
        <div class="ytp-speedmaster-icon">
          <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
            <use class="ytp-svg-shadow" xlink:href="#ytp-id-1"></use>
            <path
              class="ytp-svg-fill"
              d="M 10,24 18.5,18 10,12 V 24 z M 19,12 V 24 L 27.5,18 19,12 z"
              id="ytp-id-1"
            ></path>
          </svg>
        </div>
      </div>
    </div>
    <div class="ytp-suggested-action" data-layer="4">
      <div
        class="ytp-button ytp-suggested-action-badge ytp-suggested-action ytp-suggested-action-badge-with-controls ytp-suggested-action-badge-expanded"
        style="display: none"
      >
        <div class="ytp-suggested-action-badge-icon-container"></div>
        <div
          class="ytp-suggested-action-badge-expanded-content-container"
          style=""
        >
          <label class="ytp-suggested-action-badge-title"></label
          ><a class="ytp-suggested-action-container"
            ><img
              class="ytp-suggested-action-badge-img"
              style="display: none" />
            <div
              class="ytp-suggested-action-badge-icon"
              style="display: none"
            ></div>
            <div class="ytp-suggested-action-details">
              <text class="ytp-suggested-action-title"></text
              ><text class="ytp-suggested-action-subtitle"></text
              ><text class="ytp-suggested-action-metadata-text"></text>
            </div>
            <button
              class="ytp-suggested-action-badge-dismiss-button-icon ytp-button"
            ></button
            ><button
              class="ytp-featured-product-overflow-icon ytp-button"
              aria-haspopup="true"
              style="display: none"
            ></button
          ></a>
        </div>
      </div>
    </div>
    <div
      class="ytp-player-content ytp-timely-actions-content"
      data-layer="4"
      style=""
    >
      <div class="ytp-timely-actions-overlay">
        <ytw-timely-actions-overlay-view-model
          class="ytwTimelyActionsOverlayViewModelHost"
        ></ytw-timely-actions-overlay-view-model>
      </div>
    </div>
    <div
      class="ytp-cued-thumbnail-overlay"
      data-layer="4"
      style="display: none"
    >
      <div class="ytp-cued-thumbnail-overlay-image"></div>
      <button
        class="ytp-large-play-button ytp-button"
        aria-label="Play"
        title="Play"
      >
        <svg height="100%" version="1.1" viewBox="0 0 68 48" width="100%">
          <path
            class="ytp-large-play-button-bg"
            d="M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z"
            fill="#f03"
          ></path>
          <path d="M 45,24 27,14 27,34" fill="#fff"></path>
        </svg>
      </button>
    </div>
    <div class="ytp-spinner" data-layer="4" style="display: none">
      <div class="ytp-spinner-container">
        <div class="ytp-spinner-rotator">
          <div class="ytp-spinner-left">
            <div class="ytp-spinner-circle"></div>
          </div>
          <div class="ytp-spinner-right">
            <div class="ytp-spinner-circle"></div>
          </div>
        </div>
      </div>
      <div class="ytp-spinner-message" style="display: none">
        If playback doesn't begin shortly, try restarting your device.
      </div>
    </div>
    <div
      class="ytp-paid-content-overlay"
      aria-live="assertive"
      aria-atomic="true"
      data-overlay-order="5"
      data-layer="4"
    >
      <a
        class="ytp-paid-content-overlay-link"
        target="_blank"
        style="display: none"
        ><div class="ytp-paid-content-overlay-indicator"></div>
        <div class="ytp-paid-content-overlay-icon"></div>
        <div class="ytp-paid-content-overlay-text"></div>
        <div class="ytp-paid-content-overlay-chevron"></div
      ></a>
    </div>
    <div
      class="ytp-storyboard-framepreview"
      data-layer="4"
      style="display: none"
    >
      <div class="ytp-storyboard-framepreview-timestamp"></div>
      <div class="ytp-storyboard-framepreview-img"></div>
    </div>
    <div data-layer="4" style="display: none" class="ytp-bezel-text-hide">
      <div class="ytp-bezel-text-wrapper">
        <div class="ytp-bezel-text"></div>
      </div>
      <div class="ytp-bezel" role="status" aria-label="Pause">
        <div class="ytp-bezel-icon">
          <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
            <use class="ytp-svg-shadow" xlink:href="#ytp-id-41"></use>
            <path
              class="ytp-svg-fill"
              d="M 12,26 16,26 16,10 12,10 z M 21,26 25,26 25,10 21,10 z"
              id="ytp-id-41"
            ></path>
          </svg>
        </div>
      </div>
    </div>
    <div class="ytp-doubletap-ui-legacy" data-layer="4" style="display: none">
      <div class="ytp-doubletap-fast-forward-ve"></div>
      <div class="ytp-doubletap-rewind-ve"></div>
      <div class="ytp-doubletap-static-circle">
        <div class="ytp-doubletap-ripple"></div>
      </div>
      <div class="ytp-doubletap-overlay-a11y"></div>
      <div class="ytp-doubletap-seek-info-container">
        <div class="ytp-doubletap-arrows-container">
          <span class="ytp-doubletap-base-arrow"></span
          ><span class="ytp-doubletap-base-arrow"></span
          ><span class="ytp-doubletap-base-arrow"></span>
        </div>
        <div class="ytp-doubletap-tooltip">
          <div class="ytp-seek-icon-text-container">
            <div class="ytp-seek-icon" style="display: none"></div>
            <div class="ytp-chapter-seek-text-legacy"></div>
          </div>
          <div class="ytp-doubletap-tooltip-label"></div>
        </div>
      </div>
    </div>
    <div data-layer="4" aria-live="polite" style="display: none">
      <div class="ytp-tooltip-text-wrapper">
        <div class="ytp-tooltip-edu">
          <svg height="100%" viewBox="0 0 36 36" width="100%">
            <path
              d="M14.1 36.75 12 34.65 24 22.65 36 34.65 33.9 36.75 24 26.85ZM14.1 24.1 12 22 24 10 36 22 33.9 24.1 24 14.2Z"
            ></path></svg
          ><span></span>
        </div>
        <div class="ytp-tooltip-image"></div>
        <div class="ytp-tooltip-title">
          <span></span>
          <div class="ytp-tooltip-keyboard-shortcut"></div>
        </div>
        <div class="ytp-tooltip-bottom-text">
          <span class="ytp-tooltip-text"></span>
          <div class="ytp-tooltip-keyboard-shortcut"></div>
        </div>
        <div class="ytp-tooltip-progress-bar-pill">
          <div class="ytp-tooltip-progress-bar-pill-time-stamp"></div>
          <div class="ytp-tooltip-progress-bar-pill-title"></div>
        </div>
      </div>
      <div class="ytp-tooltip-bg"><div class="ytp-tooltip-duration"></div></div>
    </div>
    <div class="ytp-suggested-action" data-overlay-order="9" data-layer="4">
      <div
        class="ytp-button ytp-suggested-action-badge ytp-featured-product ytp-suggested-action-badge-with-controls"
        style="display: none"
      >
        <div class="ytp-suggested-action-badge-icon-container"></div>
        <div
          class="ytp-suggested-action-badge-expanded-content-container"
          style="display: none"
        >
          <label class="ytp-suggested-action-badge-title"></label
          ><button
            class="ytp-suggested-action-badge-dismiss-button-icon ytp-button"
            style="display: none"
          ></button
          ><a class="ytp-featured-product-container"
            ><div class="ytp-featured-product-thumbnail">
              <img />
              <div class="ytp-featured-product-open-in-new"></div>
            </div>
            <div class="ytp-featured-product-details">
              <text class="ytp-featured-product-title"></text>
              <div class="ytp-featured-product-price-container">
                <text
                  class="ytp-featured-product-price-when-promotion-text-enabled"
                  aria-hidden="true"
                ></text
                ><text
                  class="ytp-featured-product-promotion-text"
                  aria-hidden="true"
                ></text>
              </div>
              <div class="ytp-featured-product-when-promotion-text-enabled">
                <text
                  class="ytp-featured-product-affiliate-disclaimer-when-promotion-text-enabled"
                ></text>
                <div
                  class="ytp-featured-product-trending"
                  style="display: none"
                >
                  <div class="ytp-featured-product-trending-icon"></div>
                  <text class="ytp-featured-product-trending-text"></text>
                </div>
                <text
                  class="ytp-featured-product-vendor-when-promotion-text-enabled"
                ></text>
              </div>
            </div>
            <button
              class="ytp-featured-product-overflow-icon ytp-button"
              aria-haspopup="true"
              style=""
            ></button
          ></a>
        </div>
      </div>
    </div>
    <div class="ytp-suggested-action" data-overlay-order="8" data-layer="4">
      <button
        class="ytp-button ytp-suggested-action-badge ytp-suggested-action-badge-with-controls ytp-suggested-action-badge-expanded"
        style="display: none"
      >
        <div class="ytp-suggested-action-badge-icon-container">
          <div class="ytp-suggested-action-badge-icon"></div>
        </div>
        <div
          class="ytp-suggested-action-badge-expanded-content-container"
          style=""
        >
          <label class="ytp-suggested-action-badge-title"></label
          ><button
            class="ytp-suggested-action-badge-dismiss-button-icon ytp-button"
          ></button>
        </div>
      </button>
    </div>
    <div class="ytp-suggested-action" data-overlay-order="7" data-layer="4">
      <button
        class="ytp-button ytp-suggested-action-badge ytp-suggested-action-badge-with-controls ytp-suggested-action-badge-expanded"
        style="display: none"
      >
        <div class="ytp-suggested-action-badge-icon-container"></div>
        <div
          class="ytp-suggested-action-badge-expanded-content-container"
          style=""
        >
          <label class="ytp-suggested-action-badge-title"></label
          ><button
            class="ytp-suggested-action-badge-dismiss-button-icon ytp-button"
          ></button>
        </div>
      </button>
    </div>
    <div
      class="ytp-player-content ytp-iv-player-content"
      data-layer="4"
      style="display: none"
    >
      <div class="ytp-free-preview-countdown-timer">
        <span></span
        ><span class="ytp-free-preview-countdown-timer-separator">•</span
        ><span></span>
      </div>
    </div>
    <div class="ytp-remote" data-layer="4" style="display: none">
      <div class="ytp-remote-display-status">
        <div class="ytp-remote-display-status-icon">
          <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
            <use class="ytp-svg-shadow" xlink:href="#ytp-id-32"></use>
            <path
              d="M7,24 L7,27 L10,27 C10,25.34 8.66,24 7,24 L7,24 Z M7,20 L7,22 C9.76,22 12,24.24 12,27 L14,27 C14,23.13 10.87,20 7,20 L7,20 Z M25,13 L11,13 L11,14.63 C14.96,15.91 18.09,19.04 19.37,23 L25,23 L25,13 L25,13 Z M7,16 L7,18 C11.97,18 16,22.03 16,27 L18,27 C18,20.92 13.07,16 7,16 L7,16 Z M27,9 L9,9 C7.9,9 7,9.9 7,11 L7,14 L9,14 L9,11 L27,11 L27,25 L20,25 L20,27 L27,27 C28.1,27 29,26.1 29,25 L29,11 C29,9.9 28.1,9 27,9 L27,9 Z"
              fill="#fff"
              id="ytp-id-32"
            ></path>
          </svg>
        </div>
        <div class="ytp-remote-display-status-text"></div>
      </div>
    </div>
    <div
      class="ytp-mdx-popup-dialog"
      role="dialog"
      data-layer="4"
      style="display: none"
    >
      <div class="ytp-mdx-popup-dialog-inner-content">
        <div class="ytp-mdx-popup-title">You're signed out</div>
        <div class="ytp-mdx-popup-description">
          Videos you watch may be added to the TV's watch history and influence
          TV recommendations. To avoid this, cancel and sign in to YouTube on
          your computer.
        </div>
        <div class="ytp-mdx-privacy-popup-buttons">
          <button class="ytp-button ytp-mdx-privacy-popup-cancel">Cancel</button
          ><button class="ytp-button ytp-mdx-privacy-popup-confirm">
            Confirm
          </button>
        </div>
      </div>
    </div>
    <div class="ytp-miniplayer-ui" data-layer="4" style="display: none"></div>
    <div
      class="ytp-caption-window-container"
      id="ytp-caption-window-container"
      data-layer="4"
    ></div>
    <div
      class="ytp-autonav-endscreen-countdown-overlay"
      data-layer="4"
      style="display: none"
    >
      <div
        class="ytp-autonav-endscreen-countdown-container ytp-autonav-endscreen-upnext-no-alternative-header"
      >
        <div class="ytp-autonav-endscreen-upnext-container">
          <div class="ytp-autonav-endscreen-upnext-header">Up next</div>
          <div class="ytp-autonav-endscreen-upnext-alternative-header"></div>
          <a class="ytp-autonav-endscreen-link-container" target=""
            ><div class="ytp-autonav-endscreen-upnext-thumbnail" style="">
              <div class="ytp-autonav-timestamp"></div>
              <div class="ytp-autonav-live-stamp">Live</div>
              <div class="ytp-autonav-upcoming-stamp">Upcoming</div>
            </div>
            <div class="ytp-autonav-endscreen-video-info">
              <div class="ytp-autonav-endscreen-premium-badge"></div>
              <div class="ytp-autonav-endscreen-upnext-title"></div>
              <div class="ytp-autonav-endscreen-upnext-author"></div>
              <div class="ytp-autonav-view-and-date"></div>
              <div class="ytp-autonav-author-and-view"></div></div
          ></a>
        </div>
        <div class="ytp-autonav-overlay" style="width: 1128px"></div>
        <div
          class="ytp-autonav-endscreen-button-container"
          style="display: none"
        >
          <button
            class="ytp-autonav-endscreen-upnext-button ytp-autonav-endscreen-upnext-cancel-button ytp-autonav-endscreen-upnext-button-rounded"
            aria-label="Cancel autoplay"
          >
            Cancel</button
          ><a
            class="ytp-autonav-endscreen-upnext-button ytp-autonav-endscreen-upnext-play-button ytp-autonav-endscreen-upnext-button-rounded"
            role="button"
            aria-label="Play next video"
            >Play Now</a
          >
        </div>
      </div>
    </div>
    <div
      class="html5-endscreen ytp-player-content videowall-endscreen"
      data-layer="4"
      style="display: none"
    >
      <button class="ytp-button ytp-endscreen-previous" aria-label="Previous">
        <svg height="100%" version="1.1" viewBox="0 0 32 32" width="100%">
          <path
            d="M 19.41,20.09 14.83,15.5 19.41,10.91 18,9.5 l -6,6 6,6 z"
            fill="#fff"
          ></path>
        </svg>
      </button>
      <div class="ytp-endscreen-content"></div>
      <button class="ytp-button ytp-endscreen-next" aria-label="Next">
        <svg height="100%" version="1.1" viewBox="0 0 32 32" width="100%">
          <path
            d="m 12.59,20.34 4.58,-4.59 -4.58,-4.59 1.41,-1.41 6,6 -6,6 z"
            fill="#fff"
          ></path>
        </svg>
      </button>
    </div>
    <div
      class="ytp-playlist-menu"
      role="dialog"
      id="ytp-id-22"
      data-layer="5"
      style="display: none"
    >
      <div class="ytp-playlist-menu-header">
        <div class="ytp-playlist-menu-title">
          <a class="ytp-playlist-menu-title-name"></a
          ><button
            class="ytp-playlist-menu-close ytp-button"
            aria-label="Close"
          >
            <svg height="100%" viewBox="0 0 24 24" width="100%">
              <path
                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                fill="#fff"
              ></path>
            </svg>
          </button>
        </div>
        <div class="ytp-playlist-menu-subtitle"></div>
      </div>
      <div class="ytp-playlist-menu-items" role="menu"></div>
    </div>
    <div
      class="ytp-share-panel"
      id="ytp-id-25"
      role="dialog"
      aria-labelledby="ytp-id-24"
      data-layer="5"
      style="display: none"
    >
      <div class="ytp-share-panel-inner-content">
        <div class="ytp-share-panel-title" id="ytp-id-24">Share</div>
        <a
          class="ytp-share-panel-link ytp-no-contextmenu"
          target="_blank"
          title="Share link"
        ></a
        ><label class="ytp-share-panel-include-playlist"
          ><input
            class="ytp-share-panel-include-playlist-checkbox"
            type="checkbox"
            checked="true"
          />Include playlist</label
        >
        <div class="ytp-share-panel-loading-spinner">
          <div class="ytp-spinner-container">
            <div class="ytp-spinner-rotator">
              <div class="ytp-spinner-left">
                <div class="ytp-spinner-circle"></div>
              </div>
              <div class="ytp-spinner-right">
                <div class="ytp-spinner-circle"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="ytp-share-panel-service-buttons"></div>
        <div class="ytp-share-panel-error">
          An error occurred while retrieving sharing information. Please try
          again later.
        </div>
      </div>
      <button class="ytp-share-panel-close ytp-button" title="Close">
        <svg height="100%" viewBox="0 0 24 24" width="100%">
          <path
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            fill="#fff"
          ></path>
        </svg>
      </button>
    </div>
    <div
      class="ytp-overflow-panel"
      id="ytp-id-29"
      role="dialog"
      data-layer="5"
      style="display: none"
    >
      <div class="ytp-overflow-panel-content">
        <div class="ytp-overflow-panel-action-buttons"></div>
      </div>
      <button
        class="ytp-overflow-panel-close ytp-button"
        data-tooltip-title="Close"
        title=""
        aria-label="Close"
      >
        <svg height="100%" viewBox="0 0 24 24" width="100%">
          <path
            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            fill="#fff"
          ></path>
        </svg>
      </button>
    </div>
    <div
      class="ytp-popup ytp-settings-menu"
      data-layer="6"
      id="ytp-id-18"
      style="display: none"
    >
      <div class="ytp-panel">
        <div class="ytp-panel-menu" role="menu"></div>
      </div>
    </div>
    <div
      class="ytp-gradient-bottom"
      data-layer="9"
      style="
        height: 146px;
        background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAACSCAYAAACE56BkAAAAAXNSR0IArs4c6QAAAPVJREFUKFNlyOlHGAAcxvHuY93H1n1fW1v3fbej+zAmI5PIRGYiM5JEEkkiiSSRRPoj83nze9Pz4uPrSUh4tURPEpKDFJWKtCBdZSAzeKOykB3kqFzkBfmqAIVBkSrGW7wLSlQpyoJyVYHKoEpVoyaoVXWoDxpUI5qCZtWC98EH1YqPwSfVhvagQ3WiK+hWPegN+lQ/BoJBNYRhjASjagzjwYSaxOfgi/qKb8GUmsZMMKvmMB8sqEUsYRnf8QMr+IlV/MIa1rGB39jEFv7gL7axg3/4j13sYR8HOMQRjnGCU5zhHBe4xBWucYNb3OEeD3jEE55fAOe7I9q0+rDDAAAAAElFTkSuQmCC');
      "
    ></div>
    <div
      class="ytp-chrome-bottom"
      data-layer="9"
      style="width: 616px; left: 12px"
    >
      <div class="ytp-progress-bar-container">
        <div class="ytp-heat-map-container">
          <div class="ytp-heat-map-edu"></div>
        </div>
        <div
          class="ytp-progress-bar"
          tabindex="0"
          role="slider"
          aria-label="Seek slider"
          draggable="true"
          aria-valuemin="0"
          aria-valuemax="8260"
          aria-valuenow="37"
          aria-valuetext="0 Minutes 37 Seconds of 2 Hours 17 Minutes 40 Seconds"
          style="touch-action: none"
        >
          <div class="ytp-chapters-container">
            <div class="ytp-chapter-hover-container" style="width: 616px">
              <div class="ytp-progress-bar-padding"></div>
              <div class="ytp-progress-list">
                <div
                  class="ytp-play-progress ytp-swatch-background-color"
                  style="
                    left: 0px;
                    transform: scaleX(0.00450032);
                    background-size: 616px;
                    background-position-x: 0px;
                  "
                ></div>
                <div class="ytp-progress-linear-live-buffer"></div>
                <div
                  class="ytp-load-progress"
                  style="left: 0px; transform: scaleX(0.0109434)"
                ></div>
                <div
                  class="ytp-hover-progress"
                  style="left: 2.7722px; transform: scaleX(0)"
                ></div>
                <div class="ytp-ad-progress-list"></div>
              </div>
            </div>
          </div>
          <div class="ytp-timed-markers-container"></div>
          <div class="ytp-clip-start-exclude" style="width: 0%"></div>
          <div class="ytp-clip-end-exclude" style="left: 100%; width: 0%"></div>
          <div
            class="ytp-scrubber-container"
            style="transform: translateX(2.7722px)"
          >
            <div class="ytp-scrubber-button ytp-swatch-background-color">
              <div class="ytp-scrubber-pull-indicator"></div>
              <img class="ytp-decorated-scrubber-button" />
            </div>
          </div>
        </div>
        <div class="ytp-fine-scrubbing-container">
          <div class="ytp-fine-scrubbing-edu"></div>
          <div class="ytp-fine-scrubbing">
            <div
              class="ytp-fine-scrubbing-draggable"
              draggable="true"
              style="
                touch-action: none;
                padding: 0px 308px;
                transform: translateX(-160px);
              "
            >
              <div
                class="ytp-fine-scrubbing-thumbnails"
                tabindex="0"
                role="slider"
                type="range"
                aria-label="Click or scroll the panel for the precise seeking."
                aria-valuemin="0"
                aria-valuemax="8261"
                aria-valuenow="0"
                aria-valuetext="Seek to 0 Minutes 0 Seconds"
                style="position: relative"
              ></div>
            </div>
            <div class="ytp-fine-scrubbing-cursor" aria-hidden="true"></div>
            <div class="ytp-fine-scrubbing-seek-time" aria-hidden="true">
              0:37
            </div>
            <div
              class="ytp-fine-scrubbing-play"
              title="Play from this position"
              role="button"
            >
              <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
                <use class="ytp-svg-shadow" xlink:href="#ytp-id-6"></use>
                <path
                  class="ytp-svg-fill"
                  d="M 12,26 18.5,22 18.5,14 12,10 z M 18.5,22 25,18 25,18 18.5,14 z"
                  id="ytp-id-6"
                ></path>
              </svg>
            </div>
            <div
              class="ytp-fine-scrubbing-dismiss"
              title="Exit precise seeking"
              role="button"
            >
              <svg height="100%" viewBox="0 0 24 24" width="100%">
                <path
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                  fill="#fff"
                ></path>
              </svg>
            </div>
          </div>
        </div>
        <div class="ytp-bound-time-left"></div>
        <div class="ytp-bound-time-right"></div>
        <div
          class="ytp-clip-start"
          draggable="true"
          title="Watch full video"
          style="touch-action: none; left: 0%"
        >
          <svg height="100%" version="1.1" viewBox="0 0 14 14" width="100%">
            <use class="ytp-svg-shadow" xlink:href="#ytp-id-4"></use>
            <path
              d="M12,14 L9,11 L9,3 L12,0 L5,0 L5,14 L12,14 Z"
              fill="#eaeaea"
              id="ytp-id-4"
            ></path>
          </svg>
        </div>
        <div
          class="ytp-clip-end"
          draggable="true"
          title="Watch full video"
          style="touch-action: none; left: 100%"
        >
          <svg height="100%" version="1.1" viewBox="0 0 14 14" width="100%">
            <use class="ytp-svg-shadow" xlink:href="#ytp-id-5"></use>
            <path
              d="M2,14 L5,11 L5,3 L2,0 L9,0 L9,14 L2,14 L2,14 Z"
              fill="#eaeaea"
              id="ytp-id-5"
            ></path>
          </svg>
        </div>
      </div>
      <div class="ytp-chrome-controls">
        <div class="ytp-left-controls">
          <a
            class="ytp-prev-button ytp-button"
            role="button"
            aria-disabled="true"
            style="display: none"
            ><svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-11"></use>
              <path
                class="ytp-svg-fill"
                d="m 12,12 h 2 v 12 h -2 z m 3.5,6 8.5,6 V 12 z"
                id="ytp-id-11"
              ></path></svg></a
          ><button
            class="ytp-play-button ytp-button"
            title=""
            aria-keyshortcuts="k"
            data-title-no-tooltip="Play"
            data-tooltip-title="Play (k)"
            aria-label="Pause (k)"
          >
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-42"></use>
              <path
                class="ytp-svg-fill"
                d="M 12,26 18.5,22 18.5,14 12,10 z M 18.5,22 25,18 25,18 18.5,14 z"
                id="ytp-id-42"
              ></path>
            </svg></button
          ><a
            class="ytp-next-button ytp-button"
            role="button"
            title="Next (SHIFT+n)"
            data-tooltip-title="Next (SHIFT+n)"
            data-title-no-tooltip="Next"
            aria-keyshortcuts="SHIFT+n"
            aria-disabled="false"
            aria-label="Next (SHIFT+n)"
            data-duration="20:53"
            data-preview="https://i.ytimg.com/vi/VwOfvE_3Mtk/hqdefault.jpg?sqp=-oaymwEnCNACELwBSFryq4qpAxkIARUAAIhCGAHYAQHiAQoIGBACGAY4AUAB&amp;rs=AOn4CLCmIIeSuvgR1GlwedXDCmO6KauKLQ"
            data-tooltip-text="Is Google’s Gemini CLI Ready for Dev Work?"
            href="https://www.youtube.com/watch?v=VwOfvE_3Mtk"
            ><svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-13"></use>
              <path
                class="ytp-svg-fill"
                d="M 12,24 20.5,18 12,12 V 24 z M 22,12 v 12 h 2 V 12 h -2 z"
                id="ytp-id-13"
              ></path></svg></a
          ><span class="ytp-volume-area"
            ><button
              class="ytp-mute-button ytp-button"
              aria-keyshortcuts="m"
              title=""
              data-tooltip-offset-y="0"
              data-tooltip-title="Mute (m)"
              aria-label="Mute (m)"
              data-title-no-tooltip="Mute"
            >
              <div class="ytp-volume-icon">
                <svg
                  height="100%"
                  version="1.1"
                  viewBox="0 0 36 36"
                  width="100%"
                >
                  <use class="ytp-svg-shadow" xlink:href="#ytp-id-15"></use>
                  <use class="ytp-svg-shadow" xlink:href="#ytp-id-16"></use>
                  <defs>
                    <clipPath id="ytp-svg-volume-animation-mask">
                      <path
                        d="m 14.35,-0.14 -5.86,5.86 20.73,20.78 5.86,-5.91 z"
                      ></path>
                      <path
                        d="M 7.07,6.87 -1.11,15.33 19.61,36.11 27.80,27.60 z"
                      ></path>
                      <path
                        class="ytp-svg-volume-animation-mover"
                        d="M 9.09,5.20 6.47,7.88 26.82,28.77 29.66,25.99 z"
                        transform="translate(0, 0)"
                      ></path>
                    </clipPath>
                    <clipPath id="ytp-svg-volume-animation-slash-mask">
                      <path
                        class="ytp-svg-volume-animation-mover"
                        d="m -11.45,-15.55 -4.44,4.51 20.45,20.94 4.55,-4.66 z"
                        transform="translate(0, 0)"
                      ></path>
                    </clipPath>
                  </defs>
                  <path
                    class="ytp-svg-fill ytp-svg-volume-animation-speaker"
                    clip-path="url(#ytp-svg-volume-animation-mask)"
                    d="M8,21 L12,21 L17,26 L17,10 L12,15 L8,15 L8,21 Z M19,14 L19,22 C20.48,21.32 21.5,19.77 21.5,18 C21.5,16.26 20.48,14.74 19,14 ZM19,11.29 C21.89,12.15 24,14.83 24,18 C24,21.17 21.89,23.85 19,24.71 L19,26.77 C23.01,25.86 26,22.28 26,18 C26,13.72 23.01,10.14 19,9.23 L19,11.29 Z"
                    fill="#fff"
                    id="ytp-id-15"
                  ></path>
                  <path
                    class="ytp-svg-fill ytp-svg-volume-animation-hider"
                    clip-path="url(#ytp-svg-volume-animation-slash-mask)"
                    d="M 9.25,9 7.98,10.27 24.71,27 l 1.27,-1.27 Z"
                    fill="#fff"
                    id="ytp-id-16"
                    style="display: none"
                  ></path>
                </svg>
              </div>
            </button>
            <div
              class="ytp-volume-panel"
              title=""
              data-tooltip-title="Volume"
              role="slider"
              aria-valuemin="0"
              aria-valuemax="100"
              tabindex="0"
              aria-valuenow="100"
              aria-valuetext="100% volume"
              aria-label="Volume"
            >
              <div
                class="ytp-volume-slider"
                draggable="true"
                style="touch-action: none"
              >
                <div class="ytp-volume-slider-handle" style="left: 40px"></div>
              </div></div
          ></span>
          <div class="ytp-time-display notranslate" tabindex="0">
            <span class="ytp-time-wrapper"
              ><div class="ytp-time-contents">
                <span class="ytp-time-clip-icon" aria-label="Clip"
                  ><svg
                    height="100%"
                    version="1.1"
                    viewBox="0 0 24 24"
                    width="100%"
                  >
                    <path
                      d="M22,3h-4l-5,5l3,3l6-6V3L22,3z M10.79,7.79C10.91,7.38,11,6.95,11,6.5C11,4.01,8.99,2,6.5,2S2,4.01,2,6.5S4.01,11,6.5,11 c0.45,0,.88-0.09,1.29-0.21L9,12l-1.21,1.21C7.38,13.09,6.95,13,6.5,13C4.01,13,2,15.01,2,17.5S4.01,22,6.5,22s4.5-2.01,4.5-4.5 c0-0.45-0.09-0.88-0.21-1.29L12,15l6,6h4v-2L10.79,7.79z M6.5,8C5.67,8,5,7.33,5,6.5S5.67,5,6.5,5S8,5.67,8,6.5S7.33,8,6.5,8z M6.5,19C5.67,19,5,18.33,5,17.5S5.67,16,6.5,16S8,16.67,8,17.5S7.33,19,6.5,19z"
                    ></path></svg></span
                ><span class="ytp-time-current">0:37</span
                ><span class="ytp-time-separator"> / </span
                ><span class="ytp-time-duration">2:17:40</span>
              </div></span
            ><span class="ytp-clip-watch-full-video-button-separator">•</span
            ><span class="ytp-clip-watch-full-video-button"
              >Watch full video</span
            ><button class="ytp-live-badge ytp-button">Live</button>
          </div>
          <div class="ytp-chapter-container" style="display: none">
            <button
              class="ytp-chapter-title ytp-button ytp-chapter-container-disabled"
              disabled=""
            >
              <span class="ytp-chapter-title-prefix" aria-hidden="true">•</span>
              <div
                class="ytp-chapter-title-content"
                aria-live="polite"
                title=""
                data-tooltip-title="View chapter"
                aria-label=""
              ></div>
              <div class="ytp-chapter-title-chevron">
                <svg height="100%" viewBox="0 0 24 24" width="100%">
                  <path
                    d="M9.71 18.71l-1.42-1.42 5.3-5.29-5.3-5.29 1.42-1.42 6.7 6.71z"
                    fill="#fff"
                  ></path>
                </svg>
              </div>
            </button>
          </div>
          <div class="ytp-chapter-container" style="display: none">
            <button
              class="ytp-chapter-title ytp-button ytp-chapter-container-disabled"
              disabled=""
            >
              <span class="ytp-chapter-title-prefix" aria-hidden="true">•</span>
              <div
                class="ytp-chapter-title-content"
                aria-live="polite"
                title=""
                data-tooltip-title="View key moments"
                aria-label="View key moments"
              ></div>
              <div class="ytp-chapter-title-chevron">
                <svg height="100%" viewBox="0 0 24 24" width="100%">
                  <path
                    d="M9.71 18.71l-1.42-1.42 5.3-5.29-5.3-5.29 1.42-1.42 6.7 6.71z"
                    fill="#fff"
                  ></path>
                </svg>
              </div>
            </button>
          </div>
        </div>
        <div class="ytp-right-controls">
          <button
            class="ytp-fullerscreen-edu-button ytp-button"
            data-priority="2"
            style="display: none"
          >
            <div class="ytp-fullerscreen-edu-text">Scroll for details</div>
            <div class="ytp-fullerscreen-edu-chevron">
              <svg height="100%" viewBox="0 0 24 24" width="100%">
                <path
                  d="M7.41,8.59L12,13.17l4.59-4.58L18,10l-6,6l-6-6L7.41,8.59z"
                  fill="#fff"
                ></path>
              </svg>
            </div></button
          ><button
            class="ytp-button ytp-autonav-toggle"
            title=""
            data-priority="3"
            data-tooltip-target-id="ytp-autonav-toggle-button"
            style=""
            data-tooltip-title="Autoplay is on"
            aria-label="Autoplay is on"
          >
            <div class="ytp-autonav-toggle-button-container">
              <div class="ytp-autonav-toggle-button" aria-checked="true"></div>
            </div></button
          ><button
            class="ytp-subtitles-button ytp-button"
            aria-keyshortcuts="c"
            data-priority="5"
            title=""
            data-tooltip-title="Subtitles/closed captions (c)"
            data-title-no-tooltip="Subtitles/closed captions"
            aria-pressed="false"
            aria-label="Subtitles/closed captions unavailable"
          >
            <svg
              class="ytp-subtitles-button-icon"
              height="100%"
              version="1.1"
              viewBox="0 0 36 36"
              width="100%"
              fill-opacity="1"
            >
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-17"></use>
              <path
                d="M11,11 C9.89,11 9,11.9 9,13 L9,23 C9,24.1 9.89,25 11,25 L25,25 C26.1,25 27,24.1 27,23 L27,13 C27,11.9 26.1,11 25,11 L11,11 Z M17,17 L15.5,17 L15.5,16.5 L13.5,16.5 L13.5,19.5 L15.5,19.5 L15.5,19 L17,19 L17,20 C17,20.55 16.55,21 16,21 L13,21 C12.45,21 12,20.55 12,20 L12,16 C12,15.45 12.45,15 13,15 L16,15 C16.55,15 17,15.45 17,16 L17,17 L17,17 Z M24,17 L22.5,17 L22.5,16.5 L20.5,16.5 L20.5,19.5 L22.5,19.5 L22.5,19 L24,19 L24,20 C24,20.55 23.55,21 23,21 L20,21 C19.45,21 19,20.55 19,20 L19,16 C19,15.45 19.45,15 20,15 L23,15 C23.55,15 24,15.45 24,16 L24,17 L24,17 Z"
                fill="#fff"
                id="ytp-id-17"
              ></path>
            </svg></button
          ><button
            class="ytp-button ytp-settings-button"
            aria-expanded="false"
            aria-haspopup="true"
            aria-controls="ytp-id-18"
            title=""
            data-tooltip-title="Settings"
            data-tooltip-target-id="ytp-settings-button"
            aria-label="Settings"
          >
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-19"></use>
              <path
                d="m 23.94,18.78 c .03,-0.25 .05,-0.51 .05,-0.78 0,-0.27 -0.02,-0.52 -0.05,-0.78 l 1.68,-1.32 c .15,-0.12 .19,-0.33 .09,-0.51 l -1.6,-2.76 c -0.09,-0.17 -0.31,-0.24 -0.48,-0.17 l -1.99,.8 c -0.41,-0.32 -0.86,-0.58 -1.35,-0.78 l -0.30,-2.12 c -0.02,-0.19 -0.19,-0.33 -0.39,-0.33 l -3.2,0 c -0.2,0 -0.36,.14 -0.39,.33 l -0.30,2.12 c -0.48,.2 -0.93,.47 -1.35,.78 l -1.99,-0.8 c -0.18,-0.07 -0.39,0 -0.48,.17 l -1.6,2.76 c -0.10,.17 -0.05,.39 .09,.51 l 1.68,1.32 c -0.03,.25 -0.05,.52 -0.05,.78 0,.26 .02,.52 .05,.78 l -1.68,1.32 c -0.15,.12 -0.19,.33 -0.09,.51 l 1.6,2.76 c .09,.17 .31,.24 .48,.17 l 1.99,-0.8 c .41,.32 .86,.58 1.35,.78 l .30,2.12 c .02,.19 .19,.33 .39,.33 l 3.2,0 c .2,0 .36,-0.14 .39,-0.33 l .30,-2.12 c .48,-0.2 .93,-0.47 1.35,-0.78 l 1.99,.8 c .18,.07 .39,0 .48,-0.17 l 1.6,-2.76 c .09,-0.17 .05,-0.39 -0.09,-0.51 l -1.68,-1.32 0,0 z m -5.94,2.01 c -1.54,0 -2.8,-1.25 -2.8,-2.8 0,-1.54 1.25,-2.8 2.8,-2.8 1.54,0 2.8,1.25 2.8,2.8 0,1.54 -1.25,2.8 -2.8,2.8 l 0,0 z"
                fill="#fff"
                id="ytp-id-19"
              ></path>
            </svg></button
          ><button
            class="ytp-miniplayer-button ytp-button"
            title=""
            aria-keyshortcuts="i"
            data-priority="7"
            data-tooltip-target-id="ytp-miniplayer-button"
            data-tooltip-title="Miniplayer (i)"
            data-title-no-tooltip="Miniplayer"
            aria-label="Miniplayer (i)"
          >
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-20"></use>
              <path
                d="M25,17 L17,17 L17,23 L25,23 L25,17 L25,17 Z M29,25 L29,10.98 C29,9.88 28.1,9 27,9 L9,9 C7.9,9 7,9.88 7,10.98 L7,25 C7,26.1 7.9,27 9,27 L27,27 C28.1,27 29,26.1 29,25 L29,25 Z M27,25.02 L9,25.02 L9,10.97 L27,10.97 L27,25.02 L27,25.02 Z"
                fill="#fff"
                id="ytp-id-20"
              ></path>
            </svg></button
          ><button
            class="ytp-pip-button ytp-button"
            title=""
            data-priority="8"
            data-tooltip-target-id="ytp-pip-button"
            data-tooltip-title="Picture-in-picture"
            data-title-no-tooltip="Picture-in-picture"
            style="display: none"
          >
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-31"></use>
              <path
                d="M25,17 L17,17 L17,23 L25,23 L25,17 L25,17 Z M29,25 L29,10.98 C29,9.88 28.1,9 27,9 L9,9 C7.9,9 7,9.88 7,10.98 L7,25 C7,26.1 7.9,27 9,27 L27,27 C28.1,27 29,26.1 29,25 L29,25 Z M27,25.02 L9,25.02 L9,10.97 L27,10.97 L27,25.02 L27,25.02 Z"
                fill="#fff"
                id="ytp-id-31"
              ></path>
            </svg></button
          ><button
            class="ytp-size-button ytp-button"
            title=""
            aria-keyshortcuts="t"
            data-priority="9"
            data-tooltip-title="Theater mode (t)"
            data-title-no-tooltip="Theater mode"
            aria-label="Theater mode (t)"
          >
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-30"></use>
              <path
                d="m 28,11 0,14 -20,0 0,-14 z m -18,2 16,0 0,10 -16,0 0,-10 z"
                fill="#fff"
                fill-rule="evenodd"
                id="ytp-id-30"
              ></path>
            </svg></button
          ><button
            class="ytp-remote-button ytp-button"
            title=""
            data-tooltip-title="Play on TV"
            aria-haspopup="true"
            data-priority="10"
            aria-label="Play on TV"
            style="display: none"
          >
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <use class="ytp-svg-shadow" xlink:href="#ytp-id-21"></use>
              <path
                d="M27,9 L9,9 C7.9,9 7,9.9 7,11 L7,14 L9,14 L9,11 L27,11 L27,25 L20,25 L20,27 L27,27 C28.1,27 29,26.1 29,25 L29,11 C29,9.9 28.1,9 27,9 L27,9 Z M7,24 L7,27 L10,27 C10,25.34 8.66,24 7,24 L7,24 Z M7,20 L7,22 C9.76,22 12,24.24 12,27 L14,27 C14,23.13 10.87,20 7,20 L7,20 Z M7,16 L7,18 C11.97,18 16,22.03 16,27 L18,27 C18,20.92 13.07,16 7,16 L7,16 Z"
                fill="#fff"
                id="ytp-id-21"
              ></path>
            </svg></button
          ><button
            class="ytp-fullscreen-button ytp-button"
            title=""
            aria-keyshortcuts="f"
            data-priority="12"
            data-title-no-tooltip="Full screen"
            data-tooltip-title="Full screen (f)"
            aria-label="Full screen (f)"
          >
            <svg height="100%" version="1.1" viewBox="0 0 36 36" width="100%">
              <g class="ytp-fullscreen-button-corner-0">
                <use class="ytp-svg-shadow" xlink:href="#ytp-id-7"></use>
                <path
                  class="ytp-svg-fill"
                  d="m 10,16 2,0 0,-4 4,0 0,-2 L 10,10 l 0,6 0,0 z"
                  id="ytp-id-7"
                ></path>
              </g>
              <g class="ytp-fullscreen-button-corner-1">
                <use class="ytp-svg-shadow" xlink:href="#ytp-id-8"></use>
                <path
                  class="ytp-svg-fill"
                  d="m 20,10 0,2 4,0 0,4 2,0 L 26,10 l -6,0 0,0 z"
                  id="ytp-id-8"
                ></path>
              </g>
              <g class="ytp-fullscreen-button-corner-2">
                <use class="ytp-svg-shadow" xlink:href="#ytp-id-9"></use>
                <path
                  class="ytp-svg-fill"
                  d="m 24,24 -4,0 0,2 L 26,26 l 0,-6 -2,0 0,4 0,0 z"
                  id="ytp-id-9"
                ></path>
              </g>
              <g class="ytp-fullscreen-button-corner-3">
                <use class="ytp-svg-shadow" xlink:href="#ytp-id-10"></use>
                <path
                  class="ytp-svg-fill"
                  d="M 12,20 10,20 10,26 l 6,0 0,-2 -4,0 0,-4 0,0 z"
                  id="ytp-id-10"
                ></path>
              </g>
            </svg>
          </button>
          <div class="trancy-youtube-button" id="xt-toggle-button">
            <div class="trancy-button-container">
              <div class="trancy-button-logo">
                <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.23684" y="0.736842" width="18.5263" height="18.5263" rx="2.63158" stroke="url(#paint0_linear_85_597)" stroke-width="1.47368"/>
<path d="M11.7441 8.31592V12.3122C11.7441 12.7598 11.9238 13.189 12.2437 13.5055C12.5635 13.822 12.9973 13.9998 13.4497 13.9998H14.1319C14.5842 13.9998 15.018 13.822 15.3379 13.5055C15.6577 13.189 15.8374 12.7598 15.8374 12.3122V8.31592" stroke="url(#paint1_linear_85_597)" stroke-width="1.62008" stroke-linejoin="round"/>
<path d="M5.55273 5.47388V13.4739H10.1843" stroke="url(#paint2_linear_85_597)" stroke-width="1.89474" stroke-linejoin="round"/>
<defs>
<linearGradient id="paint0_linear_85_597" x1="-8.5" y1="-3.5" x2="-18.5" y2="12" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB700"/>
<stop offset="1" stop-color="#996E00"/>
</linearGradient>
<linearGradient id="paint1_linear_85_597" x1="13.7908" y1="8.31592" x2="13.7908" y2="13.9998" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB700"/>
<stop offset="1" stop-color="#996E00"/>
</linearGradient>
<linearGradient id="paint2_linear_85_597" x1="7.86852" y1="5.47388" x2="7.86852" y2="13.4739" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB700"/>
<stop offset="1" stop-color="#996E00"/>
</linearGradient>
</defs>
</svg>

              </div>
              <div class="trancy-panel-menu">
                <div class="trancy-panel-menu-container">
                  <div class="trancy-primary-panel-menu">
                    <div class="trancy-menuitem-group">
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M200-160q-33 0-56.5-23.5T120-240v-480q0-33 23.5-56.5T200-800h560q33 0 56.5 23.5T840-720v480q0 33-23.5 56.5T760-160H200Zm0-80h560v-480H200v480Zm80-120h120q17 0 28.5-11.5T440-400v-20q0-9-6-15t-15-6h-18q-9 0-15 6t-6 15h-80v-120h80q0 9 6 15t15 6h18q9 0 15-6t6-15v-20q0-17-11.5-28.5T400-600H280q-17 0-28.5 11.5T240-560v160q0 17 11.5 28.5T280-360Zm400-240H560q-17 0-28.5 11.5T520-560v160q0 17 11.5 28.5T560-360h120q17 0 28.5-11.5T720-400v-20q0-9-6-15t-15-6h-18q-9 0-15 6t-6 15h-80v-120h80q0 9 6 15t15 6h18q9 0 15-6t6-15v-20q0-17-11.5-28.5T680-600ZM200-240v-480 480Z"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">
                          Trancy 字幕
                          <div class="trancy-menuitem-label-info">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                            >
                              <path
                                d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11 7H13V9H11V7ZM11 11H13V17H11V11Z"
                              ></path>
                            </svg>
                            <div class="trancy-menuitem-label-info-tooltip">
                              <div class="trancy-menuitem-shrotcut">
                                推荐配置快捷键
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="trancy-menuitem-action">
                          <span
                            class="MuiSwitch-root MuiSwitch-sizeMedium css-ecvcn9"
                            ><span
                              class="MuiButtonBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary Mui-checked PrivateSwitchBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary Mui-checked Mui-checked css-1nr2wod"
                              ><input
                                class="PrivateSwitchBase-input MuiSwitch-input css-1m9pwf3"
                                type="checkbox"
                                checked="" /><span
                                class="MuiSwitch-thumb css-19gndve"
                              ></span
                              ><span
                                class="MuiTouchRipple-root css-w0pj6f"
                              ></span></span
                            ><span class="MuiSwitch-track css-1ju1kxc"></span
                          ></span>
                        </div>
                      </div>
                    </div>
                    <div class="trancy-menuitem-group">
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M20.4668 8.69379L20.7134 8.12811C21.1529 7.11947 21.9445 6.31641 22.9323 5.87708L23.6919 5.53922C24.1027 5.35653 24.1027 4.75881 23.6919 4.57612L22.9748 4.25714C21.9616 3.80651 21.1558 2.97373 20.7238 1.93083L20.4706 1.31953C20.2942 0.89349 19.7058 0.89349 19.5293 1.31953L19.2761 1.93083C18.8442 2.97373 18.0384 3.80651 17.0252 4.25714L16.308 4.57612C15.8973 4.75881 15.8973 5.35653 16.308 5.53922L17.0677 5.87708C18.0555 6.31641 18.8471 7.11947 19.2866 8.12811L19.5331 8.69379C19.7136 9.10792 20.2864 9.10792 20.4668 8.69379ZM3 3H14V5H4V19H20V11H22V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4C2 3.44772 2.44772 3 3 3ZM9 8C10.1045 8 11.1049 8.44841 11.829 9.173L10.4153 10.5866C10.0534 10.2241 9.55299 10 9 10C7.895 10 7 10.895 7 12C7 13.105 7.895 14 9 14C9.5525 14 10.0525 13.7762 10.4144 13.4144L11.828 14.828C11.104 15.552 10.104 16 9 16C6.792 16 5 14.208 5 12C5 9.792 6.792 8 9 8ZM16 8C17.1045 8 18.1049 8.44841 18.829 9.173L17.4153 10.5866C17.0534 10.2241 16.553 10 16 10C14.895 10 14 10.895 14 12C14 13.105 14.895 14 16 14C16.5525 14 17.0525 13.7762 17.4144 13.4144L18.828 14.828C18.104 15.552 17.104 16 16 16C13.792 16 12 14.208 12 12C12 9.792 13.792 8 16 8Z"
                              fill="url(#paint0_linear_1253_10009)"
                            ></path>
                            <defs>
                              <linearGradient
                                id="paint0_linear_1253_10009"
                                x1="2"
                                y1="3"
                                x2="21.5"
                                y2="21"
                                gradientUnits="userSpaceOnUse"
                              >
                                <stop stop-color="#FFE566"></stop>
                                <stop offset="0.15" stop-color="#B4F27D"></stop>
                                <stop offset="0.35" stop-color="#35CFFF"></stop>
                                <stop offset="0.65" stop-color="#4875FF"></stop>
                                <stop offset="0.85" stop-color="#9A4DFF"></stop>
                                <stop offset="1" stop-color="#FF5DE6"></stop>
                              </linearGradient>
                            </defs>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">
                          AI 字幕
                          <div class="trancy-menuitem-label-info">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                            >
                              <path
                                d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11 7H13V9H11V7ZM11 11H13V17H11V11Z"
                              ></path>
                            </svg>
                            <div class="trancy-menuitem-label-info-tooltip">
                              通过AI转写技术让断句效果更佳
                            </div>
                          </div>
                        </div>
                        <div class="trancy-menuitem-action">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div class="trancy-menuitem-group">
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M480-80q-82 0-155-31.5t-127.5-86Q143-252 111.5-325T80-480q0-83 31.5-155.5t86-127Q252-817 325-848.5T480-880q83 0 155.5 31.5t127 86q54.5 54.5 86 127T880-480q0 82-31.5 155t-86 127.5q-54.5 54.5-127 86T480-80Zm0-82q26-36 45-75t31-83H404q12 44 31 83t45 75Zm-104-16q-18-33-31.5-68.5T322-320H204q29 50 72.5 87t99.5 55Zm208 0q56-18 99.5-55t72.5-87H638q-9 38-22.5 73.5T584-178ZM170-400h136q-3-20-4.5-39.5T300-480q0-21 1.5-40.5T306-560H170q-5 20-7.5 39.5T160-480q0 21 2.5 40.5T170-400Zm216 0h188q3-20 4.5-39.5T580-480q0-21-1.5-40.5T574-560H386q-3 20-4.5 39.5T380-480q0 21 1.5 40.5T386-400Zm268 0h136q5-20 7.5-39.5T800-480q0-21-2.5-40.5T790-560H654q3 20 4.5 39.5T660-480q0 21-1.5 40.5T654-400Zm-16-240h118q-29-50-72.5-87T584-782q18 33 31.5 68.5T638-640Zm-234 0h152q-12-44-31-83t-45-75q-26 36-45 75t-31 83Zm-200 0h118q9-38 22.5-73.5T376-782q-56 18-99.5 55T204-640Z"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">主字幕</div>
                        <div class="trancy-menuitem-action">
                          <div class="trancy-action-label">English</div>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path
                              d="M19.7134 8.12811L19.4668 8.69379C19.2864 9.10792 18.7136 9.10792 18.5331 8.69379L18.2866 8.12811C17.8471 7.11947 17.0555 6.31641 16.0677 5.87708L15.308 5.53922C14.8973 5.35653 14.8973 4.75881 15.308 4.57612L16.0252 4.25714C17.0384 3.80651 17.8442 2.97373 18.2761 1.93083L18.5293 1.31953C18.7058 0.893489 19.2942 0.893489 19.4706 1.31953L19.7238 1.93083C20.1558 2.97373 20.9616 3.80651 21.9748 4.25714L22.6919 4.57612C23.1027 4.75881 23.1027 5.35653 22.6919 5.53922L21.9323 5.87708C20.9445 6.31641 20.1529 7.11947 19.7134 8.12811ZM5 17V15H3V17C3 19.2091 4.79086 21 7 21H10V19H7L6.85074 18.9945C5.81588 18.9182 5 18.0544 5 17ZM22.4 21L18 10H16L11.601 21H13.755L14.954 18H19.044L20.245 21H22.4ZM15.753 16L17 12.8852L18.245 16H15.753ZM8 4V2H6V4H2V11H6V14H8V11H12V4H8ZM4 6H6V9H4V6ZM8 6H10V9H8V6Z"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">翻译字幕</div>
                        <div class="trancy-menuitem-action">
                          <div class="trancy-action-label">中文</div>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div class="trancy-menuitem-group">
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path
                              d="M20.4668 8.69379L20.7134 8.12811C21.1529 7.11947 21.9445 6.31641 22.9323 5.87708L23.6919 5.53922C24.1027 5.35653 24.1027 4.75881 23.6919 4.57612L22.9748 4.25714C21.9616 3.80651 21.1558 2.97373 20.7238 1.93083L20.4706 1.31953C20.2942 0.893489 19.7058 0.893489 19.5293 1.31953L19.2761 1.93083C18.8442 2.97373 18.0384 3.80651 17.0252 4.25714L16.308 4.57612C15.8973 4.75881 15.8973 5.35653 16.308 5.53922L17.0677 5.87708C18.0555 6.31641 18.8471 7.11947 19.2866 8.12811L19.5331 8.69379C19.7136 9.10792 20.2864 9.10792 20.4668 8.69379ZM5.79993 16H7.95399L8.55399 14.5H11.4459L12.0459 16H14.1999L10.9999 8H8.99993L5.79993 16ZM9.99993 10.8852L10.6459 12.5H9.35399L9.99993 10.8852ZM15 16V8H17V16H15ZM3 3C2.44772 3 2 3.44772 2 4V20C2 20.5523 2.44772 21 3 21H21C21.5523 21 22 20.5523 22 20V11H20V19H4V5H14V3H3Z"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">翻译引擎</div>
                        <div class="trancy-menuitem-action">
                          <div class="trancy-action-label">Microsoft</div>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24px"
                            viewBox="0 -960 960 960"
                            width="24px"
                            fill="#e8eaed"
                          >
                            <path
                              d="M240-320h320v-80H240v80Zm400 0h80v-80h-80v80ZM240-480h80v-80h-80v80Zm160 0h320v-80H400v80ZM160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm0-80h640v-480H160v480Zm0 0v-480 480Z"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">字幕显示</div>
                        <div class="trancy-menuitem-action">
                          <div class="trancy-action-label">双语字幕</div>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24px"
                            viewBox="0 -960 960 960"
                            width="24px"
                            fill="#e8eaed"
                          >
                            <path
                              d="M160-240v-480 480Zm160-240v-80h-80v80h80Zm108 0q11-23 25.5-43t32.5-37h-86v80h28Zm-24 160q-2-10-2.5-19.5T401-360q0-11 .5-20.5T404-400H240v80h164Zm81 160H160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v166q-17-18-37-32.5T800-612v-108H160v480h268q11 23 25 43t32 37Zm235 0h-80l-12-60q-12-5-22.5-10.5T584-244l-58 18-40-68 46-40q-2-13-2-26t2-26l-46-40 40-68 58 18q11-8 21.5-13.5T628-500l12-60h80l12 60q12 5 23 11.5t21 14.5l58-20 40 70-46 40q2 13 2 25t-2 25l46 40-40 68-58-18q-11 8-21.5 13.5T732-220l-12 60Zm-40-120q33 0 56.5-23.5T760-360q0-33-23.5-56.5T680-440q-33 0-56.5 23.5T600-360q0 33 23.5 56.5T680-280Z"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">字幕样式</div>
                        <div class="trancy-menuitem-action">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                      <div class="trancy-menuitem">
                        <div class="trancy-menuitem-icon icon-19">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path
                              d="M4 5V19H20V5H4ZM3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4C2 3.44772 2.44772 3 3 3ZM6 7H8V9H6V7ZM6 11H8V13H6V11ZM6 15H18V17H6V15ZM11 11H13V13H11V11ZM11 7H13V9H11V7ZM16 7H18V9H16V7ZM16 11H18V13H16V11Z"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">设置快捷键</div>
                        <div class="trancy-menuitem-action">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="24"
                            viewBox="0 -960 960 960"
                            width="24"
                          >
                            <path
                              d="M504-480 348-636q-11-11-11-28t11-28q11-11 28-11t28 11l184 184q6 6 8.5 13t2.5 15q0 8-2.5 15t-8.5 13L404-268q-11 11-28 11t-28-11q-11-11-11-28t11-28l156-156Z"
                            ></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                    <div class="trancy-menuitem-focus-wrapper">
                      <div class="trancy-menuitem-focus">
                        <div class="trancy-menuitem-icon trancy-icon-focus">
                          <svg
                            class="icon-trancy-brand"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              class="icon-trancy-brand-icon"
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M2.26981 3.3543C2.49747 3.08644 2.80895 2.99474 3.0825 3.00023C4.04646 3.0195 6.89828 3.35796 10.113 4.63373C11.8224 4.03793 14.0853 3.55535 16.8303 3.526C17.4822 3.51903 18 4.05545 18 4.70159V10.8281C18 11.4858 17.4752 12.0003 16.8485 12.0121C15.9601 12.0288 14.9544 12.1145 14.1241 12.2113V14.9603C14.1241 15.0724 14.1102 15.1987 14.0706 15.3292C13.7792 16.2894 13.0493 16.9508 12.2382 17.3637C11.4256 17.7775 10.4644 17.9804 9.55306 17.9986C8.64761 18.0168 7.71703 17.8538 6.98212 17.4604C6.23348 17.0596 5.60439 16.3595 5.60439 15.3428V15.339L5.63937 11.7304C5.44415 11.6614 5.19389 11.5813 4.91526 11.4999C4.2171 11.2958 3.42754 11.1084 2.95856 11.0582C2.47548 11.0065 2.01845 10.6037 2.01088 10.0191C1.98858 8.29598 2.00645 5.08082 2.01277 4.09242C2.0141 3.88666 2.06169 3.59918 2.26981 3.3543ZM7.17098 11.2332L7.15542 12.8654C8.43327 11.8278 10.4426 11.1551 13.2565 10.7627C14.1141 10.6431 15.3426 10.5148 16.473 10.4747V5.0801C13.9265 5.14874 11.8658 5.63148 10.3578 6.18703C8.81528 6.75533 7.80923 7.48847 7.32084 8.00885C7.21419 8.12248 7.13535 8.33217 7.1396 8.67776L7.17098 11.2332ZM7.20812 15.2532C7.15751 15.3745 7.13846 15.5122 7.19502 15.6309C7.27198 15.7925 7.42598 15.9482 7.69541 16.0924C8.13611 16.3283 8.79056 16.4665 9.52289 16.4518C10.2493 16.4372 10.9787 16.2735 11.5525 15.9813C12.0295 15.7385 12.3534 15.4331 12.5245 15.0909C12.5803 14.9795 12.5972 14.854 12.5972 14.7294V12.4303C9.11285 13.0326 7.70422 14.0632 7.20812 15.2532ZM5.62999 10.101L5.61276 8.697C5.60612 8.15644 5.71821 7.4713 6.21428 6.94271C6.63293 6.49665 7.25541 6.0036 8.07195 5.54282C6.11249 4.91405 4.44095 4.65652 3.53687 4.57637C3.53018 5.76394 3.52035 8.05492 3.53329 9.5791C4.09274 9.6758 4.7712 9.84755 5.33843 10.0133C5.43781 10.0424 5.5354 10.0717 5.62999 10.101Z"
                              fill="white"
                            ></path>
                          </svg>
                        </div>
                        <div class="trancy-menuitem-label">学习模式</div>
                        <div class="trancy-menuitem-label-info">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path
                              d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11 7H13V9H11V7ZM11 11H13V17H11V11Z"
                            ></path>
                          </svg>
                          <div class="trancy-menuitem-label-info-tooltip">
                            该模式更适于沉浸观看或学习，支持查词、总结、口语练习等。<br />
                            <div class="trancy-menuitem-shrotcut"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="trancy-second-panel-menu"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <trancy-caption-window
      id="trancy-caption-window"
      style="width: 640px; height: 360px"
      ><div class="trancy-caption-container">
        <div class="trancy-caption">
          <div class="trancy-caption-drag">
            <div class="trancy-svg-icon icon-18">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.38465 9C4.38465 8.55578 4.54282 8.1755 4.85915 7.85915C5.1755 7.54282 5.55579 7.38465 6 7.38465C6.44422 7.38465 6.8245 7.54282 7.14085 7.85915C7.45719 8.1755 7.61535 8.55578 7.61535 9C7.61535 9.44422 7.45719 9.8245 7.14085 10.1408C6.8245 10.4572 6.44422 10.6153 6 10.6153C5.55579 10.6153 5.1755 10.4572 4.85915 10.1408C4.54282 9.8245 4.38465 9.44422 4.38465 9ZM4.38465 15C4.38465 14.5558 4.54282 14.1755 4.85915 13.8591C5.1755 13.5428 5.55579 13.3846 6 13.3846C6.44422 13.3846 6.8245 13.5428 7.14085 13.8591C7.45719 14.1755 7.61535 14.5558 7.61535 15C7.61535 15.4442 7.45719 15.8245 7.14085 16.1408C6.8245 16.4572 6.44422 16.6153 6 16.6153C5.55578 16.6153 5.1755 16.4572 4.85915 16.1408C4.54282 15.8245 4.38465 15.4442 4.38465 15ZM10.3847 9C10.3847 8.55578 10.5428 8.1755 10.8592 7.85915C11.1755 7.54282 11.5558 7.38465 12 7.38465C12.4442 7.38465 12.8245 7.54282 13.1409 7.85915C13.4572 8.1755 13.6154 8.55578 13.6154 9C13.6154 9.44422 13.4572 9.8245 13.1409 10.1408C12.8245 10.4572 12.4442 10.6153 12 10.6153C11.5558 10.6153 11.1755 10.4572 10.8592 10.1408C10.5428 9.8245 10.3847 9.44422 10.3847 9ZM10.3847 15C10.3847 14.5558 10.5428 14.1755 10.8592 13.8592C11.1755 13.5428 11.5558 13.3846 12 13.3846C12.4442 13.3846 12.8245 13.5428 13.1409 13.8592C13.4572 14.1755 13.6154 14.5558 13.6154 15C13.6154 15.4442 13.4572 15.8245 13.1409 16.1408C12.8245 16.4572 12.4442 16.6153 12 16.6153C11.5558 16.6153 11.1755 16.4572 10.8592 16.1408C10.5428 15.8245 10.3847 15.4442 10.3847 15ZM16.3847 9C16.3847 8.55578 16.5428 8.1755 16.8592 7.85915C17.1755 7.54282 17.5558 7.38465 18 7.38465C18.4442 7.38465 18.8245 7.54282 19.1409 7.85915C19.4572 8.1755 19.6154 8.55578 19.6154 9C19.6154 9.44422 19.4572 9.8245 19.1409 10.1408C18.8245 10.4572 18.4442 10.6153 18 10.6153C17.5558 10.6153 17.1755 10.4572 16.8592 10.1408C16.5428 9.8245 16.3847 9.44422 16.3847 9ZM16.3847 15C16.3847 14.5558 16.5428 14.1755 16.8592 13.8592C17.1755 13.5428 17.5558 13.3846 18 13.3846C18.4442 13.3846 18.8245 13.5428 19.1409 13.8592C19.4572 14.1755 19.6154 14.5558 19.6154 15C19.6154 15.4442 19.4572 15.8245 19.1409 16.1408C18.8245 16.4572 18.4442 16.6154 18 16.6154C17.5558 16.6154 17.1755 16.4572 16.8592 16.1408C16.5428 15.8245 16.3847 15.4442 16.3847 15Z"
                ></path>
              </svg>
            </div>
          </div>
          <div
            class="trancy-caption-content trancy-text-shadow"
            style="font-size: 10.6667px; background: rgba(0, 0, 0, 0.7)"
          >
            <div
              class="trancy-caption-primary inter"
              style="
                font-size: 1em;
                color: rgb(255, 255, 255);
                font-weight: normal;
              "
            >
              My power went off.
            </div>
            <div
              class="trancy-caption-secondary inter"
              style="
                font-size: 1em;
                color: rgb(255, 255, 255);
                font-weight: normal;
              "
            >
              我的电源熄灭了。
            </div>
          </div>
        </div>
      </div></trancy-caption-window
    >
  </div>
</div>
