import React, { useState, useEffect, useCallback } from "react";
import { createRoot } from "react-dom/client";

// --- Data ---
const wordData = [
  {
    id: 1,
    word: "escalade",
    explain: [
      {
        pos: "noun",
        definitions: [
          {
            definition:
              "An act of scaling a wall or rampart, especially in military operations or as a sport, involving the use of ladders, ropes, or climbing techniques to overcome vertical obstacles and reach elevated positions safely",
            chinese: "攀登，攀爬墙壁或城墙的行为",
            chinese_short: "攀登",
          },
        ],
      },
      {
        pos: "verb",
        definitions: [
          {
            definition:
              "To climb or scale, especially a wall or fortification, as in a sport or military operation, or to climb a mountain, as in a mountaineering expedition, or to climb a ladder or rope using specialized equipment and techniques for safety and efficiency",
            chinese: "攀登，攀爬，特别是墙壁或防御工事",
            chinese_short: "攀登",
          },
        ],
      },
    ],
    wordFormats: [
      { name: "原型", form: "escalade" },
      { name: "第三人称单数", form: "escalades" },
      { name: "过去式", form: "escaladed" },
      { name: "过去分词", form: "escaladed" },
      { name: "现在分词", form: "escalading" },
    ],
    phonetic: { us: "/ˌɛskəˈleɪd/", uk: "/ˈɛskəleɪd/" },
  },
  {
    id: 2,
    word: "comprehensive",
    explain: [
      {
        pos: "adjective",
        definitions: [
          {
            definition:
              "Complete and including everything that is necessary; covering all or nearly all elements or aspects of something in a thorough and detailed manner, leaving no important parts uncovered or unexplored, and providing a full understanding of the subject matter through extensive analysis and examination",
            chinese: "全面的，综合的，包含所有必要元素的",
            chinese_short: "全面的",
          },
        ],
      },
    ],
    wordFormats: [
      { name: "原型", form: "comprehensive" },
      { name: "比较级", form: "more comprehensive" },
      { name: "最高级", form: "most comprehensive" },
    ],
    phonetic: { us: "/ˌkɑːmprɪˈhensɪv/", uk: "/ˌkɒmprɪˈhensɪv/" },
  },
  {
    id: 3,
    word: "favorite",
    explain: [
      {
        pos: "adjective",
        definitions: [
          {
            definition: "Preferred before all others of the same kind.",
            chinese: "最喜爱的",
            chinese_short: "最爱的",
          },
        ],
      },
      {
        pos: "noun",
        definitions: [
          {
            definition:
              "A person or thing that is particularly popular or well-liked.",
            chinese: "特别受喜爱的人或物",
            chinese_short: "宠儿",
          },
        ],
      },
    ],
    wordFormats: [{ name: "原型", form: "favorite" }],
    phonetic: { us: "/ˈfeɪvərɪt/", uk: "/ˈfeɪvərɪt/" },
  },
];

// --- SVG Icons ---
const BackIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2.5}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <polyline points="15 18 9 12 15 6"></polyline>
  </svg>
);
const CloseIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M18 6L6 18M6 6l12 12"
      stroke="currentColor"
      strokeWidth={2.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const MoreIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2.5}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="1"></circle>
    <circle cx="12" cy="5" r="1"></circle>
    <circle cx="12" cy="19" r="1"></circle>
  </svg>
);
const ArrowRightIcon = (props) => (
  <svg
    className="lu-setting-arrow"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2.5}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <polyline points="9 18 15 12 9 6"></polyline>
  </svg>
);
const GitHubIcon = (props) => (
  <svg
    role="img"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    {...props}
  >
    <path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12" />
  </svg>
);
const GoogleIcon = (props) => (
  <svg
    role="img"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    {...props}
  >
    <path d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-5.113 2.4-4.333 0-7.4-3.4-7.4-7.8s3.067-7.8 7.4-7.8c2.4 0 4.133 1 5.113 2.08l2.7-2.7C18.6 1.12 15.92 0 12.48 0 5.867 0 .333 5.333.333 12s5.534 12 12.147 12c3.567 0 6.2-1.233 8.233-3.367 2.133-2.133 2.8-5.4 2.8-8.533 0-.733-.067-1.467-.187-2.2z" />
  </svg>
);
const AppleIcon = (props) => (
  <svg
    role="img"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    {...props}
  >
    <path d="M12.152 6.896c-.922 0-1.758.461-2.482 1.154-.792.761-1.417 1.85-1.745 2.94-.462 1.539-.142 3.39.882 4.414.724.724 1.682 1.124 2.64 1.124.893 0 1.746-.432 2.434-1.155.793-.781 1.34-1.879 1.621-2.951.199-.751.246-1.539.013-2.29-.533-1.732-2.03-2.822-3.62-2.859l-.013.003zM12.181 0C9.33 0 6.64.954 4.773 2.492c-1.637 1.344-2.846 3.14-3.414 5.393-.724 2.825-.373 5.82 1.002 8.169 1.252 2.126 3.11 3.738 5.485 4.545.694.246 1.447.37 2.2.37.724 0 1.447-.123 2.172-.37 2.375-.794 4.221-2.387 5.472-4.545 1.375-2.349 1.726-5.344 1.002-8.169-.582-2.253-1.791-4.049-3.428-5.393C17.684.94 15.003 0 12.181 0z" />
  </svg>
);
const MailIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
    <polyline points="22,6 12,13 2,6"></polyline>
  </svg>
);
const FeatherIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path>
    <line x1="16" y1="8" x2="2" y2="22"></line>
    <line x1="17.5" y1="15" x2="9" y2="15"></line>
  </svg>
);
const ImmersiveIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M12 3c-1.2 0-2.4.3-3.5.8L3 6.5v3.3c0 4.8 3.5 9.2 8.5 10.7 1.2-3.8 3.3-7.2 6.5-9.8.3-.3.5-.6.8-.9-2.3-2.5-5.5-4.1-9.3-4.1z"></path>
    <path d="M18.8 13.2c.2-.2.3-.5.3-.8 0-1.5-1.1-2.8-2.6-3.2-1.4-.4-2.8 0-3.8 1s-1.4 2.4-1 3.8c.4 1.4 1.7 2.5 3.2 2.6.3 0 .6-.1.8-.3l1.4-1.4-1.4-1.4 1.4-1.4z"></path>
  </svg>
);
const HoverTranslateIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="10"></circle>
    <path d="M12 16.5V12a2.5 2.5 0 1 0-5 0V12"></path>
    <line x1="12" y1="7.5" x2="12.01" y2="7.5"></line>
  </svg>
);
const AlertTriangleIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    <line x1="12" y1="9" x2="12" y2="13"></line>
    <line x1="12" y1="17" x2="12.01" y2="17"></line>
  </svg>
);
const GlobeIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="2" y1="12" x2="22" y2="12"></line>
    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
  </svg>
);
const PaperPlaneIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M22 2 11 13"></path>
    <path d="m22 2-7 20-4-9-9-4 20-7z"></path>
  </svg>
);
const HelpCircleIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="10"></circle>
    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
    <line x1="12" y1="17" x2="12.01" y2="17"></line>
  </svg>
);
const MessageSquareIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
);
const RefreshCwIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M1 4v6h6"></path>
    <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
  </svg>
);

const SpeakerIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M11 5L6 9H2v6h4l5 4V5z"></path>
    <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
  </svg>
);
const ActivityIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M21.5 12H14l-2.5 6-4-12-2.5 6H2.5"></path>
  </svg>
);
const MoonIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
  </svg>
);
const CommandIcon = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth={2}
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3zM6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3zM3 18a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v0a3 3 0 0 0-3-3H6a3 3 0 0 0-3 3zm0-12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3v0a3 3 0 0 0-3-3H6a3 3 0 0 0-3 3z" />
  </svg>
);

// --- Components ---
const LoginView = ({ onLogin }) => (
  <div className="lu-login-container">
    <div className="lu-login-box">
      <div
        className="lu-brand"
        style={{ justifyContent: "center", marginBottom: "32px" }}
      >
        <div className="lu-logo">L</div>
        <div className="lu-title">Welcome to Lucid</div>
      </div>
      <div className="lu-social-login-group">
        <button className="lu-social-login-btn github" onClick={onLogin}>
          <GitHubIcon /> Sign in with GitHub
        </button>
        <button className="lu-social-login-btn google" onClick={onLogin}>
          <GoogleIcon /> Sign in with Google
        </button>
        <button className="lu-social-login-btn apple" onClick={onLogin}>
          <AppleIcon /> Sign in with Apple
        </button>
      </div>
      <div className="lu-divider">
        <span>OR</span>
      </div>
      <div className="lu-email-form">
        <div className="lu-email-input-wrapper">
          <MailIcon />
          <input
            type="email"
            placeholder="Enter your email"
            className="lu-email-input"
          />
        </div>
        <button className="lu-send-code-btn" onClick={onLogin}>
          Send Code
        </button>
      </div>
    </div>
  </div>
);

const Typewriter = ({ text }) => {
  const [displayedText, setDisplayedText] = useState("");

  useEffect(() => {
    setDisplayedText("");
    let i = 0;
    const intervalId = setInterval(() => {
      if (i < text.length) {
        setDisplayedText((prev) => prev + text.charAt(i));
        i++;
      } else {
        clearInterval(intervalId);
      }
    }, 20); // Typing speed
    return () => clearInterval(intervalId);
  }, [text]);

  return <span>{displayedText}</span>;
};

const WordItem = ({ data }) => {
  const [isExpanded, setExpanded] = useState(false);
  const [isMastered, setMastered] = useState(false);
  const [isFavorite, setFavorite] = useState(false);

  const handleHeaderClick = (e) => {
    if (e.target.closest(".lu-word-actions")) {return;}
    setExpanded(!isExpanded);
  };

  const toggleFavorite = (e) => {
    e.stopPropagation();
    setFavorite(!isFavorite);
  };

  const toggleMastered = (e) => {
    e.stopPropagation();
    setMastered(!isMastered);
  };

  const favoriteIcon = isFavorite ? (
    <svg
      width={18}
      height={18}
      viewBox="0 0 24 24"
      fill="currentColor"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
    </svg>
  ) : (
    <svg
      width={18}
      height={18}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth={2}
    >
      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
    </svg>
  );

  return (
    <div className={`lu-card lu-word-item ${isExpanded ? "expanded" : ""}`}>
      <div className="lu-word-item-header" onClick={handleHeaderClick}>
        <div className="lu-word-text-wrapper">
          <svg
            className="lu-expand-icon"
            width={16}
            height={16}
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth={2.5}
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
          <div className="lu-word-text">{data.word}</div>
        </div>
        <div className="lu-word-actions">
          <button
            className={`lu-word-action master ${isMastered ? "active" : ""}`}
            title="掌握"
            onClick={toggleMastered}
          >
            <svg
              width={18}
              height={18}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={2.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M20 6L9 17l-5-5" />
            </svg>
          </button>
          <button
            className={`lu-word-action favorite ${isFavorite ? "active" : ""}`}
            title="收藏"
            onClick={toggleFavorite}
          >
            {favoriteIcon}
          </button>
        </div>
      </div>
      <div className="lu-word-details">
        {/* Phonetics */}
        <div className="lu-word-detail-item lu-phonetic-group">
          <strong>音标</strong>
          <div>
            <span className="lu-phonetic-item">
              <span className="lu-phonetic-label">US</span>{" "}
              <span className="lu-phonetic">{data.phonetic.us}</span>
            </span>
            <span className="lu-phonetic-item">
              <span className="lu-phonetic-label">UK</span>{" "}
              <span className="lu-phonetic">{data.phonetic.uk}</span>
            </span>
          </div>
        </div>

        {/* Explanations */}
        {data.explain.map((exp, index) => (
          <div key={index} className="lu-word-detail-item lu-pos-section">
            <strong>{exp.pos}</strong>
            <div className="lu-definitions-list">
              {exp.definitions.map((def, defIndex) => (
                <div key={defIndex} className="lu-definition-item">
                  <div className="lu-definition-cn">{def.chinese}</div>
                  <div className="lu-definition-en">
                    {isExpanded && <Typewriter text={def.definition} />}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Word Forms */}
        {data.wordFormats && data.wordFormats.length > 0 && (
          <div className="lu-word-detail-item">
            <strong>单词形态</strong>
            <div className="lu-word-forms-grid">
              {data.wordFormats.map((format, index) => (
                <React.Fragment key={index}>
                  <span className="lu-word-form-name">{format.name}</span>
                  <span className="lu-word-form-value">{format.form}</span>
                </React.Fragment>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

const MyView = () => (
  <div id="view-my">
    <div className="lu-stats-group">
      <div className="lu-card lu-stat-card">
        <div className="lu-stat-number">784</div>
        <div className="lu-stat-title">生词本</div>
      </div>
      <div className="lu-card lu-stat-card">
        <div className="lu-stat-number">12</div>
        <div className="lu-stat-title">今日单词</div>
      </div>
    </div>
    <div id="word-list-container" className="lu-word-section">
      <div className="lu-section-header">
        <div className="lu-section-title">本页生词</div>
        <div className="lu-word-count">{wordData.length}</div>
      </div>
      {wordData.map((word) => (
        <WordItem key={word.id} data={word} />
      ))}
    </div>
  </div>
);

const Switch = ({ defaultChecked = false }) => {
  const [isActive, setActive] = useState(defaultChecked);
  return (
    <div
      className={`lu-switch ${isActive ? "active" : ""}`}
      role="switch"
      aria-checked={isActive}
      tabIndex={0}
      onClick={(e) => {
        e.stopPropagation();
        setActive(!isActive);
      }}
    >
      <div className="lu-switch-thumb"></div>
    </div>
  );
};

const SettingItem = ({
  icon = null,
  title,
  value = null,
  tag = null,
  children = null,
  hasArrow = true,
  onClick = () => {},
}) => (
  <div className="lu-setting-item" onClick={onClick}>
    {icon && React.cloneElement(icon, { className: "lu-setting-icon" })}
    <div className="lu-setting-title">
      {title} {tag && <span className="lu-new-tag">{tag}</span>}
    </div>
    {value && <span className="lu-setting-value">{value}</span>}
    {children}
    {hasArrow && !children && <ArrowRightIcon />}
  </div>
);

const SettingsView = ({ onNavigate }) => (
  <div id="view-settings">
    <div className="lu-settings-section">
      <div className="lu-card lu-settings-card">
        <div
          id="account-info-btn"
          className="lu-setting-item"
          onClick={() => onNavigate("account")}
        >
          <svg
            className="lu-setting-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <div className="lu-setting-title">账户信息</div>
          <div className="lu-user-info">
            <span className="lu-setting-value">
              R1ozron
              <svg
                className="lu-verified-icon"
                width={16}
                height={16}
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path>
              </svg>
            </span>
          </div>
          <ArrowRightIcon />
        </div>
      </div>
    </div>

    <div className="lu-settings-section">
      <div className="lu-settings-header">网页翻译设置</div>
      <div className="lu-card lu-settings-card">
        <SettingItem icon={<FeatherIcon />} title="翻译引擎" tag="NEW" />
        <SettingItem
          icon={<ImmersiveIcon />}
          title="沉浸翻译"
          value="对照翻译"
        />
        <SettingItem
          icon={<HoverTranslateIcon />}
          title="划词翻译"
          value="划词 + Alt"
        />
        <SettingItem
          icon={<AlertTriangleIcon />}
          title="学习语言"
          value="English"
        />
        <SettingItem icon={<GlobeIcon />} title="翻译语言" value="中文" />
      </div>
    </div>

    <div className="lu-settings-section">
      <div className="lu-settings-header">通用设置</div>
      <div className="lu-card lu-settings-card">
        <SettingItem icon={<SpeakerIcon />} title="自选发音" value="默认发音" />
        <SettingItem icon={<ActivityIcon />} title="界面语言" value="中文" />
        <SettingItem icon={<MoonIcon />} title="主题设置" value="Auto" />
        <SettingItem icon={<CommandIcon />} title="配置快捷键" />
      </div>
    </div>

    <div className="lu-settings-section">
      <div className="lu-card lu-settings-card">
        <SettingItem icon={<HelpCircleIcon />} title="视频教程" />
        <SettingItem icon={<MessageSquareIcon />} title="意见反馈" />
        <SettingItem icon={<RefreshCwIcon />} title="更新日志" value="0.0.1" />
      </div>
    </div>
  </div>
);

const LearnView = () => (
  <div id="view-learn">
    <div className="lu-card" style={{ textAlign: "center", padding: "40px" }}>
      <h3 style={{ color: "#fff" }}>学习视图</h3>
      <p style={{ color: "#9e9e9e" }}>此页面内容待添加。</p>
    </div>
  </div>
);

const AccountView = ({ onLogout }) => (
  <div id="view-account" className="lu-view lu-account-view active">
    <div className="lu-card lu-settings-card">
      <div className="lu-setting-item">
        <div className="lu-setting-title">邮箱</div>
        <span className="lu-setting-value"><EMAIL></span>
      </div>
      <div className="lu-setting-item">
        <div className="lu-setting-title">会员</div>
        <span className="lu-membership-tag early-bird">Early Bird</span>
      </div>
      <div className="lu-setting-item">
        <div className="lu-setting-title">到期时间</div>
        <span className="lu-setting-value">2024.09.06</span>
      </div>
      <SettingItem title="订阅管理" hasArrow={true} />
      <SettingItem title="删除账号" hasArrow={true} />
    </div>
    <button className="lu-logout-button" onClick={onLogout}>
      退出账号
    </button>
  </div>
);

const App = () => {
  const [isSliderOpen, setSliderOpen] = useState(true);
  const [activeView, setActiveView] = useState("my");
  const [currentPage, setCurrentPage] = useState("main"); // 'main' or page name like 'account'
  const [pageTitle, setPageTitle] = useState("Lucid");
  const [isLoggedIn, setIsLoggedIn] = useState(true);

  const handleLogin = () => setIsLoggedIn(true);
  const handleLogout = () => setIsLoggedIn(false);

  const handleClose = () => setSliderOpen(false);

  const changeView = useCallback((view) => {
    setActiveView(view);
    setCurrentPage("main");
    setPageTitle("Lucid");
  }, []);

  const navigateTo = useCallback((page) => {
    setCurrentPage(page);
    if (page === "account") {
      setPageTitle("Lucid 账号");
    }
  }, []);

  const goBack = useCallback(() => {
    setCurrentPage("main");
    setPageTitle("Lucid");
    // This ensures the correct footer button is active when going back
    const settingsButton = document.querySelector(
      '.lu-action-button[data-view="settings"]'
    );
    if (settingsButton && !settingsButton.classList.contains("active")) {
      setActiveView("settings");
    }
  }, []);

  const renderView = () => {
    if (currentPage === "account") {
      return <AccountView onLogout={handleLogout} />;
    }
    switch (activeView) {
      case "settings":
        return <SettingsView onNavigate={navigateTo} />;
      case "learn":
        return <LearnView />;
      case "my":
      default:
        return <MyView />;
    }
  };

  if (!isLoggedIn) {
    return <LoginView onLogin={handleLogin} />;
  }

  const isSubPage = currentPage !== "main";

  return (
    <>
      <div style={{ padding: "20px", display: "none" }}>
        <h1 style={{ color: "#f0f0f0" }}>Lucid Slider in React</h1>
        <p>This app has been converted to React.</p>
        <button id="open-slider-btn" onClick={() => setSliderOpen(true)}>
          打开侧边栏
        </button>
      </div>

      <div
        id="lucid-slider"
        className={`lu-slide ${!isSliderOpen ? "hidden" : ""}`}
      >
        <div
          id="slider-header"
          className={`lu-slider-header ${isSubPage ? "is-page" : ""}`}
        >
          <button
            id="back-button"
            className="lu-header-button lu-back-button"
            aria-label="返回"
            onClick={goBack}
            style={{ display: isSubPage ? "flex" : "none" }}
          >
            <BackIcon />
          </button>

          <div
            className="lu-brand"
            style={{ display: isSubPage ? "none" : "flex" }}
          >
            <div className="lu-logo">L</div>
            <div className="lu-title">Lucid</div>
            <div className="lu-vip-tag early-bird">Early Bird</div>
          </div>
          {isSubPage && (
            <div id="header-title" className="lu-title">
              {pageTitle}
            </div>
          )}

          <button
            id="close-button"
            className="lu-header-button lu-close-button"
            aria-label="关闭"
            onClick={handleClose}
            style={{ display: isSubPage ? "none" : "flex" }}
          >
            <CloseIcon />
          </button>
          <button
            id="more-button"
            className="lu-header-button lu-more-button"
            aria-label="更多信息"
            style={{ display: isSubPage ? "flex" : "none" }}
          >
            <MoreIcon />
          </button>
        </div>

        <div className="lu-slider-content">{renderView()}</div>

        <div className="lu-slider-footer">
          <button
            className={`lu-action-button ${activeView === "my" && currentPage === "main" ? "active" : ""}`}
            onClick={() => changeView("my")}
          >
            <svg
              width={20}
              height={20}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
            <span>我的</span>
          </button>
          <button
            className={`lu-action-button ${activeView === "settings" || currentPage === "account" ? "active" : ""}`}
            onClick={() => changeView("settings")}
          >
            <svg
              width={20}
              height={20}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            <span>设置</span>
          </button>
          <button
            className={`lu-action-button ${activeView === "learn" && currentPage === "main" ? "active" : ""}`}
            onClick={() => changeView("learn")}
          >
            <svg
              width={20}
              height={20}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
              <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
            </svg>
            <span>学习</span>
          </button>
        </div>
      </div>
    </>
  );
};

const container = document.getElementById("root");
const root = createRoot(container);
root.render(<App />);
