<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>完整字幕翻译UI演示 - 拖拽功能与视频容器定位</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #1E1E1E;
      color: white;
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #FFB700, #996E00);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .header p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 1.1rem;
    }
    
    .demo-section {
      margin-bottom: 60px;
    }
    
    .demo-section h2 {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: #FFC862;
    }
    
    .demo-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 30px;
    }
    
    button {
      background: linear-gradient(135deg, #FFB700, #996E00);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(255, 183, 0, 0.3);
    }
    
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 183, 0, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    .video-mock {
      width: 100%;
      height: 400px;
      background: #000;
      border-radius: 12px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20px;
      overflow: hidden;
    }
    
    .video-mock::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="20" fill="rgba(255,255,255,0.1)"/><path d="M45 35 L65 50 L45 65 Z" fill="rgba(255,255,255,0.3)"/></svg>') center/80px no-repeat;
    }
    
    .feature-highlight {
      background: rgba(0, 255, 127, 0.1);
      border: 1px solid rgba(0, 255, 127, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }
    
    .feature-highlight h3 {
      color: #00FF7F;
      margin-bottom: 10px;
      font-size: 1.3rem;
    }
    
    .feature-highlight p {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }
    
    .settings-trigger {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
    
    .keyboard-shortcuts {
      background: rgba(255, 200, 98, 0.1);
      border: 1px solid rgba(255, 200, 98, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }
    
    .keyboard-shortcuts h3 {
      color: #FFC862;
      margin-bottom: 10px;
    }
    
    .shortcut {
      display: flex;
      justify-content: space-between;
      margin: 5px 0;
      font-size: 14px;
    }
    
    .key {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }

    .status-info {
      background: rgba(138, 43, 226, 0.1);
      border: 1px solid rgba(138, 43, 226, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }
    
    .status-info h3 {
      color: #8A2BE2;
      margin-bottom: 10px;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      margin: 5px 0;
      font-size: 14px;
    }
    
    .status-value {
      color: #00FF7F;
      font-weight: 500;
    }
    
    @media (max-width: 768px) {
      .demo-controls {
        flex-direction: column;
      }
      
      button {
        width: 100%;
      }
      
      .video-mock {
        height: 250px;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Lucid 字幕翻译 - 完整功能演示</h1>
      <p>拖拽功能、视频容器定位、设置面板修复</p>
    </div>
    
    <div class="feature-highlight">
      <h3>🎯 新功能亮点</h3>
      <p>
        <strong>拖拽功能：</strong> 字幕组件现在支持hover显示拖拽手柄，可随意拖拽到任意位置<br/>
        <strong>视频容器定位：</strong> 字幕会自动查找视频容器并相对定位，不再跟随页面滚动<br/>
        <strong>设置面板修复：</strong> 解决了设置面板立即消失的问题，增加了点击检测逻辑<br/>
        <strong>触摸支持：</strong> 支持移动端触摸拖拽操作
      </p>
    </div>
    
    <div class="status-info">
      <h3>当前状态信息</h3>
      <div class="status-item">
        <span>拖拽功能：</span>
        <span class="status-value">已启用</span>
      </div>
      <div class="status-item">
        <span>视频容器检测：</span>
        <span class="status-value" id="container-status">检测中...</span>
      </div>
      <div class="status-item">
        <span>设置面板状态：</span>
        <span class="status-value" id="settings-status">已修复</span>
      </div>
      <div class="status-item">
        <span>响应式设计：</span>
        <span class="status-value">已适配</span>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>字幕显示 & 拖拽测试</h2>
      <div class="demo-controls">
        <button onclick="showBilingualSubtitle()">显示双语字幕</button>
        <button onclick="showLongSubtitle()">显示长文本字幕</button>
        <button onclick="showCustomPositionSubtitle()">自定义位置字幕</button>
        <button onclick="testDragFunctionality()">测试拖拽功能</button>
        <button onclick="hideSubtitle()">隐藏字幕</button>
      </div>
      
      <div class="video-mock" id="videoMock" class="video-container">
        <span>模拟视频播放器容器 - 字幕将定位在此容器内</span>
      </div>
      
      <div class="keyboard-shortcuts">
        <h3>操作说明</h3>
        <div class="shortcut">
          <span>鼠标悬停字幕查看拖拽手柄</span>
          <span class="key">鼠标悬停</span>
        </div>
        <div class="shortcut">
          <span>拖拽6点手柄移动字幕位置</span>
          <span class="key">拖拽6点图标</span>
        </div>
        <div class="shortcut">
          <span>字幕主体显示正常光标（不可拖拽）</span>
          <span class="key">默认光标</span>
        </div>
        <div class="shortcut">
          <span>移动端触摸拖拽6点手柄</span>
          <span class="key">触摸拖动</span>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>设置面板测试</h2>
      <div class="demo-controls">
        <button onclick="showSettings()">显示设置面板</button>
        <button onclick="hideSettings()">隐藏设置面板</button>
        <button onclick="toggleSettings()">切换设置面板</button>
        <button onclick="testSettingsStability()">测试面板稳定性</button>
      </div>
      
      <div class="keyboard-shortcuts">
        <h3>键盘快捷键</h3>
        <div class="shortcut">
          <span>切换设置面板</span>
          <span class="key">Ctrl + Shift + S</span>
        </div>
        <div class="shortcut">
          <span>隐藏所有UI</span>
          <span class="key">Esc</span>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>响应式测试</h2>
      <div class="demo-controls">
        <button onclick="simulateMobile()">模拟移动端</button>
        <button onclick="simulateTablet()">模拟平板端</button>
        <button onclick="simulateDesktop()">模拟桌面端</button>
        <button onclick="updateSettingsPanelPosition()">更新设置面板位置</button>
      </div>
    </div>
  </div>
  
  <div class="settings-trigger">
    <button onclick="toggleSettings()">⚙️ 设置</button>
  </div>
  
  <script>
    // 导入实际的DirectSubtitleManager实现
    class DirectSubtitleManagerDemo {
      constructor() {
        this.subtitleElement = null;
        this.settingsElement = null;
        this.isSettingsVisible = false;
        this.config = {
          enabled: true,
          displayMode: 'bilingual'
        };
        
        this.injectStyles();
        this.setupEventListeners();
        this.updateContainerStatus();
      }
      
      updateContainerStatus() {
        const container = this.findVideoContainer();
        const statusElement = document.getElementById('container-status');
        if (statusElement) {
          statusElement.textContent = container ? '已检测到' : '使用默认容器';
        }
      }
      
      injectStyles() {
        if (document.getElementById('lucid-subtitle-styles')) return;

        const styleElement = document.createElement('style');
        styleElement.id = 'lucid-subtitle-styles';
        styleElement.textContent = `
          /* 字幕显示样式 - 完整版本 */
          .lucid-subtitle-overlay {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            pointer-events: auto;
            cursor: default; /* 字幕主体显示正常光标 */
            
            padding: 5.24px 3.90px 6.24px 3.90px;
            background: rgba(0, 0, 0, 0.70);
            border-radius: 12px;
            
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: 2.10px;
            
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(2px);
            
            min-width: 100px;
            max-width: 80%;
            word-wrap: break-word;
            
            opacity: 0;
            transform: translateX(-50%) translateY(10px);
            transition: all 0.3s ease-out;
          }
          
          .lucid-subtitle-overlay.visible {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
          
          .lucid-subtitle-overlay.dragging {
            transition: none;
            user-select: none;
          }
          
          /* 拖拽控制器 - hover时显示 */
          .lucid-subtitle-drag-handle {
            position: absolute;
            top: -30px; /* 稍微向上移动一点，增加与字幕的连接 */
            left: 50%;
            transform: translateX(-50%);
            
            height: 20px; /* 增加高度提升触发区域 */
            padding: 2px 9px; /* 增加上下padding */
            background: rgba(0, 0, 0, 0.80);
            border-radius: 20px;
            outline: 1px rgba(255, 255, 255, 0.20) solid;
            outline-offset: -1px;
            backdrop-filter: blur(5px);
            
            display: flex;
            justify-content: center;
            align-items: center;
            
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            cursor: grab;
          }
          
          .lucid-subtitle-drag-handle:active {
            cursor: grabbing;
          }
          
          .lucid-subtitle-overlay:hover .lucid-subtitle-drag-handle,
          .lucid-subtitle-drag-handle:hover {
            opacity: 1;
            pointer-events: auto;
          }
          
          .lucid-subtitle-drag-icon {
            width: 19px;
            height: 18px;
            color: rgba(255, 255, 255, 0.8);
          }
          
          .lucid-subtitle-text {
            width: 100%;
            text-align: center;
            color: white;
            font-size: 15.60px;
            font-weight: 400;
            line-height: 21.86px;
            text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
            margin: 0;
            padding: 0;
            white-space: pre-wrap;
          }
          
          .lucid-subtitle-text.original {
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
          }
          
          .lucid-subtitle-text.translated {
            font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            margin-top: 2.10px;
          }
          
          /* 设置面板样式 */
          .lucid-settings-panel {
            position: fixed;
            top: 70px;
            right: 20px;
            width: 282px;
            z-index: 10001;
            
            padding: 1px;
            background: rgba(28, 28, 28, 0.90);
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
            border-radius: 12px;
            outline: 1px rgba(255, 255, 255, 0.10) solid;
            outline-offset: -1px;
            backdrop-filter: blur(5px);
            
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
            transition: all 0.2s ease-out;
            pointer-events: none;
          }
          
          .lucid-settings-panel.visible {
            opacity: 1;
            transform: scale(1) translateY(0);
            pointer-events: auto;
          }
          
          .lucid-settings-content {
            width: 100%;
            display: flex;
            flex-direction: column;
          }
          
          .lucid-setting-item {
            width: 100%;
            height: 40px;
            padding: 0 8px 0 12px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            transition: background-color 0.2s ease;
          }
          
          .lucid-setting-item.clickable {
            cursor: pointer;
          }
          
          .lucid-setting-item.clickable:hover {
            background: rgba(255, 255, 255, 0.05);
          }
          
          .lucid-setting-icon {
            width: 28px;
            height: 20px;
            padding-right: 8px;
            display: flex;
            align-items: center;
            font-size: 16px;
          }
          
          .lucid-setting-label {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex: 1;
            
            color: rgba(255, 255, 255, 0.80);
            font-size: 13px;
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 13px;
            text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
            white-space: nowrap;
          }
          
          .lucid-setting-value {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 4px;
            
            max-width: 110px;
            padding-right: 4px;
            color: rgba(255, 255, 255, 0.90);
            font-size: 12.90px;
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 15.60px;
            text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          
          .lucid-setting-separator {
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, 0.08);
          }
          
          /* 开关组件样式 */
          .lucid-toggle {
            width: 56px;
            height: 38px;
            padding: 12px;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            cursor: pointer;
          }
          
          .lucid-toggle-track {
            width: 32px;
            height: 14px;
            border-radius: 7px;
            position: relative;
            transition: background-color 0.3s ease;
          }
          
          .lucid-toggle-track.enabled {
            background: rgba(255, 200, 98, 0.5);
          }
          
          .lucid-toggle-track.disabled {
            background: rgba(255, 255, 255, 0.3);
          }
          
          .lucid-toggle-thumb {
            width: 18px;
            height: 18px;
            border-radius: 9px;
            background: #FFC862;
            position: absolute;
            top: -2px;
            left: -1px;
            transition: transform 0.3s ease;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.2);
          }
          
          .lucid-toggle-thumb.enabled {
            transform: translateX(15px);
          }
          
          .lucid-toggle-thumb.disabled {
            transform: translateX(0);
          }
          
          /* 响应式适配 */
          @media (max-width: 768px) {
            .lucid-settings-panel {
              right: 10px;
              left: 10px;
              width: auto;
            }
            
            .lucid-subtitle-overlay {
              max-width: 90%;
              bottom: 20px;
            }
          }
          
          @media (max-width: 480px) {
            .lucid-subtitle-text {
              font-size: 14px;
              line-height: 20px;
            }
            
            .lucid-subtitle-overlay {
              bottom: 15px;
            }
          }
        `;
        
        document.head.appendChild(styleElement);
      }
      
      setupEventListeners() {
        document.addEventListener('keydown', (event) => {
          if (event.ctrlKey && event.shiftKey && event.key === 'S') {
            event.preventDefault();
            this.toggleSettings();
          }
          
          if (event.key === 'Escape') {
            this.hideSettings();
          }
        });

        // 使用改进的点击外部关闭逻辑
        document.addEventListener('click', (event) => {
          setTimeout(() => {
            if (this.isSettingsVisible && this.settingsElement) {
              const target = event.target;
              if (!this.settingsElement.contains(target)) {
                const isSettingsButton = target.closest('[onclick*="Settings"]') ||
                                       target.closest('.settings-trigger');
                
                if (!isSettingsButton) {
                  this.hideSettings();
                }
              }
            }
          }, 100);
        });
      }
      
      findVideoContainer() {
        const videoSelectors = [
          '#videoMock',
          '.video-mock',
          'video',
          '.video-player',
          '.video-container',
          '.player-container',
          '.video-wrapper'
        ];

        for (const selector of videoSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            const rect = element.getBoundingClientRect();
            if (rect.width > 200 && rect.height > 150) {
              return element;
            }
          }
        }

        return null;
      }
      
      setupDragFunctionality(subtitleElement, dragHandle) {
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let initialX = 0;
        let initialY = 0;

        // 鼠标按下开始拖拽 - 只在拖拽手柄上监听
        const handleMouseDown = (e) => {
          isDragging = true;
          subtitleElement.classList.add('dragging');
          dragHandle.style.cursor = 'grabbing';
          
          startX = e.clientX;
          startY = e.clientY;
          
          const rect = subtitleElement.getBoundingClientRect();
          initialX = rect.left + rect.width / 2;
          initialY = rect.top;
          
          e.preventDefault();
          e.stopPropagation(); // 防止事件冒泡到字幕主体
          
          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
        };

        const handleMouseMove = (e) => {
          if (!isDragging) return;
          
          const deltaX = e.clientX - startX;
          const deltaY = e.clientY - startY;
          
          const newX = initialX + deltaX;
          const newY = initialY + deltaY;
          
          subtitleElement.style.position = 'fixed';
          subtitleElement.style.left = newX + 'px';
          subtitleElement.style.top = newY + 'px';
          subtitleElement.style.transform = 'translateX(-50%)';
          subtitleElement.style.bottom = 'auto';
        };

        const handleMouseUp = () => {
          isDragging = false;
          subtitleElement.classList.remove('dragging');
          dragHandle.style.cursor = 'grab';
          
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
        };

        // 只在拖拽手柄上添加拖拽事件
        dragHandle.addEventListener('mousedown', handleMouseDown);
        
        // 确保字幕主体不可拖拽
        subtitleElement.addEventListener('mousedown', (e) => {
          // 如果点击的不是拖拽手柄，阻止任何拖拽行为
          if (!dragHandle.contains(e.target)) {
            e.stopPropagation();
          }
        });
        
        // 触摸事件支持（移动端）- 同样只在拖拽手柄上
        const handleTouchStart = (e) => {
          if (e.touches.length !== 1) return;
          
          const touch = e.touches[0];
          isDragging = true;
          subtitleElement.classList.add('dragging');
          
          startX = touch.clientX;
          startY = touch.clientY;
          
          const rect = subtitleElement.getBoundingClientRect();
          initialX = rect.left + rect.width / 2;
          initialY = rect.top;
          
          e.preventDefault();
          e.stopPropagation();
          
          document.addEventListener('touchmove', handleTouchMove, { passive: false });
          document.addEventListener('touchend', handleTouchEnd);
        };

        const handleTouchMove = (e) => {
          if (!isDragging || e.touches.length !== 1) return;
          
          const touch = e.touches[0];
          const deltaX = touch.clientX - startX;
          const deltaY = touch.clientY - startY;
          
          const newX = initialX + deltaX;
          const newY = initialY + deltaY;
          
          subtitleElement.style.position = 'fixed';
          subtitleElement.style.left = newX + 'px';
          subtitleElement.style.top = newY + 'px';
          subtitleElement.style.transform = 'translateX(-50%)';
          subtitleElement.style.bottom = 'auto';
          
          e.preventDefault();
        };

        const handleTouchEnd = () => {
          isDragging = false;
          subtitleElement.classList.remove('dragging');
          
          document.removeEventListener('touchmove', handleTouchMove);
          document.removeEventListener('touchend', handleTouchEnd);
        };

        dragHandle.addEventListener('touchstart', handleTouchStart, { passive: false });
      }
      
      showSubtitle(originalText, translatedText, position) {
        this.hideSubtitle();

        this.subtitleElement = document.createElement('div');
        this.subtitleElement.className = 'lucid-subtitle-overlay';
        this.subtitleElement.id = 'lucid-subtitle-overlay';

        // 创建拖拽控制器
        const dragHandle = document.createElement('div');
        dragHandle.className = 'lucid-subtitle-drag-handle';
        
        const dragIcon = document.createElement('div');
        dragIcon.className = 'lucid-subtitle-drag-icon';
        dragIcon.innerHTML = `
          <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3.67627 6.75595C3.67627 6.42278 3.7949 6.13757 4.03214 5.90031C4.26941 5.66306 4.55462 5.54443 4.88778 5.54443C5.22095 5.54443 5.50616 5.66306 5.74342 5.90031C5.98067 6.13757 6.09929 6.42278 6.09929 6.75595C6.09929 7.08911 5.98067 7.37432 5.74342 7.61155C5.50616 7.84885 5.22095 7.96742 4.88778 7.96742C4.55462 7.96742 4.26941 7.84885 4.03214 7.61155C3.7949 7.37432 3.67627 7.08911 3.67627 6.75595ZM3.67627 11.2559C3.67627 10.9228 3.7949 10.6376 4.03214 10.4003C4.26941 10.163 4.55462 10.0444 4.88778 10.0444C5.22095 10.0444 5.50616 10.163 5.74342 10.4003C5.98067 10.6376 6.09929 10.9228 6.09929 11.2559C6.09929 11.5891 5.98067 11.8743 5.74342 12.1115C5.50616 12.3488 5.22095 12.4674 4.88778 12.4674C4.55462 12.4674 4.26941 12.3488 4.03214 12.1115C3.7949 11.8743 3.67627 11.5891 3.67627 11.2559ZM8.17631 6.75595C8.17631 6.42278 8.29488 6.13757 8.53218 5.90031C8.76941 5.66306 9.05463 5.54443 9.38778 5.54443C9.72093 5.54443 10.0062 5.66306 10.2435 5.90031C10.4807 6.13757 10.5993 6.42278 10.5993 6.75595C10.5993 7.08911 10.4807 7.37432 10.2435 7.61155C10.0062 7.84885 9.72093 7.96742 9.38778 7.96742C9.05463 7.96742 8.76941 7.84885 8.53218 7.61155C8.29488 7.37432 8.17631 7.08911 8.17631 6.75595ZM8.17631 11.2559C8.17631 10.9228 8.29488 10.6376 8.53218 10.4003C8.76941 10.163 9.05463 10.0444 9.38778 10.0444C9.72093 10.0444 10.0062 10.163 10.2435 10.4003C10.4807 10.6376 10.5993 10.9228 10.5993 11.2559C10.5993 11.5891 10.4807 11.8743 10.2435 12.1115C10.0062 12.3488 9.72093 12.4674 9.38778 12.4674C9.05463 12.4674 8.76941 12.3488 8.53218 12.1115C8.29488 11.8743 8.17631 11.5891 8.17631 11.2559ZM12.6763 6.75595C12.6763 6.42278 12.7949 6.13757 13.0322 5.90031C13.2694 5.66306 13.5546 5.54443 13.8878 5.54443C14.2209 5.54443 14.5062 5.66306 14.7435 5.90031C14.9807 6.13757 15.0993 6.42278 15.0993 6.75595C15.0993 7.08911 14.9807 7.37432 14.7435 7.61155C14.5062 7.84885 14.2209 7.96742 13.8878 7.96742C13.5546 7.96742 13.2694 7.84885 13.0322 7.61155C12.7949 7.37432 12.6763 7.08911 12.6763 6.75595ZM12.6763 11.2559C12.6763 10.9228 12.7949 10.6376 13.0322 10.4003C13.2694 10.163 13.5546 10.0444 13.8878 10.0444C14.2209 10.0444 14.5062 10.163 14.7435 10.4003C14.9807 10.6376 15.0993 10.9228 15.0993 11.2559C15.0993 11.5891 14.9807 11.8743 14.7435 12.1115C14.5062 12.3488 14.2209 12.4675 13.8878 12.4675C13.5546 12.4675 13.2694 12.3488 13.0322 12.1115C12.7949 11.8743 12.6763 11.5891 12.6763 11.2559Z" fill="white" fill-opacity="0.8"/>
          </svg>
        `;
        
        dragHandle.appendChild(dragIcon);
        this.subtitleElement.appendChild(dragHandle);

        if (originalText) {
          const originalDiv = document.createElement('div');
          originalDiv.className = 'lucid-subtitle-text original';
          originalDiv.textContent = originalText;
          this.subtitleElement.appendChild(originalDiv);
        }

        if (translatedText) {
          const translatedDiv = document.createElement('div');
          translatedDiv.className = 'lucid-subtitle-text translated';
          translatedDiv.textContent = translatedText;
          this.subtitleElement.appendChild(translatedDiv);
        }

        if (position) {
          this.subtitleElement.style.position = 'fixed';
          this.subtitleElement.style.left = position.x + 'px';
          this.subtitleElement.style.top = position.y + 'px';
          this.subtitleElement.style.transform = 'translateX(-50%)';
          this.subtitleElement.style.bottom = 'auto';
        }

        // 设置拖拽功能
        this.setupDragFunctionality(this.subtitleElement, dragHandle);

        // 查找视频容器
        const videoContainer = this.findVideoContainer();
        if (videoContainer && !position) {
          videoContainer.style.position = 'relative';
          videoContainer.appendChild(this.subtitleElement);
        } else {
          document.body.appendChild(this.subtitleElement);
        }

        requestAnimationFrame(() => {
          if (this.subtitleElement) {
            this.subtitleElement.classList.add('visible');
          }
        });
      }
      
      hideSubtitle() {
        if (this.subtitleElement) {
          this.subtitleElement.classList.remove('visible');
          
          setTimeout(() => {
            if (this.subtitleElement?.parentNode) {
              this.subtitleElement.parentNode.removeChild(this.subtitleElement);
            }
            this.subtitleElement = null;
          }, 300);
        }
      }
      
      showSettings() {
        this.hideSettings();

        this.settingsElement = document.createElement('div');
        this.settingsElement.className = 'lucid-settings-panel';
        this.settingsElement.id = 'lucid-settings-panel';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'lucid-settings-content';

        const items = [
          { icon: '⚙️', label: 'Lucid 字幕', type: 'toggle', enabled: true },
          { icon: '🌐', label: '主字幕', type: 'select', value: 'English' },
          { icon: '🔤', label: '翻译字幕', type: 'select', value: '中文' },
          { icon: '⚡', label: '翻译引擎', type: 'select', value: 'Microsoft' },
          { icon: '📺', label: '字幕显示', type: 'select', value: '双语字幕' },
          { icon: '🎨', label: '字幕样式', type: 'action' },
          { icon: '⌨️', label: '设置快捷键', type: 'action' }
        ];

        items.forEach((item, index) => {
          const itemElement = this.createSettingItem(item);
          contentDiv.appendChild(itemElement);
          
          if (index < items.length - 1) {
            const separator = document.createElement('div');
            separator.className = 'lucid-setting-separator';
            contentDiv.appendChild(separator);
          }
        });

        this.settingsElement.appendChild(contentDiv);
        document.body.appendChild(this.settingsElement);

        requestAnimationFrame(() => {
          if (this.settingsElement) {
            this.settingsElement.classList.add('visible');
          }
        });

        this.isSettingsVisible = true;
      }
      
      hideSettings() {
        if (this.settingsElement) {
          this.settingsElement.classList.remove('visible');
          
          setTimeout(() => {
            if (this.settingsElement?.parentNode) {
              this.settingsElement.parentNode.removeChild(this.settingsElement);
            }
            this.settingsElement = null;
          }, 200);
        }
        
        this.isSettingsVisible = false;
      }
      
      toggleSettings() {
        if (this.isSettingsVisible) {
          this.hideSettings();
        } else {
          this.showSettings();
        }
      }
      
      createSettingItem(item) {
        const element = document.createElement('div');
        element.className = item.type === 'action' ? 'lucid-setting-item clickable' : 'lucid-setting-item';

        const iconDiv = document.createElement('div');
        iconDiv.className = 'lucid-setting-icon';
        iconDiv.textContent = item.icon;

        const labelDiv = document.createElement('div');
        labelDiv.className = 'lucid-setting-label';
        labelDiv.textContent = item.label;

        element.appendChild(iconDiv);
        element.appendChild(labelDiv);

        if (item.type === 'toggle') {
          const toggle = this.createToggle(item.enabled);
          element.appendChild(toggle);
        } else if (item.type === 'select' || item.type === 'action') {
          const valueDiv = document.createElement('div');
          valueDiv.className = 'lucid-setting-value';
          
          if (item.value) {
            valueDiv.textContent = item.value;
          }
          
          const chevron = document.createElement('div');
          chevron.innerHTML = `
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
            </svg>
          `;
          valueDiv.appendChild(chevron);
          element.appendChild(valueDiv);
        }

        return element;
      }
      
      createToggle(enabled) {
        const toggle = document.createElement('div');
        toggle.className = 'lucid-toggle';

        const track = document.createElement('div');
        track.className = `lucid-toggle-track ${enabled ? 'enabled' : 'disabled'}`;

        const thumb = document.createElement('div');
        thumb.className = `lucid-toggle-thumb ${enabled ? 'enabled' : 'disabled'}`;

        track.appendChild(thumb);
        toggle.appendChild(track);

        toggle.addEventListener('click', () => {
          enabled = !enabled;
          track.className = `lucid-toggle-track ${enabled ? 'enabled' : 'disabled'}`;
          thumb.className = `lucid-toggle-thumb ${enabled ? 'enabled' : 'disabled'}`;
        });

        return toggle;
      }
    }
    
    // 创建管理器实例
    const directManager = new DirectSubtitleManagerDemo();
    
    // 全局函数
    function showBilingualSubtitle() {
      directManager.showSubtitle(
        "Is this really why everyone go crazy around Gemini?",
        "这真的是大家围绕双子座疯狂的原因吗？"
      );
    }
    
    function showLongSubtitle() {
      directManager.showSubtitle(
        "This is a much longer subtitle text that demonstrates how the component handles longer content and text wrapping in different screen sizes.",
        "这是一段更长的字幕文本，用于演示组件如何处理较长的内容以及在不同屏幕尺寸下的文本换行效果。"
      );
    }
    
    function showCustomPositionSubtitle() {
      directManager.showSubtitle(
        "Custom positioned subtitle",
        "自定义位置字幕",
        { x: 400, y: 200 }
      );
    }
    
    function testDragFunctionality() {
      directManager.showSubtitle(
        "Drag me! Hover to see drag handle",
        "拖拽我！鼠标悬停查看拖拽手柄"
      );
      
      // 提示用户hover查看拖拽手柄
      setTimeout(() => {
        alert('字幕已显示！\\n\\n操作说明：\\n1. 将鼠标悬停在字幕上\\n2. 会出现6点拖拽手柄\\n3. 只能拖拽6点手柄移动字幕\\n4. 字幕主体显示正常光标，不可拖拽');
      }, 500);
    }
    
    function hideSubtitle() {
      directManager.hideSubtitle();
    }
    
    function showSettings() {
      directManager.showSettings();
    }
    
    function hideSettings() {
      directManager.hideSettings();
    }
    
    function toggleSettings() {
      directManager.toggleSettings();
    }
    
    function testSettingsStability() {
      directManager.showSettings();
      
      setTimeout(() => {
        const message = directManager.isSettingsVisible ? 
          '✅ 设置面板稳定性测试通过！\\n面板显示正常，没有立即消失。' :
          '❌ 设置面板测试失败！\\n面板可能立即消失了。';
        alert(message);
      }, 1000);
    }
    
    function simulateMobile() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '375px';
      container.style.margin = '0 auto';
      container.style.border = '2px solid #333';
      container.style.minHeight = '100vh';
      updateSettingsPanelPosition();
    }
    
    function simulateTablet() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '768px';
      container.style.margin = '0 auto';
      container.style.border = '2px solid #333';
      container.style.minHeight = '100vh';
      updateSettingsPanelPosition();
    }
    
    function simulateDesktop() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '1200px';
      container.style.margin = '0 auto';
      container.style.border = 'none';
      container.style.minHeight = 'auto';
      updateSettingsPanelPosition();
    }
    
    function updateSettingsPanelPosition() {
      if (directManager.settingsElement) {
        // 重新计算设置面板位置
        const viewport = window.innerWidth;
        if (viewport <= 768) {
          directManager.settingsElement.style.right = '10px';
          directManager.settingsElement.style.left = '10px';
          directManager.settingsElement.style.width = 'auto';
        } else {
          directManager.settingsElement.style.right = '20px';
          directManager.settingsElement.style.left = 'auto';
          directManager.settingsElement.style.width = '282px';
        }
      }
    }
  </script>
</body>
</html>