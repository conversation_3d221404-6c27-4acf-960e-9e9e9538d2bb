<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>直接DOM字幕翻译UI组件演示</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #1E1E1E;
      color: white;
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #FFB700, #996E00);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .header p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 1.1rem;
    }
    
    .demo-section {
      margin-bottom: 60px;
    }
    
    .demo-section h2 {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: #FFC862;
    }
    
    .demo-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 30px;
    }
    
    button {
      background: linear-gradient(135deg, #FFB700, #996E00);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(255, 183, 0, 0.3);
    }
    
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 183, 0, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    .video-mock {
      width: 100%;
      height: 400px;
      background: #000;
      border-radius: 12px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20px;
      overflow: hidden;
    }
    
    .video-mock::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="20" fill="rgba(255,255,255,0.1)"/><path d="M45 35 L65 50 L45 65 Z" fill="rgba(255,255,255,0.3)"/></svg>') center/80px no-repeat;
    }
    
    .code-section {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
    }
    
    .code-section h3 {
      color: #FFC862;
      margin-bottom: 10px;
      font-size: 1.2rem;
    }
    
    pre {
      background: rgba(0, 0, 0, 0.3);
      padding: 15px;
      border-radius: 6px;
      overflow-x: auto;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .settings-trigger {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
    
    .keyboard-shortcuts {
      background: rgba(255, 200, 98, 0.1);
      border: 1px solid rgba(255, 200, 98, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }
    
    .keyboard-shortcuts h3 {
      color: #FFC862;
      margin-bottom: 10px;
    }
    
    .shortcut {
      display: flex;
      justify-content: space-between;
      margin: 5px 0;
      font-size: 14px;
    }
    
    .key {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
    
    .feature-highlight {
      background: rgba(0, 255, 127, 0.1);
      border: 1px solid rgba(0, 255, 127, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }
    
    .feature-highlight h3 {
      color: #00FF7F;
      margin-bottom: 10px;
      font-size: 1.3rem;
    }
    
    .feature-highlight p {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }
    
    @media (max-width: 768px) {
      .demo-controls {
        flex-direction: column;
      }
      
      button {
        width: 100%;
      }
      
      .video-mock {
        height: 250px;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Lucid 直接DOM字幕翻译演示</h1>
      <p>完全不使用Shadow DOM的纯DOM字幕翻译界面展示</p>
    </div>
    
    <div class="feature-highlight">
      <h3>🚀 全新直接DOM架构</h3>
      <p>
        此版本完全摒弃了Shadow DOM，使用直接DOM操作和CSS类名控制样式。
        所有UI组件都直接添加到页面DOM树中，通过精心设计的CSS类名系统确保样式不冲突。
        提供更好的性能和更简单的集成方式。
      </p>
    </div>
    
    <div class="demo-section">
      <h2>字幕显示组件</h2>
      <div class="demo-controls">
        <button onclick="showEnglishSubtitle()">显示英文字幕</button>
        <button onclick="showBilingualSubtitle()">显示双语字幕</button>
        <button onclick="showLongSubtitle()">显示长文本字幕</button>
        <button onclick="showCustomPositionSubtitle()">自定义位置字幕</button>
        <button onclick="hideSubtitle()">隐藏字幕</button>
      </div>
      
      <div class="video-mock" id="videoMock">
        <span>模拟视频播放器</span>
      </div>
      
      <div class="code-section">
        <h3>使用方式 - 直接DOM API</h3>
        <pre>
// 导入直接DOM管理器
import { directSubtitleManager } from '@features/subtitle-translation/DirectSubtitleManager';

// 显示双语字幕
directSubtitleManager.showSubtitle(
  "Is this really why everyone go crazy around Gemini?",
  "这真的是大家围绕双子座疯狂的原因吗？"
);

// 自定义位置显示
directSubtitleManager.showSubtitle(
  "Custom position subtitle",
  "自定义位置字幕",
  { x: 400, y: 200 }
);

// 隐藏字幕
directSubtitleManager.hideSubtitle();
        </pre>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>设置面板组件</h2>
      <div class="demo-controls">
        <button onclick="showSettings()">显示设置面板</button>
        <button onclick="hideSettings()">隐藏设置面板</button>
        <button onclick="toggleSettings()">切换设置面板</button>
      </div>
      
      <div class="keyboard-shortcuts">
        <h3>键盘快捷键</h3>
        <div class="shortcut">
          <span>切换设置面板</span>
          <span class="key">Ctrl + Shift + S</span>
        </div>
        <div class="shortcut">
          <span>隐藏所有UI</span>
          <span class="key">Esc</span>
        </div>
      </div>
      
      <div class="code-section">
        <h3>直接DOM架构优势</h3>
        <pre>
✅ 无Shadow DOM复杂性 - 直接操作DOM元素
✅ 更好的性能 - 减少DOM层级和内存占用
✅ 简化的样式系统 - 使用CSS类名，易于调试和修改
✅ 更好的可访问性 - 与页面DOM完全集成
✅ 简单的事件处理 - 直接使用标准DOM事件
✅ 易于测试和调试 - 可直接在开发者工具中查看

// 样式完全通过CSS类控制
.lucid-subtitle-overlay { /* 字幕样式 */ }
.lucid-settings-panel { /* 设置面板样式 */ }
.lucid-toggle { /* 开关组件样式 */ }
        </pre>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>响应式设计</h2>
      <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 20px;">
        直接DOM组件已适配不同屏幕尺寸，支持移动端和桌面端显示。响应式设计通过CSS媒体查询实现。
      </p>
      
      <div class="demo-controls">
        <button onclick="simulateMobile()">模拟移动端</button>
        <button onclick="simulateTablet()">模拟平板端</button>
        <button onclick="simulateDesktop()">模拟桌面端</button>
      </div>
      
      <div class="code-section">
        <h3>响应式CSS设计</h3>
        <pre>
/* 平板适配 */
@media (max-width: 768px) {
  .lucid-settings-panel {
    right: 10px;
    left: 10px;
    width: auto;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .lucid-subtitle-text {
    font-size: 14px;
    line-height: 20px;
  }
}
        </pre>
      </div>
    </div>
  </div>
  
  <div class="settings-trigger">
    <button onclick="toggleSettings()">⚙️ 设置</button>
  </div>
  
  <script>
    // 直接DOM字幕管理器的模拟实现
    class DirectSubtitleManagerDemo {
      constructor() {
        this.subtitleElement = null;
        this.settingsElement = null;
        this.isSettingsVisible = false;
        this.config = {
          enabled: true,
          displayMode: 'bilingual'
        };
        
        this.injectStyles();
        this.setupEventListeners();
      }
      
      injectStyles() {
        if (document.getElementById('lucid-subtitle-styles')) return;

        const styleElement = document.createElement('style');
        styleElement.id = 'lucid-subtitle-styles';
        styleElement.textContent = `
          /* 字幕显示样式 - 基于设计稿 */
          .lucid-subtitle-overlay {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            pointer-events: none;
            
            padding: 5.24px 3.90px 6.24px 3.90px;
            background: rgba(0, 0, 0, 0.70);
            border-radius: 12px;
            
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: 2.10px;
            
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(2px);
            
            min-width: 100px;
            max-width: 80vw;
            word-wrap: break-word;
            
            opacity: 0;
            transform: translateX(-50%) translateY(10px);
            transition: all 0.3s ease-out;
          }
          
          .lucid-subtitle-overlay.visible {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
          
          .lucid-subtitle-text {
            width: 100%;
            text-align: center;
            color: white;
            font-size: 15.60px;
            font-weight: 400;
            line-height: 21.86px;
            text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
            margin: 0;
            padding: 0;
            white-space: pre-wrap;
          }
          
          .lucid-subtitle-text.original {
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
          }
          
          .lucid-subtitle-text.translated {
            font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            margin-top: 2.10px;
          }
          
          /* 设置面板样式 - 基于设计稿毛玻璃效果 */
          .lucid-settings-panel {
            position: fixed;
            top: 70px;
            right: 20px;
            width: 282px;
            z-index: 10001;
            
            padding: 1px;
            background: rgba(28, 28, 28, 0.90);
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
            border-radius: 12px;
            outline: 1px rgba(255, 255, 255, 0.10) solid;
            outline-offset: -1px;
            backdrop-filter: blur(5px);
            
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
            transition: all 0.2s ease-out;
            pointer-events: none;
          }
          
          .lucid-settings-panel.visible {
            opacity: 1;
            transform: scale(1) translateY(0);
            pointer-events: auto;
          }
          
          .lucid-settings-content {
            width: 100%;
            display: flex;
            flex-direction: column;
          }
          
          .lucid-setting-item {
            width: 100%;
            height: 40px;
            padding: 0 8px 0 12px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            transition: background-color 0.2s ease;
          }
          
          .lucid-setting-item.clickable {
            cursor: pointer;
          }
          
          .lucid-setting-item.clickable:hover {
            background: rgba(255, 255, 255, 0.05);
          }
          
          .lucid-setting-icon {
            width: 28px;
            height: 20px;
            padding-right: 8px;
            display: flex;
            align-items: center;
            font-size: 16px;
          }
          
          .lucid-setting-label {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex: 1;
            
            color: rgba(255, 255, 255, 0.80);
            font-size: 13px;
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 13px;
            text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
            white-space: nowrap;
          }
          
          .lucid-setting-value {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 4px;
            
            max-width: 110px;
            padding-right: 4px;
            color: rgba(255, 255, 255, 0.90);
            font-size: 12.90px;
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 15.60px;
            text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          
          .lucid-setting-separator {
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, 0.08);
          }
          
          /* 开关组件样式 */
          .lucid-toggle {
            width: 56px;
            height: 38px;
            padding: 12px;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            cursor: pointer;
          }
          
          .lucid-toggle-track {
            width: 32px;
            height: 14px;
            border-radius: 7px;
            position: relative;
            transition: background-color 0.3s ease;
          }
          
          .lucid-toggle-track.enabled {
            background: rgba(255, 200, 98, 0.5);
          }
          
          .lucid-toggle-track.disabled {
            background: rgba(255, 255, 255, 0.3);
          }
          
          .lucid-toggle-thumb {
            width: 18px;
            height: 18px;
            border-radius: 9px;
            background: #FFC862;
            position: absolute;
            top: -2px;
            left: -1px;
            transition: transform 0.3s ease;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.2);
          }
          
          .lucid-toggle-thumb.enabled {
            transform: translateX(15px);
          }
          
          .lucid-toggle-thumb.disabled {
            transform: translateX(0);
          }
          
          /* 响应式适配 */
          @media (max-width: 768px) {
            .lucid-settings-panel {
              right: 10px;
              left: 10px;
              width: auto;
            }
            
            .lucid-subtitle-overlay {
              max-width: 90vw;
              bottom: 60px;
            }
          }
          
          @media (max-width: 480px) {
            .lucid-subtitle-text {
              font-size: 14px;
              line-height: 20px;
            }
            
            .lucid-subtitle-overlay {
              bottom: 40px;
            }
          }
        `;
        
        document.head.appendChild(styleElement);
      }
      
      setupEventListeners() {
        document.addEventListener('keydown', (event) => {
          if (event.ctrlKey && event.shiftKey && event.key === 'S') {
            event.preventDefault();
            this.toggleSettings();
          }
          
          if (event.key === 'Escape') {
            this.hideSettings();
          }
        });

        document.addEventListener('click', (event) => {
          if (this.isSettingsVisible && this.settingsElement) {
            if (!this.settingsElement.contains(event.target)) {
              this.hideSettings();
            }
          }
        });
      }
      
      showSubtitle(originalText, translatedText, position) {
        this.hideSubtitle();

        this.subtitleElement = document.createElement('div');
        this.subtitleElement.className = 'lucid-subtitle-overlay';
        this.subtitleElement.id = 'lucid-subtitle-overlay';

        if (originalText) {
          const originalDiv = document.createElement('div');
          originalDiv.className = 'lucid-subtitle-text original';
          originalDiv.textContent = originalText;
          this.subtitleElement.appendChild(originalDiv);
        }

        if (translatedText) {
          const translatedDiv = document.createElement('div');
          translatedDiv.className = 'lucid-subtitle-text translated';
          translatedDiv.textContent = translatedText;
          this.subtitleElement.appendChild(translatedDiv);
        }

        if (position) {
          this.subtitleElement.style.position = 'fixed';
          this.subtitleElement.style.left = position.x + 'px';
          this.subtitleElement.style.top = position.y + 'px';
          this.subtitleElement.style.transform = 'translateX(-50%)';
          this.subtitleElement.style.bottom = 'auto';
        }

        document.body.appendChild(this.subtitleElement);

        requestAnimationFrame(() => {
          if (this.subtitleElement) {
            this.subtitleElement.classList.add('visible');
          }
        });
      }
      
      hideSubtitle() {
        if (this.subtitleElement) {
          this.subtitleElement.classList.remove('visible');
          
          setTimeout(() => {
            if (this.subtitleElement?.parentNode) {
              this.subtitleElement.parentNode.removeChild(this.subtitleElement);
            }
            this.subtitleElement = null;
          }, 300);
        }
      }
      
      showSettings() {
        this.hideSettings();

        this.settingsElement = document.createElement('div');
        this.settingsElement.className = 'lucid-settings-panel';
        this.settingsElement.id = 'lucid-settings-panel';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'lucid-settings-content';

        const items = [
          { icon: '⚙️', label: 'Lucid 字幕', type: 'toggle', enabled: true },
          { icon: '🌐', label: '主字幕', type: 'select', value: 'English' },
          { icon: '🔤', label: '翻译字幕', type: 'select', value: '中文' },
          { icon: '⚡', label: '翻译引擎', type: 'select', value: 'Microsoft' },
          { icon: '📺', label: '字幕显示', type: 'select', value: '双语字幕' },
          { icon: '🎨', label: '字幕样式', type: 'action' },
          { icon: '⌨️', label: '设置快捷键', type: 'action' }
        ];

        items.forEach((item, index) => {
          const itemElement = this.createSettingItem(item);
          contentDiv.appendChild(itemElement);
          
          if (index < items.length - 1) {
            const separator = document.createElement('div');
            separator.className = 'lucid-setting-separator';
            contentDiv.appendChild(separator);
          }
        });

        this.settingsElement.appendChild(contentDiv);
        document.body.appendChild(this.settingsElement);

        requestAnimationFrame(() => {
          if (this.settingsElement) {
            this.settingsElement.classList.add('visible');
          }
        });

        this.isSettingsVisible = true;
      }
      
      hideSettings() {
        if (this.settingsElement) {
          this.settingsElement.classList.remove('visible');
          
          setTimeout(() => {
            if (this.settingsElement?.parentNode) {
              this.settingsElement.parentNode.removeChild(this.settingsElement);
            }
            this.settingsElement = null;
          }, 200);
        }
        
        this.isSettingsVisible = false;
      }
      
      toggleSettings() {
        if (this.isSettingsVisible) {
          this.hideSettings();
        } else {
          this.showSettings();
        }
      }
      
      createSettingItem(item) {
        const element = document.createElement('div');
        element.className = item.type === 'action' ? 'lucid-setting-item clickable' : 'lucid-setting-item';

        const iconDiv = document.createElement('div');
        iconDiv.className = 'lucid-setting-icon';
        iconDiv.textContent = item.icon;

        const labelDiv = document.createElement('div');
        labelDiv.className = 'lucid-setting-label';
        labelDiv.textContent = item.label;

        element.appendChild(iconDiv);
        element.appendChild(labelDiv);

        if (item.type === 'toggle') {
          const toggle = this.createToggle(item.enabled);
          element.appendChild(toggle);
        } else if (item.type === 'select' || item.type === 'action') {
          const valueDiv = document.createElement('div');
          valueDiv.className = 'lucid-setting-value';
          
          if (item.value) {
            valueDiv.textContent = item.value;
          }
          
          const chevron = document.createElement('div');
          chevron.innerHTML = `
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
            </svg>
          `;
          valueDiv.appendChild(chevron);
          element.appendChild(valueDiv);
        }

        return element;
      }
      
      createToggle(enabled) {
        const toggle = document.createElement('div');
        toggle.className = 'lucid-toggle';

        const track = document.createElement('div');
        track.className = `lucid-toggle-track ${enabled ? 'enabled' : 'disabled'}`;

        const thumb = document.createElement('div');
        thumb.className = `lucid-toggle-thumb ${enabled ? 'enabled' : 'disabled'}`;

        track.appendChild(thumb);
        toggle.appendChild(track);

        toggle.addEventListener('click', () => {
          enabled = !enabled;
          track.className = `lucid-toggle-track ${enabled ? 'enabled' : 'disabled'}`;
          thumb.className = `lucid-toggle-thumb ${enabled ? 'enabled' : 'disabled'}`;
        });

        return toggle;
      }
    }
    
    // 创建管理器实例
    const directManager = new DirectSubtitleManagerDemo();
    
    // 全局函数
    function showEnglishSubtitle() {
      directManager.showSubtitle("Is this really why everyone go crazy around Gemini?", "");
    }
    
    function showBilingualSubtitle() {
      directManager.showSubtitle(
        "Is this really why everyone go crazy around Gemini?",
        "这真的是大家围绕双子座疯狂的原因吗？"
      );
    }
    
    function showLongSubtitle() {
      directManager.showSubtitle(
        "This is a much longer subtitle text that demonstrates how the component handles longer content and text wrapping in different screen sizes.",
        "这是一段更长的字幕文本，用于演示组件如何处理较长的内容以及在不同屏幕尺寸下的文本换行效果。"
      );
    }
    
    function showCustomPositionSubtitle() {
      directManager.showSubtitle(
        "Custom positioned subtitle",
        "自定义位置字幕",
        { x: 400, y: 200 }
      );
    }
    
    function hideSubtitle() {
      directManager.hideSubtitle();
    }
    
    function showSettings() {
      directManager.showSettings();
    }
    
    function hideSettings() {
      directManager.hideSettings();
    }
    
    function toggleSettings() {
      directManager.toggleSettings();
    }
    
    function simulateMobile() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '375px';
      container.style.margin = '0 auto';
      container.style.border = '2px solid #333';
      container.style.minHeight = '100vh';
    }
    
    function simulateTablet() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '768px';
      container.style.margin = '0 auto';
      container.style.border = '2px solid #333';
      container.style.minHeight = '100vh';
    }
    
    function simulateDesktop() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '1200px';
      container.style.margin = '0 auto';
      container.style.border = 'none';
      container.style.minHeight = 'auto';
    }
  </script>
</body>
</html>