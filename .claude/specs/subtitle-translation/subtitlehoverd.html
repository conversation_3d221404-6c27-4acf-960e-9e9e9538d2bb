<div data-layer="window 覆盖整个播放器组件 相当于上面盖一层 透明 没有样式" class="Window" style="width: 1430px; height: 343px; position: relative; overflow: hidden">
  <div data-layer="Component 11" class="Component11" style="width: 1215.66px; height: 149.30px; left: 63.14px; top: 89.51px; position: absolute; background: rgba(30, 30, 30, 0.70); border-radius: 5px; border: 0.50px white solid">
    <div data-layer="variant=1,:hover=false" class="Variant1HoverFalse" style="height: 81.30px; padding-top: 22px; padding-left: 96.11px; padding-right: 96.11px; left: 30px; top: 30px; position: absolute; flex-direction: column; justify-content: center; align-items: center; display: inline-flex">
      <div data-layer="Overlay" class="Overlay" style="align-self: stretch; padding-top: 5.24px; padding-bottom: 6.24px; padding-left: 3.90px; padding-right: 3.90px; background: rgba(0, 0, 0, 0.70); border-radius: 12px; flex-direction: column; justify-content: center; align-items: flex-start; gap: 2.10px; display: flex">
        <div data-layer="Container" class="Container" style="align-self: stretch; padding-bottom: 0.86px; flex-direction: column; justify-content: flex-start; align-items: center; display: flex">
          <div data-layer="Is this really why everyone go crazy around Gemini?" class="IsThisReallyWhyEveryoneGoCrazyAroundGemini" style="align-self: stretch; text-align: center; justify-content: center; display: flex; flex-direction: column; color: white; font-size: 15.60px; font-family: Roboto; font-weight: 400; line-height: 21.86px; word-wrap: break-word; text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75)">Is this really why everyone go crazy around Gemini?</div>
        </div>
        <div data-layer="Container" class="Container" style="align-self: stretch; padding-bottom: 0.86px; flex-direction: column; justify-content: flex-start; align-items: center; display: flex">
          <div data-layer="这真的是大家围绕双子座疯狂的原因吗？" style="align-self: stretch; text-align: center; justify-content: center; display: flex; flex-direction: column; color: white; font-size: 15.60px; font-family: Inter; font-weight: 400; line-height: 21.86px; word-wrap: break-word; text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75)">这真的是大家围绕双子座疯狂的原因吗？</div>
        </div>
      </div>
    </div>
    <div data-layer="variant=1,:hover=true" class="Variant1HoverTrue" style="height: 89.30px; min-width: 562.83px; padding-left: 96.11px; padding-right: 96.11px; left: 622.83px; top: 30px; position: absolute; flex-direction: column; justify-content: center; align-items: center; gap: 5px; display: inline-flex">
      <div data-layer="Overlay+Border+OverlayBlur" class="OverlayBorderOverlayblur" style="height: 15px; padding-left: 9px; padding-right: 9px; padding-top: 1px; padding-bottom: 1px; background: rgba(0, 0, 0, 0.80); border-radius: 20px; outline: 1px rgba(255, 255, 255, 0.20) solid; outline-offset: -1px; backdrop-filter: blur(5px); justify-content: center; align-items: center; display: inline-flex">
        <div data-svg-wrapper data-layer="Component 1" data-variant="2" class="Component1" style="position: relative">
          <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M3.67627 6.75595C3.67627 6.42278 3.7949 6.13757 4.03214 5.90031C4.26941 5.66306 4.55462 5.54443 4.88778 5.54443C5.22095 5.54443 5.50616 5.66306 5.74342 5.90031C5.98067 6.13757 6.09929 6.42278 6.09929 6.75595C6.09929 7.08911 5.98067 7.37432 5.74342 7.61155C5.50616 7.84885 5.22095 7.96742 4.88778 7.96742C4.55462 7.96742 4.26941 7.84885 4.03214 7.61155C3.7949 7.37432 3.67627 7.08911 3.67627 6.75595ZM3.67627 11.2559C3.67627 10.9228 3.7949 10.6376 4.03214 10.4003C4.26941 10.163 4.55462 10.0444 4.88778 10.0444C5.22095 10.0444 5.50616 10.163 5.74342 10.4003C5.98067 10.6376 6.09929 10.9228 6.09929 11.2559C6.09929 11.5891 5.98067 11.8743 5.74342 12.1115C5.50616 12.3488 5.22095 12.4674 4.88778 12.4674C4.55462 12.4674 4.26941 12.3488 4.03214 12.1115C3.7949 11.8743 3.67627 11.5891 3.67627 11.2559ZM8.17631 6.75595C8.17631 6.42278 8.29488 6.13757 8.53218 5.90031C8.76941 5.66306 9.05463 5.54443 9.38778 5.54443C9.72093 5.54443 10.0062 5.66306 10.2435 5.90031C10.4807 6.13757 10.5993 6.42278 10.5993 6.75595C10.5993 7.08911 10.4807 7.37432 10.2435 7.61155C10.0062 7.84885 9.72093 7.96742 9.38778 7.96742C9.05463 7.96742 8.76941 7.84885 8.53218 7.61155C8.29488 7.37432 8.17631 7.08911 8.17631 6.75595ZM8.17631 11.2559C8.17631 10.9228 8.29488 10.6376 8.53218 10.4003C8.76941 10.163 9.05463 10.0444 9.38778 10.0444C9.72093 10.0444 10.0062 10.163 10.2435 10.4003C10.4807 10.6376 10.5993 10.9228 10.5993 11.2559C10.5993 11.5891 10.4807 11.8743 10.2435 12.1115C10.0062 12.3488 9.72093 12.4674 9.38778 12.4674C9.05463 12.4674 8.76941 12.3488 8.53218 12.1115C8.29488 11.8743 8.17631 11.5891 8.17631 11.2559ZM12.6763 6.75595C12.6763 6.42278 12.7949 6.13757 13.0322 5.90031C13.2694 5.66306 13.5546 5.54443 13.8878 5.54443C14.2209 5.54443 14.5062 5.66306 14.7435 5.90031C14.9807 6.13757 15.0993 6.42278 15.0993 6.75595C15.0993 7.08911 14.9807 7.37432 14.7435 7.61155C14.5062 7.84885 14.2209 7.96742 13.8878 7.96742C13.5546 7.96742 13.2694 7.84885 13.0322 7.61155C12.7949 7.37432 12.6763 7.08911 12.6763 6.75595ZM12.6763 11.2559C12.6763 10.9228 12.7949 10.6376 13.0322 10.4003C13.2694 10.163 13.5546 10.0444 13.8878 10.0444C14.2209 10.0444 14.5062 10.163 14.7435 10.4003C14.9807 10.6376 15.0993 10.9228 15.0993 11.2559C15.0993 11.5891 14.9807 11.8743 14.7435 12.1115C14.5062 12.3488 14.2209 12.4675 13.8878 12.4675C13.5546 12.4675 13.2694 12.3488 13.0322 12.1115C12.7949 11.8743 12.6763 11.5891 12.6763 11.2559Z" fill="white" fill-opacity="0.8"/>
          </svg>
        </div>
      </div>
      <div data-layer="Overlay" class="Overlay" style="align-self: stretch; padding-top: 5.24px; padding-bottom: 6.24px; padding-left: 3.90px; padding-right: 3.90px; background: rgba(0, 0, 0, 0.70); border-radius: 12px; flex-direction: column; justify-content: center; align-items: flex-start; gap: 2.10px; display: flex">
        <div data-layer="Container" class="Container" style="align-self: stretch; padding-bottom: 0.86px; flex-direction: column; justify-content: flex-start; align-items: center; display: flex">
          <div data-layer="Is this really why everyone go crazy around Gemini?" class="IsThisReallyWhyEveryoneGoCrazyAroundGemini" style="align-self: stretch; text-align: center; justify-content: center; display: flex; flex-direction: column; color: white; font-size: 15.60px; font-family: Roboto; font-weight: 400; line-height: 21.86px; word-wrap: break-word; text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75)">Is this really why everyone go crazy around Gemini?</div>
        </div>
        <div data-layer="Container" class="Container" style="align-self: stretch; padding-bottom: 0.86px; flex-direction: column; justify-content: flex-start; align-items: center; display: flex">
          <div data-layer="这真的是大家围绕双子座疯狂的原因吗？" style="align-self: stretch; text-align: center; justify-content: center; display: flex; flex-direction: column; color: white; font-size: 15.60px; font-family: Inter; font-weight: 400; line-height: 21.86px; word-wrap: break-word; text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75)">这真的是大家围绕双子座疯狂的原因吗？</div>
        </div>
      </div>
    </div>
  </div>
</div>