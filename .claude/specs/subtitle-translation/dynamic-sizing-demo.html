<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lucid字幕翻译 - 动态尺寸演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: white;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .demo-header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .video-container {
            background: #000;
            border-radius: 12px;
            margin: 20px auto;
            overflow: hidden;
            position: relative;
            resize: both;
        }

        .video-container.size-small {
            width: 400px;
            height: 225px;
        }

        .video-container.size-medium {
            width: 640px;
            height: 360px;
        }

        .video-container.size-large {
            width: 854px;
            height: 480px;
        }

        .video-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #333, #555);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #999;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 14px;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .size-controls {
            margin: 10px 0;
        }

        .demo-info {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid #4CAF50;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .size-display {
            font-family: monospace;
            color: #4CAF50;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>🎬 Lucid字幕翻译 - 动态尺寸演示</h1>
        <p>展示字幕窗口如何动态适配不同尺寸的视频播放器</p>
    </div>

    <div class="status-panel">
        <h3>📊 当前状态信息</h3>
        <div id="statusInfo">
            <div>拖拽功能：已启用</div>
            <div>视频容器检测：检测中...</div>
            <div>动态尺寸：已启用</div>
            <div>设置面板状态：已修复</div>
            <div class="size-display" id="sizeDisplay">容器尺寸：计算中...</div>
        </div>
    </div>

    <div class="demo-info">
        <h4>🔧 动态尺寸功能说明</h4>
        <ul>
            <li>字幕窗口会自动检测视频播放器的实际尺寸</li>
            <li>使用 getBoundingClientRect() 获取准确的容器尺寸</li>
            <li>字幕窗口设置为与播放器完全相同的宽度和高度</li>
            <li>支持播放器尺寸变化时的实时更新</li>
            <li>保持相对于视频容器的定位精度</li>
        </ul>
    </div>

    <div class="size-controls">
        <h4>📏 测试不同播放器尺寸</h4>
        <button onclick="changeVideoSize('small')">小尺寸 (400×225)</button>
        <button onclick="changeVideoSize('medium')">中尺寸 (640×360)</button>
        <button onclick="changeVideoSize('large')">大尺寸 (854×480)</button>
    </div>

    <div id="videoContainer" class="video-container size-medium">
        <div class="video-placeholder">
            🎥 模拟视频播放器<br>
            <small>可拖拽调整大小</small>
        </div>
    </div>

    <div class="controls">
        <button onclick="showSubtitle()">显示字幕</button>
        <button onclick="hideSubtitle()">隐藏字幕</button>
        <button onclick="updateSubtitleSize()">更新尺寸</button>
        <button onclick="toggleSettings()">设置面板</button>
        <button onclick="testDrag()">测试拖拽</button>
    </div>

    <script>
        // 简化的DirectSubtitleManager核心功能
        class DynamicSubtitleManager {
            constructor() {
                this.subtitleElement = null;
                this.isVisible = false;
                this.isDragging = false;
                this.settingsVisible = false;
                
                // 监听窗口大小变化
                window.addEventListener('resize', () => {
                    if (this.isVisible) {
                        this.updateSubtitleSize();
                    }
                });
                
                // 使用ResizeObserver监听容器大小变化
                this.setupResizeObserver();
            }

            setupResizeObserver() {
                if ('ResizeObserver' in window) {
                    this.resizeObserver = new ResizeObserver(entries => {
                        if (this.isVisible && this.subtitleElement) {
                            this.updateSubtitleSize();
                        }
                    });
                }
            }

            findVideoContainer() {
                return document.getElementById('videoContainer');
            }

            updateSubtitleSize() {
                const videoContainer = this.findVideoContainer();
                if (videoContainer && this.subtitleElement) {
                    const containerRect = videoContainer.getBoundingClientRect();
                    
                    // 动态设置字幕窗口尺寸
                    this.subtitleElement.style.width = `${containerRect.width}px`;
                    this.subtitleElement.style.height = `${containerRect.height}px`;
                    
                    // 更新状态显示
                    this.updateSizeDisplay(containerRect);
                    
                    console.log('字幕窗口尺寸已更新:', {
                        width: containerRect.width,
                        height: containerRect.height
                    });
                }
            }

            updateSizeDisplay(rect) {
                const sizeDisplay = document.getElementById('sizeDisplay');
                if (sizeDisplay) {
                    sizeDisplay.textContent = `容器尺寸：${Math.round(rect.width)}px × ${Math.round(rect.height)}px`;
                }
            }

            createSubtitleElement() {
                const subtitleWindow = document.createElement('div');
                subtitleWindow.className = 'lucid-caption-window';
                
                // 字幕窗口CSS
                subtitleWindow.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    pointer-events: none;
                    z-index: 9999;
                    background: rgba(255, 0, 0, 0.1);
                    border: 2px dashed rgba(76, 175, 80, 0.5);
                `;

                // 创建字幕容器
                const captionContainer = document.createElement('div');
                captionContainer.className = 'lucid-caption-container';
                captionContainer.style.cssText = `
                    position: absolute;
                    bottom: 30px;
                    left: 50%;
                    transform: translateX(-50%);
                    pointer-events: auto;
                `;

                // 创建字幕内容
                const caption = document.createElement('div');
                caption.className = 'lucid-caption';
                caption.style.cssText = `
                    background: rgba(0, 0, 0, 0.85);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: 500;
                    max-width: 600px;
                    text-align: center;
                    cursor: default;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    backdrop-filter: blur(10px);
                    position: relative;
                `;

                caption.innerHTML = `
                    <div style="margin-bottom: 8px; color: #fff;">
                        The quick brown fox jumps over the lazy dog
                    </div>
                    <div style="color: #4CAF50; font-size: 14px;">
                        敏捷的棕色狐狸跳过懒惰的狗
                    </div>
                `;

                // 创建拖拽手柄
                const dragHandle = document.createElement('div');
                dragHandle.className = 'lucid-caption-drag';
                dragHandle.style.cssText = `
                    position: absolute;
                    top: -15px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 24px;
                    height: 12px;
                    background: rgba(76, 175, 80, 0.9);
                    border-radius: 6px;
                    cursor: grab;
                    opacity: 0;
                    transition: opacity 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                `;

                // 添加拖拽点
                dragHandle.innerHTML = '⋮⋮';
                dragHandle.style.fontSize = '8px';
                dragHandle.style.color = 'white';
                dragHandle.style.letterSpacing = '2px';

                // 设置悬停显示逻辑
                const showHandle = () => dragHandle.style.opacity = '1';
                const hideHandle = () => {
                    if (!this.isDragging) {
                        dragHandle.style.opacity = '0';
                    }
                };

                caption.addEventListener('mouseenter', showHandle);
                caption.addEventListener('mouseleave', hideHandle);
                dragHandle.addEventListener('mouseenter', showHandle);
                dragHandle.addEventListener('mouseleave', hideHandle);

                // 组装DOM结构
                caption.appendChild(dragHandle);
                captionContainer.appendChild(caption);
                subtitleWindow.appendChild(captionContainer);

                // 设置拖拽功能
                this.setupDragFunctionality(subtitleWindow, dragHandle);

                return subtitleWindow;
            }

            setupDragFunctionality(element, dragHandle) {
                let isDragging = false;
                let startX, startY, startLeft, startTop;

                const startDrag = (e) => {
                    isDragging = true;
                    this.isDragging = true;
                    dragHandle.style.cursor = 'grabbing';
                    dragHandle.style.opacity = '1';

                    const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
                    const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

                    startX = clientX;
                    startY = clientY;

                    const container = element.querySelector('.lucid-caption-container');
                    const rect = container.getBoundingClientRect();
                    const videoContainer = this.findVideoContainer();
                    const videoRect = videoContainer ? videoContainer.getBoundingClientRect() : { left: 0, top: 0 };
                    
                    // 计算相对于视频容器的位置
                    startLeft = rect.left - videoRect.left;
                    startTop = rect.top - videoRect.top;

                    e.preventDefault();
                    e.stopPropagation();
                };

                const doDrag = (e) => {
                    if (!isDragging) return;

                    const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
                    const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;

                    const deltaX = clientX - startX;
                    const deltaY = clientY - startY;

                    const container = element.querySelector('.lucid-caption-container');
                    
                    // 使用相对于视频容器的坐标
                    const newLeft = startLeft + deltaX;
                    const newTop = startTop + deltaY;
                    
                    container.style.left = `${newLeft}px`;
                    container.style.top = `${newTop}px`;
                    container.style.transform = 'none';

                    e.preventDefault();
                };

                const stopDrag = () => {
                    if (isDragging) {
                        isDragging = false;
                        this.isDragging = false;
                        dragHandle.style.cursor = 'grab';
                        
                        setTimeout(() => {
                            if (!this.isDragging) {
                                dragHandle.style.opacity = '0';
                            }
                        }, 500);
                    }
                };

                // 鼠标事件
                dragHandle.addEventListener('mousedown', startDrag);
                document.addEventListener('mousemove', doDrag);
                document.addEventListener('mouseup', stopDrag);

                // 触摸事件
                dragHandle.addEventListener('touchstart', startDrag, { passive: false });
                document.addEventListener('touchmove', doDrag, { passive: false });
                document.addEventListener('touchend', stopDrag);
            }

            showSubtitle() {
                if (this.isVisible) return;

                this.subtitleElement = this.createSubtitleElement();
                
                const videoContainer = this.findVideoContainer();
                if (videoContainer) {
                    videoContainer.style.position = 'relative';
                    
                    // 动态计算并设置字幕窗口尺寸
                    const containerRect = videoContainer.getBoundingClientRect();
                    this.subtitleElement.style.width = `${containerRect.width}px`;
                    this.subtitleElement.style.height = `${containerRect.height}px`;
                    
                    // 更新状态显示
                    this.updateSizeDisplay(containerRect);
                    
                    videoContainer.appendChild(this.subtitleElement);
                    
                    // 开始监听容器大小变化
                    if (this.resizeObserver) {
                        this.resizeObserver.observe(videoContainer);
                    }
                } else {
                    document.body.appendChild(this.subtitleElement);
                }

                this.isVisible = true;

                // 触发显示动画
                requestAnimationFrame(() => {
                    if (this.subtitleElement) {
                        this.subtitleElement.style.opacity = '1';
                    }
                });

                console.log('字幕显示成功，尺寸已动态设置');
            }

            hideSubtitle() {
                if (!this.isVisible || !this.subtitleElement) return;

                // 停止监听容器大小变化
                if (this.resizeObserver && this.subtitleElement.parentElement) {
                    this.resizeObserver.unobserve(this.subtitleElement.parentElement);
                }

                this.subtitleElement.remove();
                this.subtitleElement = null;
                this.isVisible = false;

                // 更新状态显示
                const sizeDisplay = document.getElementById('sizeDisplay');
                if (sizeDisplay) {
                    sizeDisplay.textContent = '容器尺寸：未激活';
                }

                console.log('字幕已隐藏');
            }

            createSettingsPanel() {
                const settingsPanel = document.createElement('div');
                settingsPanel.className = 'lucid-settings-panel';
                settingsPanel.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 360px;
                    background: rgba(25, 25, 25, 0.95);
                    backdrop-filter: blur(20px);
                    border-radius: 16px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    z-index: 10000;
                    padding: 0;
                    overflow: hidden;
                `;

                settingsPanel.innerHTML = `
                    <div style="padding: 24px; border-bottom: 1px solid rgba(255, 255, 255, 0.1);">
                        <h3 style="margin: 0; color: white; font-size: 18px; font-weight: 600;">
                            ⚙️ 字幕设置
                        </h3>
                    </div>
                    <div style="padding: 20px;">
                        <div style="margin-bottom: 16px;">
                            <div style="color: white; font-size: 14px; margin-bottom: 8px;">动态尺寸</div>
                            <div style="background: rgba(76, 175, 80, 0.2); border: 1px solid #4CAF50; border-radius: 8px; padding: 12px; color: #4CAF50; font-size: 13px;">
                                ✓ 已启用 - 自动适配播放器尺寸
                            </div>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <div style="color: white; font-size: 14px; margin-bottom: 8px;">尺寸监听</div>
                            <div style="background: rgba(76, 175, 80, 0.2); border: 1px solid #4CAF50; border-radius: 8px; padding: 12px; color: #4CAF50; font-size: 13px;">
                                ✓ ResizeObserver 已激活
                            </div>
                        </div>
                        <div style="margin-bottom: 16px;">
                            <div style="color: white; font-size: 14px; margin-bottom: 8px;">拖拽功能</div>
                            <div style="background: rgba(76, 175, 80, 0.2); border: 1px solid #4CAF50; border-radius: 8px; padding: 12px; color: #4CAF50; font-size: 13px;">
                                ✓ 6点拖拽手柄已启用
                            </div>
                        </div>
                    </div>
                    <div style="padding: 16px 24px; background: rgba(255, 255, 255, 0.05); text-align: center;">
                        <button onclick="subtitleManager.hideSettings()" style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
                            关闭
                        </button>
                    </div>
                `;

                return settingsPanel;
            }

            showSettings() {
                if (this.settingsVisible) return;

                this.settingsPanel = this.createSettingsPanel();
                document.body.appendChild(this.settingsPanel);
                this.settingsVisible = true;

                // 点击外部关闭 - 使用延迟避免立即触发
                setTimeout(() => {
                    const closeOnClickOutside = (e) => {
                        if (!this.settingsPanel.contains(e.target)) {
                            this.hideSettings();
                            document.removeEventListener('click', closeOnClickOutside);
                        }
                    };
                    document.addEventListener('click', closeOnClickOutside);
                }, 100);
            }

            hideSettings() {
                if (!this.settingsVisible || !this.settingsPanel) return;

                this.settingsPanel.remove();
                this.settingsPanel = null;
                this.settingsVisible = false;
            }
        }

        // 创建管理器实例
        const subtitleManager = new DynamicSubtitleManager();

        // 全局函数
        function showSubtitle() {
            subtitleManager.showSubtitle();
        }

        function hideSubtitle() {
            subtitleManager.hideSubtitle();
        }

        function updateSubtitleSize() {
            subtitleManager.updateSubtitleSize();
        }

        function toggleSettings() {
            if (subtitleManager.settingsVisible) {
                subtitleManager.hideSettings();
            } else {
                subtitleManager.showSettings();
            }
        }

        function changeVideoSize(size) {
            const container = document.getElementById('videoContainer');
            container.className = `video-container size-${size}`;
            
            // 如果字幕正在显示，延迟更新尺寸以等待容器调整完成
            if (subtitleManager.isVisible) {
                setTimeout(() => {
                    subtitleManager.updateSubtitleSize();
                }, 100);
            }
        }

        function testDrag() {
            if (!subtitleManager.isVisible) {
                showSubtitle();
                setTimeout(() => {
                    alert('现在可以将鼠标悬停在字幕上，然后拖拽出现的6点手柄来移动字幕位置。');
                }, 500);
            } else {
                alert('请将鼠标悬停在字幕上，然后拖拽出现的6点手柄来移动字幕位置。');
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('DOMContentLoaded', () => {
            console.log('动态尺寸演示页面已加载');
            
            // 初始尺寸显示
            const container = document.getElementById('videoContainer');
            const rect = container.getBoundingClientRect();
            subtitleManager.updateSizeDisplay(rect);
            
            // 更新状态信息
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.innerHTML = `
                <div>拖拽功能：✅ 已启用</div>
                <div>视频容器检测：✅ 已检测到</div>
                <div>动态尺寸：✅ 已启用</div>
                <div>设置面板状态：✅ 已修复</div>
                <div class="size-display" id="sizeDisplay">容器尺寸：${Math.round(rect.width)}px × ${Math.round(rect.height)}px</div>
            `;
        });
    </script>
</body>
</html>