<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>字幕翻译UI组件演示</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #1E1E1E;
      color: white;
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #FFB700, #996E00);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .header p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 1.1rem;
    }
    
    .demo-section {
      margin-bottom: 60px;
    }
    
    .demo-section h2 {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: #FFC862;
    }
    
    .demo-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 30px;
    }
    
    button {
      background: linear-gradient(135deg, #FFB700, #996E00);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(255, 183, 0, 0.3);
    }
    
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 183, 0, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    .video-mock {
      width: 100%;
      height: 400px;
      background: #000;
      border-radius: 12px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20px;
      overflow: hidden;
    }
    
    .video-mock::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="20" fill="rgba(255,255,255,0.1)"/><path d="M45 35 L65 50 L45 65 Z" fill="rgba(255,255,255,0.3)"/></svg>') center/80px no-repeat;
    }
    
    .subtitle-area {
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      pointer-events: none;
    }
    
    .code-section {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
    }
    
    .code-section h3 {
      color: #FFC862;
      margin-bottom: 10px;
      font-size: 1.2rem;
    }
    
    pre {
      background: rgba(0, 0, 0, 0.3);
      padding: 15px;
      border-radius: 6px;
      overflow-x: auto;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .settings-trigger {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
    
    .keyboard-shortcuts {
      background: rgba(255, 200, 98, 0.1);
      border: 1px solid rgba(255, 200, 98, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }
    
    .keyboard-shortcuts h3 {
      color: #FFC862;
      margin-bottom: 10px;
    }
    
    .shortcut {
      display: flex;
      justify-content: space-between;
      margin: 5px 0;
      font-size: 14px;
    }
    
    .key {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
    
    @media (max-width: 768px) {
      .demo-controls {
        flex-direction: column;
      }
      
      button {
        width: 100%;
      }
      
      .video-mock {
        height: 250px;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Lucid 字幕翻译 UI 演示</h1>
      <p>基于设计稿的现代化字幕翻译界面展示</p>
    </div>
    
    <div class="demo-section">
      <h2>字幕显示组件</h2>
      <div class="demo-controls">
        <button onclick="showEnglishSubtitle()">显示英文字幕</button>
        <button onclick="showBilingualSubtitle()">显示双语字幕</button>
        <button onclick="showLongSubtitle()">显示长文本字幕</button>
        <button onclick="hideSubtitle()">隐藏字幕</button>
      </div>
      
      <div class="video-mock" id="videoMock">
        <span>模拟视频播放器</span>
        <div class="subtitle-area" id="subtitleArea"></div>
      </div>
      
      <div class="code-section">
        <h3>使用方式</h3>
        <pre>
// 显示双语字幕
subtitleTranslationManager.showSubtitle(
  "Is this really why everyone go crazy around Gemini?",
  "这真的是大家围绕双子座疯狂的原因吗？",
  { x: 400, y: 300 }
);

// 隐藏字幕
subtitleTranslationManager.hideSubtitle();
        </pre>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>设置面板组件</h2>
      <div class="demo-controls">
        <button onclick="showSettings()">显示设置面板</button>
        <button onclick="hideSettings()">隐藏设置面板</button>
        <button onclick="toggleSettings()">切换设置面板</button>
      </div>
      
      <div class="keyboard-shortcuts">
        <h3>键盘快捷键</h3>
        <div class="shortcut">
          <span>切换设置面板</span>
          <span class="key">Ctrl + Shift + S</span>
        </div>
        <div class="shortcut">
          <span>隐藏所有UI</span>
          <span class="key">Esc</span>
        </div>
      </div>
      
      <div class="code-section">
        <h3>设置项配置</h3>
        <pre>
const settingItems = [
  {
    id: 'subtitle-enabled',
    icon: SubtitleIcon,
    label: 'Lucid 字幕',
    type: 'toggle',
    enabled: true,
    onChange: (enabled) => console.log('Subtitle enabled:', enabled)
  },
  {
    id: 'primary-language',
    icon: LanguageIcon,
    label: '主字幕',
    type: 'select',
    value: 'English',
    options: [
      { label: 'English', value: 'English' },
      { label: '中文', value: '中文' }
    ]
  }
];
        </pre>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>响应式设计</h2>
      <p style="color: rgba(255, 255, 255, 0.7); margin-bottom: 20px;">
        组件已适配不同屏幕尺寸，支持移动端和桌面端显示。可以调整浏览器窗口大小观察效果。
      </p>
      
      <div class="demo-controls">
        <button onclick="simulateMobile()">模拟移动端</button>
        <button onclick="simulateTablet()">模拟平板端</button>
        <button onclick="simulateDesktop()">模拟桌面端</button>
      </div>
    </div>
  </div>
  
  <div class="settings-trigger">
    <button onclick="toggleSettings()">⚙️ 设置</button>
  </div>
  
  <script>
    // 模拟字幕翻译管理器
    class MockSubtitleManager {
      constructor() {
        this.subtitleVisible = false;
        this.settingsVisible = false;
      }
      
      showSubtitle(original, translated, position) {
        this.hideSubtitle(); // 先隐藏已有的
        
        const subtitleArea = document.getElementById('subtitleArea');
        const subtitleElement = document.createElement('div');
        subtitleElement.id = 'current-subtitle';
        subtitleElement.style.cssText = `
          padding: 5.24px 3.90px 6.24px 3.90px;
          background: rgba(0, 0, 0, 0.70);
          border-radius: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
          gap: 2.10px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
          backdrop-filter: blur(2px);
          min-width: 100px;
          max-width: 80vw;
          word-wrap: break-word;
          opacity: 0;
          transform: translateY(10px);
          animation: subtitleFadeIn 0.3s ease-out forwards;
        `;
        
        if (original) {
          const originalDiv = document.createElement('div');
          originalDiv.style.cssText = `
            width: 100%;
            text-align: center;
            color: white;
            font-size: 15.60px;
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 21.86px;
            text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
            margin: 0;
            padding: 0;
            white-space: pre-wrap;
          `;
          originalDiv.textContent = original;
          subtitleElement.appendChild(originalDiv);
        }
        
        if (translated) {
          const translatedDiv = document.createElement('div');
          translatedDiv.style.cssText = `
            width: 100%;
            text-align: center;
            color: white;
            font-size: 15.60px;
            font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 21.86px;
            text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
            margin: 0;
            padding: 0;
            white-space: pre-wrap;
            margin-top: ${original ? '2.10px' : '0'};
          `;
          translatedDiv.textContent = translated;
          subtitleElement.appendChild(translatedDiv);
        }
        
        subtitleArea.appendChild(subtitleElement);
        this.subtitleVisible = true;
      }
      
      hideSubtitle() {
        const currentSubtitle = document.getElementById('current-subtitle');
        if (currentSubtitle) {
          currentSubtitle.style.animation = 'subtitleFadeOut 0.3s ease-in forwards';
          setTimeout(() => {
            if (currentSubtitle.parentNode) {
              currentSubtitle.parentNode.removeChild(currentSubtitle);
            }
          }, 300);
        }
        this.subtitleVisible = false;
      }
      
      showSettings() {
        this.hideSettings(); // 先隐藏已有的
        
        const settingsPanel = document.createElement('div');
        settingsPanel.id = 'settings-panel';
        settingsPanel.style.cssText = `
          position: fixed;
          top: 70px;
          right: 20px;
          width: 282px;
          padding: 1px;
          background: rgba(28, 28, 28, 0.90);
          box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
          border-radius: 12px;
          outline: 1px rgba(255, 255, 255, 0.10) solid;
          outline-offset: -1px;
          backdrop-filter: blur(5px);
          z-index: 10001;
          opacity: 0;
          transform: scale(0.95) translateY(-10px);
          animation: panelFadeIn 0.2s ease-out forwards;
        `;
        
        settingsPanel.innerHTML = `
          <div style="width: 100%; display: flex; flex-direction: column;">
            ${this.createSettingItem('⚙️', 'Lucid 字幕', 'toggle', true)}
            ${this.createSeparator()}
            ${this.createSettingItem('🌐', '主字幕', 'select', 'English')}
            ${this.createSeparator()}
            ${this.createSettingItem('🔤', '翻译字幕', 'select', '中文')}
            ${this.createSeparator()}
            ${this.createSettingItem('⚡', '翻译引擎', 'select', 'Microsoft')}
            ${this.createSeparator()}
            ${this.createSettingItem('📺', '字幕显示', 'select', '双语字幕')}
            ${this.createSeparator()}
            ${this.createSettingItem('🎨', '字幕样式', 'action')}
            ${this.createSeparator()}
            ${this.createSettingItem('⌨️', '设置快捷键', 'action')}
          </div>
        `;
        
        document.body.appendChild(settingsPanel);
        this.settingsVisible = true;
        
        // 根据当前容器状态调整设置面板位置
        const container = document.querySelector('.container');
        const containerWidth = container.style.maxWidth;
        if (containerWidth === '375px') {
          updateSettingsPanelPosition('mobile');
        } else if (containerWidth === '768px') {
          updateSettingsPanelPosition('tablet');
        } else {
          updateSettingsPanelPosition('desktop');
        }
        
        // 点击外部关闭
        setTimeout(() => {
          document.addEventListener('click', this.handleClickOutside.bind(this));
        }, 100);
      }
      
      createSettingItem(icon, label, type, value) {
        const hasValue = type === 'select';
        const isToggle = type === 'toggle';
        const hasChevron = type === 'select' || type === 'action';
        
        return `
          <div style="
            width: 100%;
            height: 40px;
            padding: 0 8px 0 12px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            cursor: ${type === 'action' ? 'pointer' : 'default'};
            transition: background-color 0.2s ease;
          " ${type === 'action' ? 'onmouseover="this.style.background=\'rgba(255,255,255,0.05)\'" onmouseout="this.style.background=\'transparent\'"' : ''}>
            <div style="width: 28px; height: 20px; padding-right: 8px; display: flex; align-items: center;">
              <div style="width: 20px; height: 20px; display: flex; justify-content: center; align-items: center; font-size: 16px;">
                ${icon}
              </div>
            </div>
            <div style="display: flex; justify-content: flex-start; align-items: center; flex: 1;">
              <div style="
                color: rgba(255, 255, 255, 0.80);
                font-size: 13px;
                font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
                font-weight: 400;
                line-height: 13px;
                text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
                white-space: nowrap;
              ">${label}</div>
            </div>
            <div style="display: flex; justify-content: flex-end; align-items: center; gap: 4px;">
              ${isToggle ? this.createToggle(value) : ''}
              ${hasValue ? `
                <div style="
                  max-width: 110px;
                  padding-right: 4px;
                  color: rgba(255, 255, 255, 0.90);
                  font-size: 12.90px;
                  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
                  font-weight: 400;
                  line-height: 15.60px;
                  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                ">${value}</div>
              ` : ''}
              ${hasChevron ? `
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
                </svg>
              ` : ''}
            </div>
          </div>
        `;
      }
      
      createToggle(enabled) {
        return `
          <div style="
            width: 56px;
            height: 38px;
            padding: 12px;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            cursor: pointer;
          ">
            <div style="
              width: 32px;
              height: 14px;
              border-radius: 7px;
              background: ${enabled ? 'rgba(255, 200, 98, 0.5)' : 'rgba(255, 255, 255, 0.3)'};
              position: relative;
              transition: background-color 0.3s ease;
            ">
              <div style="
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background: #FFC862;
                position: absolute;
                top: -2px;
                left: -1px;
                transform: ${enabled ? 'translateX(15px)' : 'translateX(0)'};
                transition: transform 0.3s ease;
                box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.2);
              "></div>
            </div>
          </div>
        `;
      }
      
      createSeparator() {
        return '<div style="width: 100%; height: 1px; background: rgba(255, 255, 255, 0.08);"></div>';
      }
      
      hideSettings() {
        const settingsPanel = document.getElementById('settings-panel');
        if (settingsPanel) {
          settingsPanel.style.animation = 'panelFadeOut 0.2s ease-in forwards';
          setTimeout(() => {
            if (settingsPanel.parentNode) {
              settingsPanel.parentNode.removeChild(settingsPanel);
            }
          }, 200);
        }
        this.settingsVisible = false;
        document.removeEventListener('click', this.handleClickOutside.bind(this));
      }
      
      handleClickOutside(event) {
        const settingsPanel = document.getElementById('settings-panel');
        if (settingsPanel && !settingsPanel.contains(event.target)) {
          this.hideSettings();
        }
      }
      
      toggleSettings() {
        if (this.settingsVisible) {
          this.hideSettings();
        } else {
          this.showSettings();
        }
      }
    }
    
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes subtitleFadeIn {
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes subtitleFadeOut {
        to {
          opacity: 0;
          transform: translateY(10px);
        }
      }
      
      @keyframes panelFadeIn {
        to {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }
      
      @keyframes panelFadeOut {
        to {
          opacity: 0;
          transform: scale(0.95) translateY(-10px);
        }
      }
    `;
    document.head.appendChild(style);
    
    // 创建管理器实例
    const mockManager = new MockSubtitleManager();
    
    // 根据屏幕尺寸调整设置面板位置 - 提升为全局函数
    function updateSettingsPanelPosition(screenType) {
      const settingsPanel = document.getElementById('settings-panel');
      if (!settingsPanel) return;
      
      switch (screenType) {
        case 'mobile':
          settingsPanel.style.position = 'fixed';
          settingsPanel.style.top = '70px';
          settingsPanel.style.right = '10px';
          settingsPanel.style.left = '10px';
          settingsPanel.style.width = 'auto';
          break;
        case 'tablet':
          settingsPanel.style.position = 'fixed';
          settingsPanel.style.top = '70px';
          settingsPanel.style.right = '20px';
          settingsPanel.style.left = 'auto';
          settingsPanel.style.width = '282px';
          break;
        case 'desktop':
        default:
          settingsPanel.style.position = 'fixed';
          settingsPanel.style.top = '70px';
          settingsPanel.style.right = '20px';
          settingsPanel.style.left = 'auto';
          settingsPanel.style.width = '282px';
          break;
      }
    }
    
    // 全局函数
    function showEnglishSubtitle() {
      mockManager.showSubtitle("Is this really why everyone go crazy around Gemini?", "", { x: 400, y: 300 });
    }
    
    function showBilingualSubtitle() {
      mockManager.showSubtitle(
        "Is this really why everyone go crazy around Gemini?",
        "这真的是大家围绕双子座疯狂的原因吗？",
        { x: 400, y: 300 }
      );
    }
    
    function showLongSubtitle() {
      mockManager.showSubtitle(
        "This is a much longer subtitle text that demonstrates how the component handles longer content and text wrapping in different screen sizes.",
        "这是一段更长的字幕文本，用于演示组件如何处理较长的内容以及在不同屏幕尺寸下的文本换行效果。",
        { x: 400, y: 300 }
      );
    }
    
    function hideSubtitle() {
      mockManager.hideSubtitle();
    }
    
    function showSettings() {
      mockManager.showSettings();
    }
    
    function hideSettings() {
      mockManager.hideSettings();
    }
    
    function toggleSettings() {
      mockManager.toggleSettings();
    }
    
    function simulateMobile() {
      // 使用容器包装而不是直接修改body，避免影响fixed定位元素
      const container = document.querySelector('.container');
      container.style.maxWidth = '375px';
      container.style.margin = '0 auto';
      container.style.border = '2px solid #333';
      container.style.minHeight = '100vh';
      
      // 调整设置面板位置适配移动端
      updateSettingsPanelPosition('mobile');
    }
    
    function simulateTablet() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '768px';
      container.style.margin = '0 auto';
      container.style.border = '2px solid #333';
      container.style.minHeight = '100vh';
      
      // 调整设置面板位置适配平板端
      updateSettingsPanelPosition('tablet');
    }
    
    function simulateDesktop() {
      const container = document.querySelector('.container');
      container.style.maxWidth = '1200px';
      container.style.margin = '0 auto';
      container.style.border = 'none';
      container.style.minHeight = 'auto';
      
      // 恢复设置面板默认位置
      updateSettingsPanelPosition('desktop');
    }
    
    // 键盘快捷键
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        toggleSettings();
      }
      
      if (event.key === 'Escape') {
        hideSettings();
        hideSubtitle();
      }
    });
  </script>
</body>
</html>