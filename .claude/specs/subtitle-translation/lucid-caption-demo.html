<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lucid Caption Window - 新架构演示</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #1E1E1E;
      color: white;
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #FFB700, #996E00);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .header p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 1.1rem;
    }
    
    .demo-section {
      margin-bottom: 60px;
    }
    
    .demo-section h2 {
      font-size: 1.8rem;
      margin-bottom: 20px;
      color: #FFC862;
    }
    
    .demo-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 30px;
    }
    
    button {
      background: linear-gradient(135deg, #FFB700, #996E00);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(255, 183, 0, 0.3);
    }
    
    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 183, 0, 0.4);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    .video-mock {
      width: 100%;
      height: 400px;
      background: #000;
      border-radius: 12px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: rgba(255, 255, 255, 0.5);
      margin-bottom: 20px;
      overflow: hidden;
    }
    
    .video-mock::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="20" fill="rgba(255,255,255,0.1)"/><path d="M45 35 L65 50 L45 65 Z" fill="rgba(255,255,255,0.3)"/></svg>') center/80px no-repeat;
    }
    
    .feature-highlight {
      background: rgba(0, 255, 127, 0.1);
      border: 1px solid rgba(0, 255, 127, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }
    
    .feature-highlight h3 {
      color: #00FF7F;
      margin-bottom: 10px;
      font-size: 1.3rem;
    }
    
    .feature-highlight p {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }
    
    .structure-comparison {
      background: rgba(138, 43, 226, 0.1);
      border: 1px solid rgba(138, 43, 226, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }
    
    .structure-comparison h3 {
      color: #8A2BE2;
      margin-bottom: 10px;
    }
    
    .structure-item {
      margin: 10px 0;
      font-family: monospace;
      font-size: 13px;
      line-height: 1.4;
    }
    
    .old-structure {
      color: #ff6b6b;
    }
    
    .new-structure {
      color: #00FF7F;
    }
    
    .settings-trigger {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
    }
    
    .keyboard-shortcuts {
      background: rgba(255, 200, 98, 0.1);
      border: 1px solid rgba(255, 200, 98, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin-top: 20px;
    }
    
    .keyboard-shortcuts h3 {
      color: #FFC862;
      margin-bottom: 10px;
    }
    
    .shortcut {
      display: flex;
      justify-content: space-between;
      margin: 5px 0;
      font-size: 14px;
    }
    
    .key {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
    
    @media (max-width: 768px) {
      .demo-controls {
        flex-direction: column;
      }
      
      button {
        width: 100%;
      }
      
      .video-mock {
        height: 250px;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Lucid Caption Window - 新架构演示</h1>
      <p>基于 lucid-caption-window 结构的字幕翻译组件</p>
    </div>
    
    <div class="feature-highlight">
      <h3>🏗️ 新类名架构优势</h3>
      <p>
        <strong>结构化命名：</strong> 采用 lucid-caption-window > lucid-caption-container > lucid-caption 层级结构<br/>
        <strong>语义化清晰：</strong> window(窗口) > container(容器) > caption(字幕) 逻辑层次分明<br/>
        <strong>6点拖拽手柄：</strong> lucid-caption-drag 专门的拖拽控制器，hover时显示<br/>
        <strong>样式一致性：</strong> 与用户实际实现结构保持一致，便于集成和维护
      </p>
    </div>
    
    <div class="structure-comparison">
      <h3>类名结构对比</h3>
      <div class="structure-item old-structure">
        旧结构: .lucid-subtitle-overlay > .lucid-subtitle-text
      </div>
      <div class="structure-item new-structure">
        新结构: .lucid-caption-window > .lucid-caption-container > .lucid-caption > .lucid-caption-content
      </div>
      <div class="structure-item new-structure">
        拖拽组件: .lucid-caption > .lucid-caption-drag > .lucid-svg-icon
      </div>
      <div class="structure-item new-structure">
        字幕文本: .lucid-caption-primary / .lucid-caption-secondary
      </div>
    </div>
    
    <div class="demo-section">
      <h2>字幕显示 & 新架构测试</h2>
      <div class="demo-controls">
        <button onclick="showBilingualSubtitle()">显示双语字幕</button>
        <button onclick="showLongSubtitle()">显示长文本字幕</button>
        <button onclick="showCustomPositionSubtitle()">自定义位置字幕</button>
        <button onclick="testDragFunctionality()">测试拖拽功能</button>
        <button onclick="hideSubtitle()">隐藏字幕</button>
      </div>
      
      <div class="video-mock" id="videoMock">
        <span>模拟视频播放器 - 字幕将使用新的 lucid-caption-window 架构</span>
      </div>
      
      <div class="keyboard-shortcuts">
        <h3>新架构特性</h3>
        <div class="shortcut">
          <span>字幕窗口层：lucid-caption-window</span>
          <span class="key">最外层容器</span>
        </div>
        <div class="shortcut">
          <span>字幕容器层：lucid-caption-container</span>
          <span class="key">布局容器</span>
        </div>
        <div class="shortcut">
          <span>字幕主体层：lucid-caption</span>
          <span class="key">内容容器</span>
        </div>
        <div class="shortcut">
          <span>拖拽控制器：lucid-caption-drag</span>
          <span class="key">6点拖拽手柄</span>
        </div>
        <div class="shortcut">
          <span>字幕内容层：lucid-caption-content</span>
          <span class="key">文本容器</span>
        </div>
      </div>
    </div>
    
    <div class="demo-section">
      <h2>设置面板测试</h2>
      <div class="demo-controls">
        <button onclick="showSettings()">显示设置面板</button>
        <button onclick="hideSettings()">隐藏设置面板</button>
        <button onclick="toggleSettings()">切换设置面板</button>
      </div>
      
      <div class="keyboard-shortcuts">
        <h3>键盘快捷键</h3>
        <div class="shortcut">
          <span>切换设置面板</span>
          <span class="key">Ctrl + Shift + S</span>
        </div>
        <div class="shortcut">
          <span>隐藏所有UI</span>
          <span class="key">Esc</span>
        </div>
      </div>
    </div>
  </div>
  
  <div class="settings-trigger">
    <button onclick="toggleSettings()">⚙️ 设置</button>
  </div>
  
  <script>
    // 基于新类名结构的DirectSubtitleManager演示实现
    class LucidCaptionManagerDemo {
      constructor() {
        this.subtitleElement = null;
        this.settingsElement = null;
        this.isSettingsVisible = false;
        this.config = {
          enabled: true,
          displayMode: 'bilingual'
        };
        
        this.injectStyles();
        this.setupEventListeners();
      }
      
      injectStyles() {
        if (document.getElementById('lucid-subtitle-styles')) return;

        const styleElement = document.createElement('style');
        styleElement.id = 'lucid-subtitle-styles';
        styleElement.textContent = `
          /* 字幕窗口容器 - 基于用户实际结构 */
          .lucid-caption-window {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            pointer-events: auto;
            
            opacity: 0;
            transform: translateX(-50%) translateY(10px);
            transition: all 0.3s ease-out;
          }
          
          .lucid-caption-window.visible {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
          
          .lucid-caption-window.dragging {
            transition: none;
            user-select: none;
          }
          
          /* 字幕容器 */
          .lucid-caption-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
          }
          
          /* 主字幕组件 */
          .lucid-caption {
            position: relative;
            cursor: default; /* 字幕主体显示正常光标 */
            min-width: 100px;
            max-width: 80vw;
            word-wrap: break-word;
          }
          
          /* 拖拽控制器 - hover时显示 */
          .lucid-caption-drag {
            position: absolute;
            top: -25px; /* 稍微向上移动一点，增加与字幕的连接 */
            left: 50%;
            transform: translateX(-50%);
            
            height: 20px; /* 增加高度提升触发区域 */
            padding: 2px 9px; /* 增加上下padding */
            background: rgba(0, 0, 0, 0.80);
            border-radius: 20px;
            outline: 1px rgba(255, 255, 255, 0.20) solid;
            outline-offset: -1px;
            backdrop-filter: blur(5px);
            
            display: flex;
            justify-content: center;
            align-items: center;
            
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
            cursor: grab;
          }
          
          .lucid-caption-drag:active {
            cursor: grabbing;
          }
          
          .lucid-caption:hover .lucid-caption-drag,
          .lucid-caption-drag:hover {
            opacity: 1;
            pointer-events: auto;
          }
          
          .lucid-svg-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
          }
          
          .lucid-svg-icon.icon-18 {
            width: 18px;
            height: 18px;
          }
          
          /* 字幕内容容器 */
          .lucid-caption-content {
            padding: 5.24px 3.90px 6.24px 3.90px;
            background: rgba(0, 0, 0, 0.70);
            border-radius: 12px;
            
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: 2.10px;
            
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(2px);
          }
          
          /* 文本阴影工具类 */
          .lucid-text-shadow {
            text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
          }
          
          /* 主字幕文本 */
          .lucid-caption-primary {
            width: 100%;
            text-align: center;
            color: white;
            font-size: 15.60px;
            font-weight: 400;
            line-height: 21.86px;
            margin: 0;
            padding: 0;
            white-space: pre-wrap;
          }
          
          .lucid-caption-primary.inter {
            font-family: 'Inter', 'Microsoft YaHei', sans-serif;
          }
          
          .lucid-caption-primary.roboto {
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
          }
          
          /* 翻译字幕文本 */
          .lucid-caption-secondary {
            width: 100%;
            text-align: center;
            color: white;
            font-size: 15.60px;
            font-weight: 400;
            line-height: 21.86px;
            margin: 0;
            padding: 0;
            white-space: pre-wrap;
            margin-top: 2.10px;
          }
          
          .lucid-caption-secondary.inter {
            font-family: 'Inter', 'Microsoft YaHei', sans-serif;
          }
          
          .lucid-caption-secondary.roboto {
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
          }
          
          /* 设置面板样式 - 基于设计稿毛玻璃效果 */
          .lucid-settings-panel {
            position: fixed;
            top: 70px;
            right: 20px;
            width: 282px;
            z-index: 10001;
            
            padding: 1px;
            background: rgba(28, 28, 28, 0.90);
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
            border-radius: 12px;
            outline: 1px rgba(255, 255, 255, 0.10) solid;
            outline-offset: -1px;
            backdrop-filter: blur(5px);
            
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
            transition: all 0.2s ease-out;
            pointer-events: none;
          }
          
          .lucid-settings-panel.visible {
            opacity: 1;
            transform: scale(1) translateY(0);
            pointer-events: auto;
          }
          
          .lucid-settings-content {
            width: 100%;
            display: flex;
            flex-direction: column;
          }
          
          .lucid-setting-item {
            width: 100%;
            height: 40px;
            padding: 0 8px 0 12px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            transition: background-color 0.2s ease;
          }
          
          .lucid-setting-item.clickable {
            cursor: pointer;
          }
          
          .lucid-setting-item.clickable:hover {
            background: rgba(255, 255, 255, 0.05);
          }
          
          .lucid-setting-icon {
            width: 28px;
            height: 20px;
            padding-right: 8px;
            display: flex;
            align-items: center;
            font-size: 16px;
          }
          
          .lucid-setting-label {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex: 1;
            
            color: rgba(255, 255, 255, 0.80);
            font-size: 13px;
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 13px;
            text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
            white-space: nowrap;
          }
          
          .lucid-setting-value {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 4px;
            
            max-width: 110px;
            padding-right: 4px;
            color: rgba(255, 255, 255, 0.90);
            font-size: 12.90px;
            font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
            font-weight: 400;
            line-height: 15.60px;
            text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          
          .lucid-setting-separator {
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, 0.08);
          }
          
          /* 开关组件样式 */
          .lucid-toggle {
            width: 56px;
            height: 38px;
            padding: 12px;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            cursor: pointer;
          }
          
          .lucid-toggle-track {
            width: 32px;
            height: 14px;
            border-radius: 7px;
            position: relative;
            transition: background-color 0.3s ease;
          }
          
          .lucid-toggle-track.enabled {
            background: rgba(255, 200, 98, 0.5);
          }
          
          .lucid-toggle-track.disabled {
            background: rgba(255, 255, 255, 0.3);
          }
          
          .lucid-toggle-thumb {
            width: 18px;
            height: 18px;
            border-radius: 9px;
            background: #FFC862;
            position: absolute;
            top: -2px;
            left: -1px;
            transition: transform 0.3s ease;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.2);
          }
          
          .lucid-toggle-thumb.enabled {
            transform: translateX(15px);
          }
          
          .lucid-toggle-thumb.disabled {
            transform: translateX(0);
          }
          
          /* 响应式适配 */
          @media (max-width: 768px) {
            .lucid-settings-panel {
              right: 10px;
              left: 10px;
              width: auto;
            }
            
            .lucid-caption-window {
              max-width: 90%;
              bottom: 20px;
            }
          }
          
          @media (max-width: 480px) {
            .lucid-caption-primary,
            .lucid-caption-secondary {
              font-size: 14px;
              line-height: 20px;
            }
            
            .lucid-caption-window {
              bottom: 15px;
            }
          }
        `;
        
        document.head.appendChild(styleElement);
      }
      
      setupEventListeners() {
        document.addEventListener('keydown', (event) => {
          if (event.ctrlKey && event.shiftKey && event.key === 'S') {
            event.preventDefault();
            this.toggleSettings();
          }
          
          if (event.key === 'Escape') {
            this.hideSettings();
          }
        });

        document.addEventListener('click', (event) => {
          setTimeout(() => {
            if (this.isSettingsVisible && this.settingsElement) {
              const target = event.target;
              if (!this.settingsElement.contains(target)) {
                const isSettingsButton = target.closest('[onclick*="Settings"]') ||
                                       target.closest('.settings-trigger');
                
                if (!isSettingsButton) {
                  this.hideSettings();
                }
              }
            }
          }, 100);
        });
      }
      
      findVideoContainer() {
        const videoSelectors = [
          '#videoMock',
          '.video-mock',
          'video',
          '.video-player',
          '.video-container',
          '.player-container',
          '.video-wrapper'
        ];

        for (const selector of videoSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            const rect = element.getBoundingClientRect();
            if (rect.width > 200 && rect.height > 150) {
              return element;
            }
          }
        }

        return null;
      }
      
      setupDragFunctionality(subtitleElement, dragHandle) {
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let initialX = 0;
        let initialY = 0;

        // 鼠标按下开始拖拽 - 只在拖拽手柄上监听
        const handleMouseDown = (e) => {
          isDragging = true;
          subtitleElement.classList.add('dragging');
          dragHandle.style.cursor = 'grabbing';
          
          startX = e.clientX;
          startY = e.clientY;
          
          const rect = subtitleElement.getBoundingClientRect();
          initialX = rect.left + rect.width / 2;
          initialY = rect.top;
          
          e.preventDefault();
          e.stopPropagation(); // 防止事件冒泡到字幕主体
          
          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
        };

        const handleMouseMove = (e) => {
          if (!isDragging) return;
          
          const deltaX = e.clientX - startX;
          const deltaY = e.clientY - startY;
          
          const newX = initialX + deltaX;
          const newY = initialY + deltaY;
          
          subtitleElement.style.position = 'fixed';
          subtitleElement.style.left = `${newX}px`;
          subtitleElement.style.top = `${newY}px`;
          subtitleElement.style.transform = 'translateX(-50%)';
          subtitleElement.style.bottom = 'auto';
        };

        const handleMouseUp = () => {
          isDragging = false;
          subtitleElement.classList.remove('dragging');
          dragHandle.style.cursor = 'grab';
          
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
        };

        // 只在拖拽手柄上添加拖拽事件
        dragHandle.addEventListener('mousedown', handleMouseDown);
        
        // 确保字幕主体不可拖拽
        const caption = subtitleElement.querySelector('.lucid-caption');
        if (caption) {
          caption.addEventListener('mousedown', (e) => {
            // 如果点击的不是拖拽手柄，阻止任何拖拽行为
            if (!dragHandle.contains(e.target)) {
              e.stopPropagation();
            }
          });
        }
        
        // 触摸事件支持（移动端）- 同样只在拖拽手柄上
        const handleTouchStart = (e) => {
          if (e.touches.length !== 1) return;
          
          const touch = e.touches[0];
          isDragging = true;
          subtitleElement.classList.add('dragging');
          
          startX = touch.clientX;
          startY = touch.clientY;
          
          const rect = subtitleElement.getBoundingClientRect();
          initialX = rect.left + rect.width / 2;
          initialY = rect.top;
          
          e.preventDefault();
          e.stopPropagation();
          
          document.addEventListener('touchmove', handleTouchMove, { passive: false });
          document.addEventListener('touchend', handleTouchEnd);
        };

        const handleTouchMove = (e) => {
          if (!isDragging || e.touches.length !== 1) return;
          
          const touch = e.touches[0];
          const deltaX = touch.clientX - startX;
          const deltaY = touch.clientY - startY;
          
          const newX = initialX + deltaX;
          const newY = initialY + deltaY;
          
          subtitleElement.style.position = 'fixed';
          subtitleElement.style.left = `${newX}px`;
          subtitleElement.style.top = `${newY}px`;
          subtitleElement.style.transform = 'translateX(-50%)';
          subtitleElement.style.bottom = 'auto';
          
          e.preventDefault();
        };

        const handleTouchEnd = () => {
          isDragging = false;
          subtitleElement.classList.remove('dragging');
          
          document.removeEventListener('touchmove', handleTouchMove);
          document.removeEventListener('touchend', handleTouchEnd);
        };

        dragHandle.addEventListener('touchstart', handleTouchStart, { passive: false });
      }
      
      showSubtitle(originalText, translatedText, position) {
        this.hideSubtitle();

        // 创建字幕窗口容器 - 使用新的类名结构
        this.subtitleElement = document.createElement('div');
        this.subtitleElement.className = 'lucid-caption-window';
        this.subtitleElement.id = 'lucid-caption-window';

        // 创建字幕容器
        const captionContainer = document.createElement('div');
        captionContainer.className = 'lucid-caption-container';

        // 创建主字幕组件
        const caption = document.createElement('div');
        caption.className = 'lucid-caption';

        // 创建拖拽控制器
        const dragHandle = document.createElement('div');
        dragHandle.className = 'lucid-caption-drag';
        
        // 添加6点拖拽图标 - 使用用户提供的结构
        const dragIcon = document.createElement('div');
        dragIcon.className = 'lucid-svg-icon icon-18';
        dragIcon.innerHTML = `
          <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.38465 9C4.38465 8.55578 4.54282 8.1755 4.85915 7.85915C5.1755 7.54282 5.55579 7.38465 6 7.38465C6.44422 7.38465 6.8245 7.54282 7.14085 7.85915C7.45719 8.1755 7.61535 8.55578 7.61535 9C7.61535 9.44422 7.45719 9.8245 7.14085 10.1408C6.8245 10.4572 6.44422 10.6153 6 10.6153C5.55579 10.6153 5.1755 10.4572 4.85915 10.1408C4.54282 9.8245 4.38465 9.44422 4.38465 9ZM4.38465 15C4.38465 14.5558 4.54282 14.1755 4.85915 13.8591C5.1755 13.5428 5.55579 13.3846 6 13.3846C6.44422 13.3846 6.8245 13.5428 7.14085 13.8591C7.45719 14.1755 7.61535 14.5558 7.61535 15C7.61535 15.4442 7.45719 15.8245 7.14085 16.1408C6.8245 16.4572 6.44422 16.6153 6 16.6153C5.55578 16.6153 5.1755 16.4572 4.85915 16.1408C4.54282 15.8245 4.38465 15.4442 4.38465 15ZM10.3847 9C10.3847 8.55578 10.5428 8.1755 10.8592 7.85915C11.1755 7.54282 11.5558 7.38465 12 7.38465C12.4442 7.38465 12.8245 7.54282 13.1409 7.85915C13.4572 8.1755 13.6154 8.55578 13.6154 9C13.6154 9.44422 13.4572 9.8245 13.1409 10.1408C12.8245 10.4572 12.4442 10.6153 12 10.6153C11.5558 10.6153 11.1755 10.4572 10.8592 10.1408C10.5428 9.8245 10.3847 9.44422 10.3847 9ZM10.3847 15C10.3847 14.5558 10.5428 14.1755 10.8592 13.8592C11.1755 13.5428 11.5558 13.3846 12 13.3846C12.4442 13.3846 12.8245 13.5428 13.1409 13.8592C13.4572 14.1755 13.6154 14.5558 13.6154 15C13.6154 15.4442 13.4572 15.8245 13.1409 16.1408C12.8245 16.4572 12.4442 16.6153 12 16.6153C11.5558 16.6153 11.1755 16.4572 10.8592 16.1408C10.5428 15.8245 10.3847 15.4442 10.3847 15ZM16.3847 9C16.3847 8.55578 16.5428 8.1755 16.8592 7.85915C17.1755 7.54282 17.5558 7.38465 18 7.38465C18.4442 7.38465 18.8245 7.54282 19.1409 7.85915C19.4572 8.1755 19.6154 8.55578 19.6154 9C19.6154 9.44422 19.4572 9.8245 19.1409 10.1408C18.8245 10.4572 18.4442 10.6153 18 10.6153C17.5558 10.6153 17.1755 10.4572 16.8592 10.1408C16.5428 9.8245 16.3847 9.44422 16.3847 9ZM16.3847 15C16.3847 14.5558 16.5428 14.1755 16.8592 13.8592C17.1755 13.5428 17.5558 13.3846 18 13.3846C18.4442 13.3846 18.8245 13.5428 19.1409 13.8592C19.4572 14.1755 19.6154 14.5558 19.6154 15C19.6154 15.4442 19.4572 15.8245 19.1409 16.1408C18.8245 16.4572 18.4442 16.6154 18 16.6154C17.5558 16.6154 17.1755 16.4572 16.8592 16.1408C16.5428 15.8245 16.3847 15.4442 16.3847 15Z"></path>
          </svg>
        `;
        
        dragHandle.appendChild(dragIcon);
        caption.appendChild(dragHandle);

        // 创建字幕内容容器
        const captionContent = document.createElement('div');
        captionContent.className = 'lucid-caption-content lucid-text-shadow';

        // 添加原文
        if (originalText) {
          const originalDiv = document.createElement('div');
          originalDiv.className = 'lucid-caption-primary roboto';
          originalDiv.textContent = originalText;
          captionContent.appendChild(originalDiv);
        }

        // 添加译文
        if (translatedText) {
          const translatedDiv = document.createElement('div');
          translatedDiv.className = 'lucid-caption-secondary inter';
          translatedDiv.textContent = translatedText;
          captionContent.appendChild(translatedDiv);
        }

        // 组装DOM结构
        caption.appendChild(captionContent);
        captionContainer.appendChild(caption);
        this.subtitleElement.appendChild(captionContainer);

        // 自定义位置（如果提供）
        if (position) {
          this.subtitleElement.style.position = 'fixed';
          this.subtitleElement.style.left = `${position.x}px`;
          this.subtitleElement.style.top = `${position.y}px`;
          this.subtitleElement.style.transform = 'translateX(-50%)';
          this.subtitleElement.style.bottom = 'auto';
        }

        // 设置拖拽功能
        this.setupDragFunctionality(this.subtitleElement, dragHandle);

        // 查找视频容器并设置相对定位
        const videoContainer = this.findVideoContainer();
        if (videoContainer && !position) {
          // 将字幕定位相对于视频容器而不是viewport
          videoContainer.style.position = 'relative';
          videoContainer.appendChild(this.subtitleElement);
        } else {
          // 回退到body
          document.body.appendChild(this.subtitleElement);
        }

        // 触发显示动画
        requestAnimationFrame(() => {
          if (this.subtitleElement) {
            this.subtitleElement.classList.add('visible');
          }
        });
      }
      
      hideSubtitle() {
        if (this.subtitleElement) {
          this.subtitleElement.classList.remove('visible');
          
          setTimeout(() => {
            if (this.subtitleElement?.parentNode) {
              this.subtitleElement.parentNode.removeChild(this.subtitleElement);
            }
            this.subtitleElement = null;
          }, 300);
        }
      }
      
      showSettings() {
        this.hideSettings();

        this.settingsElement = document.createElement('div');
        this.settingsElement.className = 'lucid-settings-panel';
        this.settingsElement.id = 'lucid-settings-panel';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'lucid-settings-content';

        const items = [
          { icon: '⚙️', label: 'Lucid 字幕', type: 'toggle', enabled: true },
          { icon: '🌐', label: '主字幕', type: 'select', value: 'English' },
          { icon: '🔤', label: '翻译字幕', type: 'select', value: '中文' },
          { icon: '⚡', label: '翻译引擎', type: 'select', value: 'Microsoft' },
          { icon: '📺', label: '字幕显示', type: 'select', value: '双语字幕' },
          { icon: '🎨', label: '字幕样式', type: 'action' },
          { icon: '⌨️', label: '设置快捷键', type: 'action' }
        ];

        items.forEach((item, index) => {
          const itemElement = this.createSettingItem(item);
          contentDiv.appendChild(itemElement);
          
          if (index < items.length - 1) {
            const separator = document.createElement('div');
            separator.className = 'lucid-setting-separator';
            contentDiv.appendChild(separator);
          }
        });

        this.settingsElement.appendChild(contentDiv);
        document.body.appendChild(this.settingsElement);

        requestAnimationFrame(() => {
          if (this.settingsElement) {
            this.settingsElement.classList.add('visible');
          }
        });

        this.isSettingsVisible = true;
      }
      
      hideSettings() {
        if (this.settingsElement) {
          this.settingsElement.classList.remove('visible');
          
          setTimeout(() => {
            if (this.settingsElement?.parentNode) {
              this.settingsElement.parentNode.removeChild(this.settingsElement);
            }
            this.settingsElement = null;
          }, 200);
        }
        
        this.isSettingsVisible = false;
      }
      
      toggleSettings() {
        if (this.isSettingsVisible) {
          this.hideSettings();
        } else {
          this.showSettings();
        }
      }
      
      createSettingItem(item) {
        const element = document.createElement('div');
        element.className = item.type === 'action' ? 'lucid-setting-item clickable' : 'lucid-setting-item';

        const iconDiv = document.createElement('div');
        iconDiv.className = 'lucid-setting-icon';
        iconDiv.textContent = item.icon;

        const labelDiv = document.createElement('div');
        labelDiv.className = 'lucid-setting-label';
        labelDiv.textContent = item.label;

        element.appendChild(iconDiv);
        element.appendChild(labelDiv);

        if (item.type === 'toggle') {
          const toggle = this.createToggle(item.enabled);
          element.appendChild(toggle);
        } else if (item.type === 'select' || item.type === 'action') {
          const valueDiv = document.createElement('div');
          valueDiv.className = 'lucid-setting-value';
          
          if (item.value) {
            valueDiv.textContent = item.value;
          }
          
          const chevron = document.createElement('div');
          chevron.innerHTML = `
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
            </svg>
          `;
          valueDiv.appendChild(chevron);
          element.appendChild(valueDiv);
        }

        return element;
      }
      
      createToggle(enabled) {
        const toggle = document.createElement('div');
        toggle.className = 'lucid-toggle';

        const track = document.createElement('div');
        track.className = `lucid-toggle-track ${enabled ? 'enabled' : 'disabled'}`;

        const thumb = document.createElement('div');
        thumb.className = `lucid-toggle-thumb ${enabled ? 'enabled' : 'disabled'}`;

        track.appendChild(thumb);
        toggle.appendChild(track);

        toggle.addEventListener('click', () => {
          enabled = !enabled;
          track.className = `lucid-toggle-track ${enabled ? 'enabled' : 'disabled'}`;
          thumb.className = `lucid-toggle-thumb ${enabled ? 'enabled' : 'disabled'}`;
        });

        return toggle;
      }
    }
    
    // 创建管理器实例
    const captionManager = new LucidCaptionManagerDemo();
    
    // 全局函数
    function showBilingualSubtitle() {
      captionManager.showSubtitle(
        "Is this really why everyone go crazy around Gemini?",
        "这真的是大家围绕双子座疯狂的原因吗？"
      );
    }
    
    function showLongSubtitle() {
      captionManager.showSubtitle(
        "This is a much longer subtitle text that demonstrates how the component handles longer content and text wrapping in different screen sizes.",
        "这是一段更长的字幕文本，用于演示组件如何处理较长的内容以及在不同屏幕尺寸下的文本换行效果。"
      );
    }
    
    function showCustomPositionSubtitle() {
      captionManager.showSubtitle(
        "Custom positioned subtitle",
        "自定义位置字幕",
        { x: 400, y: 200 }
      );
    }
    
    function testDragFunctionality() {
      captionManager.showSubtitle(
        "Drag me! Hover to see drag handle",
        "拖拽我！鼠标悬停查看拖拽手柄"
      );
      
      // 提示用户hover查看拖拽手柄
      setTimeout(() => {
        alert('新架构字幕已显示！\\n\\n操作说明：\\n1. 将鼠标悬停在字幕上\\n2. 会出现6点拖拽手柄\\n3. 只能拖拽6点手柄移动字幕\\n4. 字幕主体显示正常光标，不可拖拽\\n\\n新架构特性：\\n- lucid-caption-window 窗口层\\n- lucid-caption-container 容器层\\n- lucid-caption 主体层\\n- lucid-caption-drag 拖拽层');
      }, 500);
    }
    
    function hideSubtitle() {
      captionManager.hideSubtitle();
    }
    
    function showSettings() {
      captionManager.showSettings();
    }
    
    function hideSettings() {
      captionManager.hideSettings();
    }
    
    function toggleSettings() {
      captionManager.toggleSettings();
    }
  </script>
</body>
</html>