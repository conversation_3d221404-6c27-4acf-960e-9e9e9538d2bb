/**
 * 浏览器存储适配器服务
 * 提供标准化的浏览器存储访问接口，遵循WXT框架最佳实践
 */

/**
 * 存储接口定义
 */
export interface IStorageAdapter {
  get<T = any>(key: string): Promise<T | null>;
  set(key: string, value: any): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
}

/**
 * 浏览器存储适配器实现
 * 使用WXT框架官方的browser导入方式
 */
export class BrowserStorageAdapter implements IStorageAdapter {
  
  /**
   * 获取浏览器API实例
   * 使用WXT框架的官方导入方式
   */
  private async getBrowserAPI() {
    try {
      // 使用WXT框架官方的browser导入
      const { browser } = await import('wxt/browser');
      return browser;
    } catch (error) {
      // 如果WXT导入失败，尝试全局browser对象（测试环境）
      if (typeof globalThis !== 'undefined' && (globalThis as any).browser) {
        return (globalThis as any).browser;
      }
      throw new Error('浏览器存储API不可用');
    }
  }

  /**
   * 获取存储值
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const browserAPI = await this.getBrowserAPI();
      const result = await browserAPI.storage.local.get(key);
      return result[key] || null;
    } catch (error) {
      console.warn(`[BrowserStorageAdapter] 获取存储失败 (${key}):`, error);
      return null;
    }
  }

  /**
   * 设置存储值
   */
  async set(key: string, value: any): Promise<void> {
    try {
      const browserAPI = await this.getBrowserAPI();
      await browserAPI.storage.local.set({ [key]: value });
    } catch (error) {
      console.error(`[BrowserStorageAdapter] 设置存储失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 删除存储值
   */
  async remove(key: string): Promise<void> {
    try {
      const browserAPI = await this.getBrowserAPI();
      await browserAPI.storage.local.remove(key);
    } catch (error) {
      console.error(`[BrowserStorageAdapter] 删除存储失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 清空所有存储
   */
  async clear(): Promise<void> {
    try {
      const browserAPI = await this.getBrowserAPI();
      await browserAPI.storage.local.clear();
    } catch (error) {
      console.error('[BrowserStorageAdapter] 清空存储失败:', error);
      throw error;
    }
  }
}

/**
 * LocalStorage 后备适配器
 * 用于开发环境或浏览器存储不可用时的后备方案
 */
export class LocalStorageAdapter implements IStorageAdapter {
  
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn(`[LocalStorageAdapter] 获取存储失败 (${key}):`, error);
      return null;
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`[LocalStorageAdapter] 设置存储失败 (${key}):`, error);
      throw error;
    }
  }

  async remove(key: string): Promise<void> {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`[LocalStorageAdapter] 删除存储失败 (${key}):`, error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('[LocalStorageAdapter] 清空存储失败:', error);
      throw error;
    }
  }
}

/**
 * Mock 存储适配器
 * 用于测试环境
 */
export class MockStorageAdapter implements IStorageAdapter {
  private storage: Map<string, any> = new Map();

  async get<T = any>(key: string): Promise<T | null> {
    return this.storage.get(key) || null;
  }

  async set(key: string, value: any): Promise<void> {
    this.storage.set(key, value);
  }

  async remove(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }
}

/**
 * 存储适配器工厂
 * 根据环境自动选择合适的存储适配器
 */
export class StorageAdapterFactory {
  
  /**
   * 创建存储适配器实例
   */
  static create(): IStorageAdapter {
    // 测试环境
    if (process.env.NODE_ENV === 'test') {
      return new MockStorageAdapter();
    }

    // 生产/开发环境优先使用浏览器存储
    return new BrowserStorageAdapter();
  }

  /**
   * 创建测试用存储适配器
   */
  static createMock(): MockStorageAdapter {
    return new MockStorageAdapter();
  }

  /**
   * 创建本地存储适配器
   */
  static createLocalStorage(): LocalStorageAdapter {
    return new LocalStorageAdapter();
  }
}

// 默认导出工厂创建的实例
export const storageAdapter = StorageAdapterFactory.create();