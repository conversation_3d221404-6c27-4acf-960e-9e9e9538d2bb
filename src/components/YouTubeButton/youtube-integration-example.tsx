/**
 * YouTube Integration Example - YouTube页面集成示例
 * 展示如何在YouTube播放器页面中集成YouTubeButton和SubtitleSettingsMenu
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import { YouTubeButton } from './YouTubeButton';

// 扩展Window接口以支持lucidTranslationConfig
declare global {
  interface Window {
    lucidTranslationConfig?: any;
  }
}

/**
 * YouTube页面内容脚本集成示例
 * 此文件展示如何在实际的YouTube页面中注入和使用YouTubeButton组件
 */

// 1. 等待YouTube播放器加载完成
function waitForYouTubePlayer(): Promise<HTMLElement> {
  return new Promise((resolve) => {
    const checkPlayer = () => {
      const playerControls = document.querySelector('.ytp-right-controls');
      if (playerControls) {
        resolve(playerControls as HTMLElement);
      } else {
        setTimeout(checkPlayer, 100);
      }
    };
    checkPlayer();
  });
}

// 2. 注入Lucid按钮到YouTube播放器控制栏
async function injectLucidButton() {
  try {
    const rightControls = await waitForYouTubePlayer();
    
    // 检查是否已经注入过按钮
    if (document.querySelector('.lucid-youtube-button-container')) {
      return;
    }

    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'lucid-youtube-button-container';
    buttonContainer.style.cssText = `
      margin-right: 8px;
      height: 100%;
      display: flex;
      align-items: center;
    `;

    // 插入到控制栏最右侧（全屏按钮之前）
    rightControls.insertBefore(buttonContainer, rightControls.firstChild);

    // 渲染React组件
    const root = createRoot(buttonContainer);
    root.render(
      <YouTubeButton
        onSettingsChange={handleSubtitleSettingsChange}
      />
    );

    console.log('✅ Lucid字幕按钮已成功注入到YouTube播放器');
  } catch (error) {
    console.error('❌ 注入Lucid按钮失败:', error);
  }
}

// 3. 处理字幕设置变更回调
function handleSubtitleSettingsChange(settings: any) {
  console.log('🎯 字幕设置变更:', settings);

  // 更新翻译引擎配置
  if (settings.translationEngine) {
    updateTranslationEngine(settings.translationEngine);
  }

  // 更新字幕显示模式
  if (settings.displayMode) {
    updateSubtitleDisplay(settings.displayMode);
  }

  // 更新语言设置
  if (settings.primaryLanguage || settings.translationLanguage) {
    updateLanguageSettings({
      source: settings.primaryLanguage,
      target: settings.translationLanguage
    });
  }

  // 切换Lucid字幕功能
  if (typeof settings.lucidSubtitleEnabled !== 'undefined') {
    toggleLucidSubtitles(settings.lucidSubtitleEnabled);
  }
}

// 4. 翻译引擎配置更新
function updateTranslationEngine(engine: string) {
  console.log(`🔧 切换翻译引擎到: ${engine}`);
  
  // 这里实现翻译引擎切换逻辑
  // 例如: 
  // - 更新API端点
  // - 切换配置文件
  // - 重新初始化翻译服务
  
  const translationConfig = {
    'Google': {
      endpoint: 'https://translate.googleapis.com/translate_a/single',
      maxBatchSize: 128
    },
    'Microsoft': {
      endpoint: 'https://api.cognitive.microsofttranslator.com/translate',
      maxBatchSize: 100
    },
    'AI': {
      endpoint: '/api/ai-translate',
      maxBatchSize: 50
    }
  };

  // 应用配置
  const config = translationConfig[engine as keyof typeof translationConfig];
  if (config) {
    // 更新全局翻译配置
    window.lucidTranslationConfig = config;
  }
}

// 5. 字幕显示模式更新
function updateSubtitleDisplay(displayMode: string) {
  console.log(`🎨 切换字幕显示模式到: ${displayMode}`);
  
  const displayModes = {
    '双语字幕': 'bilingual',
    '仅原文': 'original-only',
    '仅翻译': 'translation-only',
    '悬停显示': 'hover-display'
  };

  const mode = displayModes[displayMode as keyof typeof displayModes];
  if (mode) {
    // 更新字幕显示样式
    document.documentElement.setAttribute('data-lucid-subtitle-mode', mode);
    
    // 触发字幕重新渲染
    window.dispatchEvent(new CustomEvent('lucid-subtitle-display-change', {
      detail: { mode }
    }));
  }
}

// 6. 语言设置更新
function updateLanguageSettings(languages: { source?: string; target?: string }) {
  console.log(`🌍 更新语言设置:`, languages);
  
  if (languages.source) {
    // 更新源语言设置
    localStorage.setItem('lucid-source-language', languages.source);
  }
  
  if (languages.target) {
    // 更新目标语言设置
    localStorage.setItem('lucid-target-language', languages.target);
  }

  // 通知翻译服务语言变更
  window.dispatchEvent(new CustomEvent('lucid-language-change', {
    detail: languages
  }));
}

// 7. 切换Lucid字幕功能
function toggleLucidSubtitles(enabled: boolean) {
  console.log(`🔄 ${enabled ? '启用' : '禁用'} Lucid字幕功能`);
  
  if (enabled) {
    // 启用字幕功能
    document.documentElement.classList.add('lucid-subtitles-enabled');
    initializeLucidSubtitles();
  } else {
    // 禁用字幕功能
    document.documentElement.classList.remove('lucid-subtitles-enabled');
    disableLucidSubtitles();
  }
}

// 8. 初始化Lucid字幕功能
function initializeLucidSubtitles() {
  // 这里实现字幕功能初始化逻辑
  // 例如: 
  // - 启动字幕监听器
  // - 初始化翻译服务
  // - 创建字幕覆盖层
  console.log('🚀 Lucid字幕功能已启用');
}

// 9. 禁用Lucid字幕功能
function disableLucidSubtitles() {
  // 这里实现字幕功能禁用逻辑
  // 例如: 
  // - 停止字幕监听器
  // - 清理翻译缓存
  // - 移除字幕覆盖层
  console.log('⏹️ Lucid字幕功能已禁用');
}

// 10. 监听页面路由变化（YouTube SPA特性）
function observePageChanges() {
  let currentUrl = location.href;
  
  new MutationObserver(() => {
    if (location.href !== currentUrl) {
      currentUrl = location.href;
      
      // 如果导航到视频页面，重新注入按钮
      if (currentUrl.includes('/watch?v=')) {
        setTimeout(injectLucidButton, 1000);
      }
    }
  }).observe(document, { subtree: true, childList: true });
}

// 11. 启动集成
function startYouTubeIntegration() {
  console.log('🎬 开始YouTube页面集成...');
  
  // 监听页面变化
  observePageChanges();
  
  // 如果当前在视频页面，立即注入按钮
  if (location.href.includes('/watch?v=')) {
    injectLucidButton();
  }
  
  // 监听动态加载的视频
  document.addEventListener('yt-navigate-finish', () => {
    if (location.href.includes('/watch?v=')) {
      setTimeout(injectLucidButton, 500);
    }
  });
}

// 12. 页面加载完成后启动
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', startYouTubeIntegration);
} else {
  startYouTubeIntegration();
}

// 13. 导出主要函数供其他模块使用
export {
  injectLucidButton,
  handleSubtitleSettingsChange,
  updateTranslationEngine,
  updateSubtitleDisplay,
  updateLanguageSettings,
  toggleLucidSubtitles
};

/**
 * 使用说明:
 * 
 * 1. 在WXT框架的content script中导入此文件:
 *    ```ts
 *    import './integration/youtube-integration-example';
 *    ```
 * 
 * 2. 或者在entrypoints/content.ts中引用:
 *    ```ts
 *    import { injectLucidButton } from '@components/YouTubeButton/youtube-integration-example';
 *    
 *    export default defineContentScript({
 *      matches: ['*://www.youtube.com/*'],
 *      main() {
 *        injectLucidButton();
 *      }
 *    });
 *    ```
 * 
 * 3. 确保在manifest.json中添加YouTube权限:
 *    ```json
 *    {
 *      "permissions": ["activeTab"],
 *      "host_permissions": ["*://www.youtube.com/*"]
 *    }
 *    ```
 */