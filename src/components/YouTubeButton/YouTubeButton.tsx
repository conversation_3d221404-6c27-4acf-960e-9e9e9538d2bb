/**
 * YouTubeButton Component - YouTube播放器集成按钮
 * 展示如何集成SubtitleSettingsMenu字幕设置菜单
 */

import React, { useState, useRef, useEffect } from 'react';
import { SubtitleSettingsMenu } from '../Slider';
import styles from './YouTubeButton.module.css';

interface YouTubeButtonProps {
  className?: string;
  onSettingsChange?: (settings: any) => void;
}

export type { YouTubeButtonProps };

export const YouTubeButton: React.FC<YouTubeButtonProps> = ({ 
  className = '',
  onSettingsChange 
}) => {
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // 点击按钮切换菜单显示状态
  const handleButtonClick = () => {
    setIsMenuVisible(!isMenuVisible);
  };

  // 点击外部区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        buttonRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsMenuVisible(false);
      }
    };

    if (isMenuVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuVisible]);

  return (
    <div className={`${styles.lucidYoutubeButton} ${className}`}>
      <div className={styles.lucidButtonContainer} ref={buttonRef}>
        {/* Lucid品牌标识按钮 */}
        <div 
          className={styles.lucidButtonLogo}
          onClick={handleButtonClick}
          title="Lucid 字幕设置"
        >
          {/* Lucid Logo SVG */}
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 2L2 7V17L12 22L22 17V7L12 2Z"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth="1"
              strokeLinejoin="round"
            />
            <path
              d="M12 8V16M8 10V14M16 10V14"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        </div>

        {/* 字幕设置菜单 */}
        {isMenuVisible && (
          <div 
            ref={menuRef}
            className={`${styles.lucidPanelMenu} ${isMenuVisible ? styles.visible : ''}`}
          >
            <SubtitleSettingsMenu />
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 使用示例和集成指南
 * 
 * 基本使用：
 * ```tsx
 * import { YouTubeButton } from '@components/YouTubeButton';
 * 
 * function YouTubePlayerIntegration() {
 *   return (
 *     <div className="youtube-player-controls">
 *       <YouTubeButton 
 *         onSettingsChange={(settings) => {
 *           console.log('字幕设置变更:', settings);
 *         }}
 *       />
 *     </div>
 *   );
 * }
 * ```
 * 
 * 在YouTube播放器页面集成：
 * ```tsx
 * // 1. 在内容脚本中注入按钮
 * const controlsContainer = document.querySelector('.ytp-right-controls');
 * if (controlsContainer) {
 *   const buttonContainer = document.createElement('div');
 *   controlsContainer.prepend(buttonContainer);
 *   
 *   ReactDOM.render(
 *     <YouTubeButton 
 *       onSettingsChange={handleSubtitleSettingsChange}
 *     />, 
 *     buttonContainer
 *   );
 * }
 * 
 * // 2. 处理设置变更
 * function handleSubtitleSettingsChange(settings) {
 *   // 更新字幕翻译引擎配置
 *   updateTranslationEngine(settings.translationEngine);
 *   
 *   // 更新字幕显示模式
 *   updateSubtitleDisplay(settings.displayMode);
 *   
 *   // 更新语言设置
 *   updateLanguageSettings({
 *     source: settings.primaryLanguage,
 *     target: settings.translationLanguage
 *   });
 * }
 * ```
 * 
 * 样式定制：
 * ```css
 * .custom-youtube-button {
 *   margin-right: 8px;
 * }
 * 
 * .custom-youtube-button .lucid-button-logo {
 *   background: linear-gradient(180deg, #FF6B6B 0%, #4ECDC4 100%);
 * }
 * ```
 */