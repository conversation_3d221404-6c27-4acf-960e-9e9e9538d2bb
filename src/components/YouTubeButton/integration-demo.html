<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTubeButton + SubtitleSettingsMenu 集成演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #0f0f0f;
            color: white;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .demo-header h1 {
            color: #6831FF;
            margin-bottom: 10px;
        }

        .demo-header p {
            color: #aaa;
            font-size: 16px;
        }

        /* 模拟YouTube播放器界面 */
        .youtube-player-mock {
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            aspect-ratio: 16/9;
            margin-bottom: 30px;
        }

        .video-area {
            width: 100%;
            height: calc(100% - 40px);
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #666;
        }

        /* 模拟YouTube控制栏 */
        .youtube-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            display: flex;
            align-items: center;
            padding: 0 12px;
            justify-content: space-between;
        }

        .controls-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .controls-right {
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
        }

        .mock-control-btn {
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .mock-control-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        /* Lucid按钮样式（在演示页面中的模拟） */
        .lucid-button-demo {
            width: 24px;
            height: 24px;
            background: linear-gradient(180deg, #6831FF 0%, #5125C9 100%);
            border-radius: 8px;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 
                rgba(0, 0, 0, 0.2) 0px -1px 1px inset, 
                rgba(255, 255, 255, 0.2) 0px 1px 2px inset, 
                rgba(0, 0, 0, 0.1) 0px 2px 3px;
            transition: all 0.2s ease;
        }

        .lucid-button-demo:hover {
            transform: translateY(-1px);
            box-shadow: 
                rgba(0, 0, 0, 0.2) 0px -1px 1px inset, 
                rgba(255, 255, 255, 0.2) 0px 1px 2px inset, 
                rgba(0, 0, 0, 0.15) 0px 4px 8px;
        }

        .lucid-button-demo svg {
            color: white;
        }

        /* 模拟字幕设置菜单 */
        .subtitle-menu-demo {
            position: absolute;
            bottom: 50px;
            right: 0;
            width: 280px;
            background: rgba(28, 28, 28, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            display: none;
            animation: slideIn 0.2s ease-out;
            z-index: 1000;
        }

        .subtitle-menu-demo.visible {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .menu-section {
            padding: 4px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }

        .menu-section:last-child {
            border-bottom: none;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            cursor: pointer;
            transition: background 0.15s ease;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .menu-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.8;
        }

        .menu-text {
            flex: 1;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        .menu-value {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-right: 8px;
        }

        .menu-arrow {
            width: 18px;
            height: 18px;
            opacity: 0.6;
        }

        .toggle-switch {
            width: 36px;
            height: 20px;
            background: #6831FF;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 18px;
            transition: all 0.2s ease;
        }

        .learn-mode-btn {
            margin: 8px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.09);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 6px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: background 0.15s ease;
        }

        .learn-mode-btn:hover {
            background: rgba(255, 255, 255, 0.12);
        }

        /* 说明文档样式 */
        .documentation {
            background: rgba(255, 255, 255, 0.02);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
        }

        .documentation h2 {
            color: #6831FF;
            margin-bottom: 16px;
        }

        .documentation pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.5;
        }

        .documentation code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', monospace;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .feature-list li::before {
            content: '✅';
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>YouTubeButton + SubtitleSettingsMenu 集成演示</h1>
            <p>展示如何在YouTube播放器中集成Lucid字幕设置菜单</p>
        </div>

        <!-- 模拟YouTube播放器 -->
        <div class="youtube-player-mock">
            <div class="video-area">
                🎬 模拟YouTube视频播放区域
            </div>
            
            <div class="youtube-controls">
                <div class="controls-left">
                    <button class="mock-control-btn">▶️</button>
                    <button class="mock-control-btn">🔊</button>
                    <span style="font-size: 12px; color: rgba(255,255,255,0.8);">0:00 / 10:30</span>
                </div>
                
                <div class="controls-right">
                    <button class="mock-control-btn">⚙️</button>
                    <button class="mock-control-btn">📱</button>
                    <button class="mock-control-btn">🔳</button>
                    
                    <!-- Lucid按钮演示 -->
                    <button class="lucid-button-demo" onclick="toggleSubtitleMenu()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7V17L12 22L22 17V7L12 2Z" fill="currentColor" stroke="currentColor" stroke-width="1" stroke-linejoin="round"/>
                            <path d="M12 8V16M8 10V14M16 10V14" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </button>
                    
                    <!-- 字幕设置菜单演示 -->
                    <div class="subtitle-menu-demo" id="subtitleMenu">
                        <div class="menu-section">
                            <div class="menu-item">
                                <div class="menu-icon">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                        <path d="M4.16667 16.6666C3.70833 16.6666 3.31597 16.5034 2.98958 16.177C2.66319 15.8506 2.5 15.4583 2.5 14.9999V4.99992C2.5 4.54159 2.66319 4.14922 2.98958 3.82284C3.31597 3.49645 3.70833 3.33325 4.16667 3.33325H15.8333C16.2917 3.33325 16.684 3.49645 17.0104 3.82284C17.3368 4.14922 17.5 4.54159 17.5 4.99992V14.9999C17.5 15.4583 17.3368 15.8506 17.0104 16.177C16.684 16.5034 16.2917 16.6666 15.8333 16.6666H4.16667Z" fill="rgba(255,255,255,0.8)"/>
                                    </svg>
                                </div>
                                <span class="menu-text">Lucid 字幕</span>
                                <div class="toggle-switch"></div>
                            </div>
                        </div>

                        <div class="menu-section">
                            <div class="menu-item">
                                <div class="menu-icon">🌍</div>
                                <span class="menu-text">主字幕</span>
                                <span class="menu-value">English</span>
                                <div class="menu-arrow">▶</div>
                            </div>
                        </div>

                        <div class="menu-section">
                            <div class="menu-item">
                                <div class="menu-icon">🔤</div>
                                <span class="menu-text">翻译字幕</span>
                                <span class="menu-value">中文</span>
                                <div class="menu-arrow">▶</div>
                            </div>
                        </div>

                        <div class="menu-section">
                            <div class="menu-item">
                                <div class="menu-icon">🤖</div>
                                <span class="menu-text">翻译引擎</span>
                                <span class="menu-value">Microsoft</span>
                                <div class="menu-arrow">▶</div>
                            </div>
                        </div>

                        <div class="menu-section">
                            <div class="menu-item">
                                <div class="menu-icon">📺</div>
                                <span class="menu-text">字幕显示</span>
                                <span class="menu-value">双语字幕</span>
                                <div class="menu-arrow">▶</div>
                            </div>
                        </div>

                        <div class="menu-section">
                            <div class="menu-item">
                                <div class="menu-icon">🎨</div>
                                <span class="menu-text">字幕样式</span>
                                <div class="menu-arrow">▶</div>
                            </div>
                        </div>

                        <div class="menu-section">
                            <div class="menu-item">
                                <div class="menu-icon">⌨️</div>
                                <span class="menu-text">设置快捷键</span>
                                <div class="menu-arrow">▶</div>
                            </div>
                        </div>

                        <div class="learn-mode-btn">
                            <span>📚</span>
                            <span>学习模式</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 集成说明文档 -->
        <div class="documentation">
            <h2>🎯 集成特性</h2>
            <ul class="feature-list">
                <li>完整的YouTube播放器控制栏集成</li>
                <li>1:1复刻设计稿的字幕设置菜单</li>
                <li>响应式设计，支持不同屏幕尺寸</li>
                <li>平滑的动画和交互效果</li>
                <li>完整的TypeScript类型支持</li>
                <li>CSS Modules样式隔离</li>
                <li>外部点击自动关闭菜单</li>
                <li>设置变更回调系统</li>
            </ul>
        </div>

        <div class="documentation">
            <h2>📦 组件使用</h2>
            <pre><code>import { YouTubeButton } from '@components/YouTubeButton';

&lt;YouTubeButton 
  onSettingsChange={(settings) => {
    console.log('字幕设置变更:', settings);
  }}
/&gt;</code></pre>
        </div>

        <div class="documentation">
            <h2>🚀 集成到YouTube页面</h2>
            <pre><code>import { injectLucidButton } from '@components/YouTubeButton';

// 在内容脚本中调用
injectLucidButton();</code></pre>
        </div>

        <div class="documentation">
            <h2>⚙️ 设置回调处理</h2>
            <pre><code>function handleSubtitleSettingsChange(settings) {
  // 更新翻译引擎
  updateTranslationEngine(settings.translationEngine);
  
  // 更新显示模式
  updateSubtitleDisplay(settings.displayMode);
  
  // 更新语言设置
  updateLanguageSettings({
    source: settings.primaryLanguage,
    target: settings.translationLanguage
  });
  
  // 切换字幕功能
  toggleLucidSubtitles(settings.lucidSubtitleEnabled);
}</code></pre>
        </div>
    </div>

    <script>
        // 演示菜单切换功能
        function toggleSubtitleMenu() {
            const menu = document.getElementById('subtitleMenu');
            menu.classList.toggle('visible');
        }

        // 点击外部关闭菜单
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('subtitleMenu');
            const button = document.querySelector('.lucid-button-demo');
            
            if (!menu.contains(event.target) && !button.contains(event.target)) {
                menu.classList.remove('visible');
            }
        });

        // 模拟设置变更回调
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                const text = this.querySelector('.menu-text').textContent;
                console.log(`🎯 设置项被点击: ${text}`);
                
                // 模拟设置变更通知
                if (text === 'Lucid 字幕') {
                    console.log('🔄 切换Lucid字幕功能');
                } else if (text === '翻译引擎') {
                    console.log('🤖 打开翻译引擎选择面板');
                } else if (text === '字幕显示') {
                    console.log('📺 打开显示模式选择面板');
                }
            });
        });
    </script>
</body>
</html>