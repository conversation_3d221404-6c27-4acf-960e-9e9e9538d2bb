/**
 * YouTubeButton Styles - YouTube播放器集成按钮样式
 * 基于 .claude/specs/subtitle-translation/subtitle-setting.html 设计稿
 */

/* 主按钮容器 */
.lucidYoutubeButton {
  width: 48px;
  height: 100%;
  display: inline-block;
  float: right;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  cursor: pointer;
  user-select: none;
}

/* 按钮内容容器 */
.lucidButtonContainer {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* Lucid品牌标识按钮 */
.lucidButtonLogo {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  line-height: 1.5;
  color: #d8d8d8;
  z-index: 1000;
  position: relative;
  background: linear-gradient(180deg, #6831FF 0%, #5125C9 100%);
  border-radius: 8px;
  width: 24px;
  height: 24px;
  box-shadow: 
    rgba(0, 0, 0, 0.2) 0px -1px 1px inset, 
    rgba(255, 255, 255, 0.2) 0px 1px 2px inset, 
    rgba(0, 0, 0, 0.1) 0px 2px 3px;
  transition: all 0.2s ease;
}

.lucidButtonLogo:hover {
  transform: translateY(-1px);
  box-shadow: 
    rgba(0, 0, 0, 0.2) 0px -1px 1px inset, 
    rgba(255, 255, 255, 0.2) 0px 1px 2px inset, 
    rgba(0, 0, 0, 0.15) 0px 4px 8px;
}

.lucidButtonLogo:active {
  transform: translateY(0px);
  box-shadow: 
    rgba(0, 0, 0, 0.3) 0px -1px 1px inset, 
    rgba(255, 255, 255, 0.1) 0px 1px 1px inset, 
    rgba(0, 0, 0, 0.1) 0px 1px 2px;
}

/* 字幕设置面板菜单 - 完全匹配设计稿样式 */
.lucidPanelMenu {
  backdrop-filter: blur(10px);
  z-index: 2147483647;
  -webkit-font-smoothing: antialiased;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 10px;
  user-select: none;
  position: absolute;
  bottom: 58px;
  right: 0px;
  width: 280px;
  display: none;
  border-radius: 12px;
  background: rgba(28, 28, 28, 0.9);
  border-width: 1px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.1);
  border-image: initial;
  overflow: hidden;
  animation: slideIn 0.2s ease-out;
}

/* 菜单可见状态 */
.lucidPanelMenu.visible {
  display: block;
}

/* 菜单动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 面板菜单容器 - 支持多级菜单滑动 */
.lucidPanelMenuContainer {
  display: flex;
  flex-direction: row;
  width: 560px;
  transition: transform 0.3s ease-in-out, height 0.3s ease-in-out;
}

.lucidPanelMenuContainer .lucidPrimaryPanelMenu,
.lucidPanelMenuContainer .lucidSecondPanelMenu {
  width: 280px;
  flex-shrink: 0;
  transition: all 0.2s ease-in-out;
}

/* 悬停效果 */
.lucidYoutubeButton:hover {
  opacity: 1 !important;
}