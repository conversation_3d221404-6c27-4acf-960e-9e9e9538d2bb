/**
 * Slider Component - 1:1 复刻自 .claude/design/lu-silde/index.tsx
 * 主 Slider 组件，整合所有子组件
 */

import React, { useState, useCallback } from 'react';
// 不使用CSS Modules，直接使用原始类名
// import styles from './Slider.module.css';
import { BackIcon, CloseIcon, MoreIcon, BookmarkIcon, SettingsIcon, BookIcon } from './icons';
import { LoginView } from './components/LoginView';
import { MyView } from './components/MyView';
import { SettingsView } from './components/SettingsView';
import { LearnView } from './components/LearnView';
import { AccountView } from './components/AccountView';

interface SliderProps {
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

export const Slider: React.FC<SliderProps> = ({ 
  isOpen = true, 
  onClose = () => {},
  className = ''
}) => {
  const [activeView, setActiveView] = useState<'my' | 'settings' | 'learn'>('my');
  const [currentPage, setCurrentPage] = useState<'main' | 'account'>('main');
  const [pageTitle, setPageTitle] = useState('Lucid');
  const [isLoggedIn, setIsLoggedIn] = useState(true);

  const handleLogin = useCallback(() => {
    setIsLoggedIn(true);
  }, []);

  const handleLogout = useCallback(() => {
    setIsLoggedIn(false);
    setCurrentPage('main');
    setActiveView('my');
  }, []);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const changeView = useCallback((view: 'my' | 'settings' | 'learn') => {
    setActiveView(view);
    setCurrentPage('main');
    setPageTitle('Lucid');
  }, []);

  const navigateTo = useCallback((page: string) => {
    setCurrentPage(page as 'main' | 'account');
    if (page === 'account') {
      setPageTitle('Lucid 账号');
    }
  }, []);

  const goBack = useCallback(() => {
    setCurrentPage('main');
    setPageTitle('Lucid');
    // Ensure the correct footer button is active when going back
    if (currentPage === 'account') {
      setActiveView('settings');
    }
  }, [currentPage]);

  const renderView = () => {
    if (currentPage === 'account') {
      return <AccountView onLogout={handleLogout} />;
    }
    
    switch (activeView) {
      case 'settings':
        return <SettingsView onNavigate={navigateTo} />;
      case 'learn':
        return <LearnView />;
      case 'my':
      default:
        return <MyView />;
    }
  };

  // 如果未登录，显示登录界面
  if (!isLoggedIn) {
    return (
      <div className={`lu-slide ${!isOpen ? 'hidden' : ''} ${className}`}>
        <LoginView onLogin={handleLogin} />
      </div>
    );
  }

  const isSubPage = currentPage !== 'main';

  return (
    <div className={`lu-slide ${!isOpen ? 'hidden' : ''} ${className}`}>
      {/* 头部 */}
      <div className={`lu-slider-header ${isSubPage ? 'is-page' : ''}`}>
        <button
          id="back-button"
          className="lu-header-button lu-back-button"
          aria-label="返回"
          onClick={goBack}
          style={{ display: isSubPage ? 'flex' : 'none' }}
        >
          <BackIcon />
        </button>

        <div
          className="lu-brand"
          style={{ display: isSubPage ? 'none' : 'flex' }}
        >
          <div className="lu-logo">L</div>
          <div className="lu-title">Lucid</div>
          <div className="lu-vip-tag early-bird">Early Bird</div>
        </div>
        
        {isSubPage && (
          <div id="header-title" className="lu-title">
            {pageTitle}
          </div>
        )}

        <button
          id="close-button"
          className="lu-header-button lu-close-button"
          aria-label="关闭"
          onClick={handleClose}
          style={{ display: isSubPage ? 'none' : 'flex' }}
        >
          <CloseIcon />
        </button>
        
        <button
          id="more-button"
          className="lu-header-button lu-more-button"
          aria-label="更多信息"
          style={{ display: isSubPage ? 'flex' : 'none' }}
        >
          <MoreIcon />
        </button>
      </div>

      {/* 内容区域 */}
      <div className="lu-slider-content">
        {renderView()}
      </div>

      {/* 底部导航 */}
      <div className="lu-slider-footer">
        <button
          className={`lu-action-button ${
            activeView === 'my' && currentPage === 'main' ? 'active' : ''
          }`}
          onClick={() => changeView('my')}
        >
          <BookmarkIcon />
          <span>我的</span>
        </button>
        
        <button
          className={`lu-action-button ${
            activeView === 'settings' || currentPage === 'account' ? 'active' : ''
          }`}
          onClick={() => changeView('settings')}
        >
          <SettingsIcon />
          <span>设置</span>
        </button>
        
        <button
          className={`lu-action-button ${
            activeView === 'learn' && currentPage === 'main' ? 'active' : ''
          }`}
          onClick={() => changeView('learn')}
        >
          <BookIcon />
          <span>学习</span>
        </button>
      </div>
    </div>
  );
};