/**
 * SubtitleSettingsMenu Component - 字幕设置菜单
 * 基于 .claude/specs/subtitle-translation/subtitle-setting.html 设计稿 1:1 复刻
 */

import React, { useState } from 'react';
import { SettingItem } from './SettingItem';
import { Switch } from './Switch';
import { GlobeIcon } from '../icons'; // 使用现有的地球图标
import { 
  SubtitleIcon, 
  TranslationIcon, 
  EngineIcon, 
  DisplayIcon, 
  StyleIcon, 
  KeyboardIcon,
  InfoIcon,
  SmallArrowIcon,
  LearnModeIcon
} from '../icons/SubtitleIcons';
import styles from './SubtitleSettingsMenu.module.css';

interface SubtitleSettingsMenuProps {
  className?: string;
}

export const SubtitleSettingsMenu: React.FC<SubtitleSettingsMenuProps> = ({ 
  className = '' 
}) => {
  // 设置状态
  const [lucidSubtitleEnabled, setLucidSubtitleEnabled] = useState(true);
  const [primaryLanguage, setPrimaryLanguage] = useState('English');
  const [translationLanguage, setTranslationLanguage] = useState('中文');
  const [translationEngine, setTranslationEngine] = useState('Microsoft');
  const [displayMode, setDisplayMode] = useState('双语字幕');

  return (
    <div className={`${styles.subtitleSettingsMenu} ${className}`}>
      {/* 主容器 */}
      <div className={styles.panelMenuContainer}>
        <div className={styles.primaryPanelMenu}>
          
          {/* Lucid字幕 - 带开关 */}
          <div className={styles.settingSection}>
            <SettingItem
              icon={<SubtitleIcon />}
              title="Lucid 字幕"
              hasArrow={false}
            >
              <div className={styles.settingInfoIcon}>
                <InfoIcon />
              </div>
              <Switch 
                defaultChecked={lucidSubtitleEnabled}
                onChange={setLucidSubtitleEnabled}
              />
            </SettingItem>
          </div>

          {/* 主字幕 */}
          <div className={styles.settingSection}>
            <SettingItem
              icon={<GlobeIcon />}
              title="主字幕"
              value={primaryLanguage}
              onClick={() => {
                // TODO: 打开语言选择面板
                console.log('Open primary language selector');
              }}
            />
          </div>

          {/* 翻译字幕 */}
          <div className={styles.settingSection}>
            <SettingItem
              icon={<TranslationIcon />}
              title="翻译字幕"
              value={translationLanguage}
              onClick={() => {
                // TODO: 打开翻译语言选择面板
                console.log('Open translation language selector');
              }}
            />
          </div>

          {/* 翻译引擎 */}
          <div className={styles.settingSection}>
            <SettingItem
              icon={<EngineIcon />}
              title="翻译引擎"
              value={translationEngine}
              onClick={() => {
                // TODO: 打开翻译引擎选择面板
                console.log('Open translation engine selector');
              }}
            />
          </div>

          {/* 字幕显示 */}
          <div className={styles.settingSection}>
            <SettingItem
              icon={<DisplayIcon />}
              title="字幕显示"
              value={displayMode}
              onClick={() => {
                // TODO: 打开显示模式选择面板
                console.log('Open display mode selector');
              }}
            />
          </div>

          {/* 字幕样式 */}
          <div className={styles.settingSection}>
            <SettingItem
              icon={<StyleIcon />}
              title="字幕样式"
              onClick={() => {
                // TODO: 打开字幕样式设置面板
                console.log('Open subtitle style settings');
              }}
            />
          </div>

          {/* 设置快捷键 */}
          <div className={styles.settingSection}>
            <SettingItem
              icon={<KeyboardIcon />}
              title="设置快捷键"
              onClick={() => {
                // TODO: 打开快捷键设置面板
                console.log('Open keyboard shortcuts settings');
              }}
            />
          </div>

          {/* 学习模式按钮 */}
          <div className={styles.learnModeSection}>
            <div 
              className={styles.learnModeButton}
              onClick={() => {
                // TODO: 启动学习模式
                console.log('Start learn mode');
              }}
            >
              <LearnModeIcon />
              <div className={styles.learnModeText}>学习模式</div>
              <div className={styles.learnModeInfo}>
                <InfoIcon />
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};