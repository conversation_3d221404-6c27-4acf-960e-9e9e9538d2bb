/**
 * SubtitleSettingsMenu Styles - 字幕设置菜单样式
 * 基于 .claude/specs/subtitle-translation/subtitle-setting.html 设计稿 1:1 复刻
 */

/* 主容器样式 - 匹配设计稿中的overlay样式 */
.subtitleSettingsMenu {
  position: absolute;
  bottom: 58px;
  right: 0px;
  width: 280px;
  background: rgba(28, 28, 28, 0.90);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
  overflow: hidden;
  border-radius: 12px;
  outline: 1px rgba(255, 255, 255, 0.10) solid;
  outline-offset: -1px;
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  z-index: 2147483647;
  -webkit-font-smoothing: antialiased;
  user-select: none;
  font-family: Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* 面板容器 */
.subtitleSettingsMenu .panelMenuContainer {
  width: 100%;
  display: flex;
  flex-direction: row;
}

.subtitleSettingsMenu .primaryPanelMenu {
  width: 280px;
  align-self: stretch;
  padding-bottom: 4px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  display: flex;
}

/* 设置项区域 */
.settingSection {
  align-self: stretch;
  padding-bottom: 1px;
  border-bottom: 1px rgba(255, 255, 255, 0.08) solid;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

/* 信息图标 */
.settingInfoIcon {
  justify-content: center;
  align-items: center;
  display: flex;
  margin-right: 4px;
}

/* 学习模式区域 */
.learnModeSection {
  width: 264px;
  margin: 0 auto;
  padding: 8px;
}

.learnModeButton {
  width: 264px;
  height: 32px;
  padding-left: 9px;
  padding-right: 9px;
  padding-top: 1px;
  padding-bottom: 1px;
  position: relative;
  background: rgba(255, 255, 255, 0.09);
  border-radius: 6px;
  outline: 1px rgba(255, 255, 255, 0.12) solid;
  outline-offset: -1px;
  justify-content: center;
  align-items: center;
  gap: 11px;
  display: flex;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.learnModeButton:hover {
  background: rgba(255, 255, 255, 0.12);
}

.learnModeInfo {
  width: 30px;
  height: 30px;
  position: absolute;
  right: 1px;
  top: 1px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.learnModeText {
  justify-content: center;
  display: flex;
  flex-direction: column;
  color: rgba(255, 255, 255, 0.80);
  font-size: 14px;
  font-family: Roboto;
  font-weight: 400;
  line-height: 14px;
  word-wrap: break-word;
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
}

/* 响应式调整 */
@media (max-width: 320px) {
  .subtitleSettingsMenu {
    width: 260px;
  }
  
  .subtitleSettingsMenu .primaryPanelMenu {
    width: 260px;
  }
  
  .learnModeSection {
    width: 244px;
  }
  
  .learnModeButton {
    width: 244px;
  }
}

/* 可见性控制 */
.subtitleSettingsMenu.visible {
  display: flex;
}

.subtitleSettingsMenu.hidden {
  display: none;
}