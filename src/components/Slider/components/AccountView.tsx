/**
 * AccountView Component - 1:1 复刻自设计文件
 * 账户页面组件
 */

import React from 'react';
// import styles from '../Slider.module.css'; // Commented out for direct CSS classes
import { SettingItem } from './SettingItem';

interface AccountViewProps {
  onLogout: () => void;
}

export const AccountView: React.FC<AccountViewProps> = ({ onLogout }) => {
  return (
    <div id="view-account" className="lu-view lu-account-view active">
      <div className="lu-card lu-settings-card">
        <div className="lu-setting-item">
          <div className="lu-setting-title">邮箱</div>
          <span className="lu-setting-value"><EMAIL></span>
        </div>
        <div className="lu-setting-item">
          <div className="lu-setting-title">会员</div>
          <span className="lu-membership-tag early-bird">Early Bird</span>
        </div>
        <div className="lu-setting-item">
          <div className="lu-setting-title">到期时间</div>
          <span className="lu-setting-value">2024.09.06</span>
        </div>
        <SettingItem title="订阅管理" hasArrow={true} />
        <SettingItem title="删除账号" hasArrow={true} />
      </div>
      <button className="lu-logout-button" onClick={onLogout}>
        退出账号
      </button>
    </div>
  );
};