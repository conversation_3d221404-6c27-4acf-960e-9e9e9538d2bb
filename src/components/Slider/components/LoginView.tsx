/**
 * LoginView Component - 1:1 复刻自设计文件
 * 登录界面组件
 */

import React from 'react';
// import styles from '../Slider.module.css'; // Commented out for direct CSS classes
import { GitHubIcon, GoogleIcon, AppleIcon, MailIcon } from '../icons';

interface LoginViewProps {
  onLogin: () => void;
}

export const LoginView: React.FC<LoginViewProps> = ({ onLogin }) => {
  return (
    <div className="lu-login-container">
      <div className="lu-login-box">
        <div
          className="lu-brand"
          style={{ justifyContent: 'center', marginBottom: '32px' }}
        >
          <div className="lu-logo">L</div>
          <div className="lu-title">Welcome to Lucid</div>
        </div>
        
        <div className="lu-social-login-group">
          <button 
            className="lu-social-login-btn github" 
            onClick={onLogin}
          >
            <GitHubIcon /> Sign in with GitHub
          </button>
          <button 
            className="lu-social-login-btn google" 
            onClick={onLogin}
          >
            <GoogleIcon /> Sign in with Google
          </button>
          <button 
            className="lu-social-login-btn apple" 
            onClick={onLogin}
          >
            <AppleIcon /> Sign in with Apple
          </button>
        </div>
        
        <div className="lu-divider">
          <span>OR</span>
        </div>
        
        <div className="lu-email-form">
          <div className="lu-email-input-wrapper">
            <MailIcon />
            <input
              type="email"
              placeholder="Enter your email"
              className="lu-email-input"
            />
          </div>
          <button className="lu-send-code-btn" onClick={onLogin}>
            Send Code
          </button>
        </div>
      </div>
    </div>
  );
};