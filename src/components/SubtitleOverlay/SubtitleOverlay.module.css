/* 字幕叠加层样式 - 基于设计稿 subtitle.html */
.subtitleOverlay {
  z-index: 10000;
  pointer-events: none;
  user-select: none;
}

.overlay {
  padding: 5.24px 3.90px 6.24px 3.90px;
  background: rgba(0, 0, 0, 0.70);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 2.10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.container {
  width: 100%;
  padding-bottom: 0.86px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.originalText {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 15.60px;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  font-weight: 400;
  line-height: 21.86px;
  word-wrap: break-word;
  text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
  margin: 0;
  padding: 0;
  white-space: pre-wrap;
}

.translatedText {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 15.60px;
  font-family: 'Inter', 'Microsoft YaHei', sans-serif;
  font-weight: 400;
  line-height: 21.86px;
  word-wrap: break-word;
  text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
  margin: 0;
  padding: 0;
  white-space: pre-wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .originalText,
  .translatedText {
    font-size: 14px;
    line-height: 19px;
  }
}

/* 动画效果 */
.subtitleOverlay {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeInUp 0.3s ease-out forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subtitleOverlay.hiding {
  animation: fadeOutDown 0.3s ease-in forwards;
}

@keyframes fadeOutDown {
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}