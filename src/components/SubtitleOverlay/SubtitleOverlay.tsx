import React from 'react';
import styles from './SubtitleOverlay.module.css';

export interface SubtitleOverlayProps {
  originalText: string;
  translatedText: string;
  visible?: boolean;
  position?: { x: number; y: number };
  className?: string;
}

export const SubtitleOverlay: React.FC<SubtitleOverlayProps> = ({
  originalText,
  translatedText,
  visible = true,
  position,
  className
}) => {
  if (!visible || (!originalText && !translatedText)) {
    return null;
  }

  const overlayStyle = position
    ? {
        left: `${position.x}px`,
        top: `${position.y}px`,
        position: 'absolute' as const
      }
    : {};

  return (
    <div 
      className={`${styles.subtitleOverlay} ${className || ''}`}
      style={overlayStyle}
    >
      <div className={styles.overlay}>
        {originalText && (
          <div className={styles.container}>
            <div className={styles.originalText}>
              {originalText}
            </div>
          </div>
        )}
        {translatedText && (
          <div className={styles.container}>
            <div className={styles.translatedText}>
              {translatedText}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubtitleOverlay;