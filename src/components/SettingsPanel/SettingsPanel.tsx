import React from 'react';
import styles from './SettingsPanel.module.css';

export interface SettingItem {
  id: string;
  icon: React.ReactNode;
  label: string;
  value?: string;
  type: 'toggle' | 'select' | 'info' | 'action';
  enabled?: boolean;
  options?: { label: string; value: string }[];
  onClick?: () => void;
  onChange?: (value: any) => void;
}

export interface SettingsPanelProps {
  items: SettingItem[];
  visible?: boolean;
  position?: { x: number; y: number };
  className?: string;
  onClose?: () => void;
}

const ChevronIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fillOpacity="0.6"/>
  </svg>
);

const ToggleSwitch: React.FC<{ enabled: boolean; onChange: (enabled: boolean) => void }> = ({ enabled, onChange }) => (
  <div className={styles.toggleContainer} onClick={() => onChange(!enabled)}>
    <div className={`${styles.toggleBackground} ${enabled ? styles.enabled : ''}`}>
      <div className={`${styles.toggleThumb} ${enabled ? styles.thumbEnabled : ''}`} />
    </div>
  </div>
);

const InfoIcon = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.00008 12.8334C3.77842 12.8334 1.16675 10.2217 1.16675 7.00008C1.16675 3.77842 3.77842 1.16675 7.00008 1.16675C10.2217 1.16675 12.8334 3.77842 12.8334 7.00008C12.8334 10.2217 10.2217 12.8334 7.00008 12.8334ZM7.00008 11.6667C9.57742 11.6667 11.6667 9.57742 11.6667 7.00008C11.6667 4.42275 9.57742 2.33341 7.00008 2.33341C4.42275 2.33341 2.33341 4.42275 2.33341 7.00008C2.33341 9.57742 4.42275 11.6667 7.00008 11.6667ZM6.41675 4.08341H7.58341V5.25008H6.41675V4.08341ZM6.41675 6.41675H7.58341V9.91675H6.41675V6.41675Z" fill="white" fillOpacity="0.6"/>
  </svg>
);

export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  items,
  visible = true,
  position,
  className,
  onClose
}) => {
  if (!visible) {
    return null;
  }

  const panelStyle = position
    ? {
        left: `${position.x}px`,
        top: `${position.y}px`,
        position: 'absolute' as const
      }
    : {};

  return (
    <div 
      className={`${styles.settingsPanel} ${className || ''}`}
      style={panelStyle}
    >
      <div className={styles.panelContainer}>
        <div className={styles.contentContainer}>
          {items.map((item, index) => (
            <div key={item.id}>
              <div 
                className={`${styles.settingItem} ${item.type === 'action' ? styles.clickable : ''}`}
                onClick={item.onClick}
              >
                <div className={styles.iconContainer}>
                  <div className={styles.iconWrapper}>
                    {item.icon}
                  </div>
                </div>
                
                <div className={styles.labelContainer}>
                  <div className={styles.label}>{item.label}</div>
                  {item.type === 'toggle' && (
                    <div className={styles.infoIconContainer}>
                      <InfoIcon />
                    </div>
                  )}
                </div>
                
                <div className={styles.controlContainer}>
                  {item.type === 'toggle' && (
                    <ToggleSwitch 
                      enabled={item.enabled || false}
                      onChange={(enabled) => item.onChange?.(enabled)}
                    />
                  )}
                  
                  {(item.type === 'select' || item.type === 'info') && (
                    <>
                      <div className={styles.valueContainer}>
                        <div className={styles.value}>
                          {item.value}
                        </div>
                      </div>
                      <ChevronIcon />
                    </>
                  )}
                  
                  {item.type === 'action' && (
                    <ChevronIcon />
                  )}
                </div>
              </div>
              
              {index < items.length - 1 && (
                <div className={styles.separator} />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;