/* 设置面板样式 - 基于设计稿 subtitle-setting.html */
.settingsPanel {
  z-index: 10001;
  user-select: none;
}

.panelContainer {
  width: 282px;
  padding: 1px;
  background: rgba(28, 28, 28, 0.90);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
  border-radius: 12px;
  outline: 1px rgba(255, 255, 255, 0.10) solid;
  outline-offset: -1px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  overflow: hidden;
}

.contentContainer {
  width: 100%;
  display: inline-flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

.settingItem {
  width: 100%;
  height: 40px;
  padding: 0 8px 0 12px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  cursor: default;
  transition: background-color 0.2s ease;
}

.settingItem.clickable {
  cursor: pointer;
}

.settingItem.clickable:hover {
  background: rgba(255, 255, 255, 0.05);
}

.iconContainer {
  width: 28px;
  height: 20px;
  padding-right: 8px;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.iconWrapper {
  width: 20px;
  height: 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.labelContainer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.label {
  color: rgba(255, 255, 255, 0.80);
  font-size: 13px;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  font-weight: 400;
  line-height: 13px;
  word-wrap: break-word;
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
  white-space: nowrap;
}

.infoIconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.controlContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.valueContainer {
  max-width: 110px;
  padding-right: 4px;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.value {
  max-width: 106px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  color: rgba(255, 255, 255, 0.90);
  font-size: 12.90px;
  font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
  font-weight: 400;
  line-height: 15.60px;
  word-wrap: break-word;
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
  white-space: nowrap;
  text-overflow: ellipsis;
}

.separator {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.08);
  margin: 0;
}

/* 开关组件样式 */
.toggleContainer {
  width: 56px;
  height: 38px;
  padding: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  cursor: pointer;
}

.toggleBackground {
  width: 32px;
  height: 14px;
  border-radius: 7px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
  transition: background-color 0.3s ease;
}

.toggleBackground.enabled {
  background: rgba(255, 200, 98, 0.5);
}

.toggleThumb {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background: #FFC862;
  position: absolute;
  top: -2px;
  left: -1px;
  transition: transform 0.3s ease;
  box-shadow: 
    0px 1px 3px rgba(0, 0, 0, 0.12),
    0px 1px 1px rgba(0, 0, 0, 0.14),
    0px 2px 1px rgba(0, 0, 0, 0.2);
}

.toggleThumb.thumbEnabled {
  transform: translateX(15px);
}

/* 动画效果 */
.settingsPanel {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
  animation: panelFadeIn 0.2s ease-out forwards;
}

@keyframes panelFadeIn {
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.settingsPanel.hiding {
  animation: panelFadeOut 0.2s ease-in forwards;
}

@keyframes panelFadeOut {
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .panelContainer {
    width: 260px;
  }
  
  .label {
    font-size: 12px;
  }
  
  .value {
    font-size: 12px;
  }
}