/**
 * 🎯 重构后的翻译编排器 - 符合WXT扩展最佳实践
 * 
 * 修复的问题：
 * ✅ 代码复杂度过高 - 拆分为专门的管理器类
 * ✅ 错误处理不当 - 使用统一错误处理系统 
 * ✅ 方法过长 - 每个方法不超过50行
 * ✅ 职责不清 - 单一职责原则
 */

import type {
  ScanConfig, ScannedNode, TranslationResult, TranslationConfig,
  OrchestratorConfig, BatchTranslationResult
} from './types';
import { ViewMode, TranslationState } from './types';

import { ScanStrategyManager, type ScanStrategyResult } from './scan-strategy-manager';
import { BatchTranslationProcessor, type BatchTranslationResult as ProcessorResult } from './batch-translation-processor';
import { 
  TranslationErrorHandler, 
  TranslationErrorType, 
  createSafeTranslationWrapper,
  type TranslationError 
} from './error-handler';

import type { DomScanner } from './scanner';
import type { DomRenderer } from './renderer';
import type { StateManager } from './state-manager';
import type { LifecycleManager } from './lifecycle-manager';
import { TranslationEventBus, ViewModeController } from './types';

/**
 * 🎯 重构后的翻译编排器 - 轻量级协调者
 * 
 * 新的职责边界：
 * - 组件协调和生命周期管理
 * - 翻译流程的高级控制
 * - 状态管理和进度报告
 * - 错误处理和降级策略
 */
export class RefactoredTranslationOrchestrator {
  // 核心组件
  private stateManager: StateManager;
  private lifecycleManager: LifecycleManager;
  private eventBus: TranslationEventBus;
  private viewController?: ViewModeController;

  // 重构后的专门管理器
  private scanStrategy: ScanStrategyManager;
  private batchProcessor: BatchTranslationProcessor;
  private errorHandler: TranslationErrorHandler;
  private safeWrapper: ReturnType<typeof createSafeTranslationWrapper>;

  private config: Required<TranslationConfig>;
  private abortController?: AbortController;
  private logger = console;

  constructor(
    scanner: DomScanner,
    renderer: DomRenderer,
    stateManager: StateManager,
    lifecycleManager: LifecycleManager,
    eventBus: TranslationEventBus,
    config: OrchestratorConfig
  ) {
    this.stateManager = stateManager;
    this.lifecycleManager = lifecycleManager;
    this.eventBus = eventBus;
    this.viewController = config.viewController;
    this.config = this.mergeDefaultConfig(config);

    // 🎯 初始化重构后的管理器
    this.scanStrategy = new ScanStrategyManager(scanner, this.config.debug);
    this.batchProcessor = new BatchTranslationProcessor(renderer, {
      targetLanguage: this.config.targetLanguage,
      enableAccessibility: this.config.enableAccessibility,
      debug: this.config.debug,
      translateFunction: this.config.translateFunction,
      onError: this.config.onError
    });

    // 🚨 统一错误处理系统
    this.errorHandler = new TranslationErrorHandler(
      this.logger, 
      this.config.debug, 
      this.config.onError ? (error) => this.config.onError!(error, 'orchestrator') : undefined
    );
    this.safeWrapper = createSafeTranslationWrapper(this.errorHandler, 'TranslationOrchestrator');

    if (this.config.debug) {
      this.logger.info('RefactoredTranslationOrchestrator initialized with specialized managers');
    }
  }

  /**
   * 🎯 翻译整个页面 - 重构后的简化版本
   */
  async translatePage(options: {
    rootNode?: HTMLElement;
    excludeSelectors?: string[];
    minTextLength?: number;
    maxDepth?: number;
  } = {}): Promise<BatchTranslationResult> {
    const startTime = performance.now();

    const result = await this.safeWrapper.safeTranslate(
      'translatePage',
      async () => {
        // 1. 状态检查和预检
        if (!this.stateManager.canStart()) {
          throw new Error(`Cannot start translation in state: ${this.stateManager.getState()}`);
        }

        this.abortController = new AbortController();
        this.batchProcessor.setAbortController(this.abortController);

        // 2. 执行智能扫描
        const scanResult = await this.performSmartScan(options);
        if (scanResult.nodes.length === 0) {
          return this.createEmptyResult(startTime);
        }

        // 3. 执行批量翻译和渲染
        const translationResult = await this.executeTranslationWorkflow(scanResult.nodes);

        // 4. 完成和清理
        this.finalizePage();
        
        return this.buildFinalResult(scanResult, translationResult, startTime);
      },
      this.createEmptyResult(startTime)
    );

    return result || this.createEmptyResult(startTime);
  }

  /**
   * 🧠 执行智能扫描 - 简化后的方法
   */
  private async performSmartScan(options: any): Promise<ScanStrategyResult> {
    this.stateManager.setState(TranslationState.SCANNING, 'translatePage');

    const scanConfig: ScanConfig = {
      rootNode: options.rootNode || document.body,
      excludeSelectors: options.excludeSelectors,
      minTextLength: options.minTextLength || 5,
      maxDepth: options.maxDepth,
      enablePerformanceMonitoring: this.config.enablePerformanceOptimization
    };

    const scanResult = await this.scanStrategy.performIntelligentScan(scanConfig);
    
    this.stateManager.updateScanStats(
      scanResult.stats.totalScanned || scanResult.nodes.length,
      scanResult.nodes.length
    );

    return scanResult;
  }

  /**
   * 🚀 执行翻译工作流程 - 简化后的方法  
   */
  private async executeTranslationWorkflow(nodes: ScannedNode[]): Promise<ProcessorResult> {
    this.stateManager.setState(TranslationState.TRANSLATING, 'start-translation');

    // 根据配置选择翻译策略
    if (this.config.enableLazyLoading && nodes.length > 50) {
      return this.executeLazyTranslationWorkflow(nodes);
    } else {
      return this.batchProcessor.processBatch(nodes);
    }
  }

  /**
   * 🔄 执行懒加载翻译工作流程
   */
  private async executeLazyTranslationWorkflow(nodes: ScannedNode[]): Promise<ProcessorResult> {
    const elements = nodes.map(n => n.element);
    const { visibleElements, invisibleElements } = 
      this.lifecycleManager.categorizeElementsByVisibility(elements);

    // 处理可见节点
    const visibleNodes = nodes.filter(node => visibleElements.includes(node.element));
    const visibleResult = await this.batchProcessor.processBatch(visibleNodes);

    // 设置不可见节点的懒加载
    const invisibleNodes = nodes.filter(node => invisibleElements.includes(node.element));
    if (invisibleNodes.length > 0) {
      this.setupLazyTranslationForInvisibleNodes(invisibleNodes);
    }

    return visibleResult;
  }

  /**
   * 🔧 设置不可见节点的懒加载
   */
  private setupLazyTranslationForInvisibleNodes(invisibleNodes: ScannedNode[]): void {
    const batchLazyItems = invisibleNodes.map((node, index) => ({
      id: `lazy-translate-${Date.now()}-${index}`,
      element: node.element,
      priority: this.lifecycleManager.calculateLazyPriority(node.element),
      once: true,
      metadata: { text: node.text.slice(0, 100), nodeIndex: index }
    }));

    const batchHandler = async (batchItems: any[]) => {
      const batchNodes = batchItems.map(item => 
        invisibleNodes.find(node => node.element === item.element)
      ).filter(Boolean) as ScannedNode[];

      await this.safeWrapper.safeTranslate(
        'lazyBatchTranslation',
        () => this.batchProcessor.processBatch(batchNodes),
        { successful: 0, failed: batchNodes.length, results: [] }
      );
    };

    this.lifecycleManager.setBatchLazyHandler(batchHandler);
    this.lifecycleManager.observeLazyBatch(batchLazyItems);
  }

  /**
   * 🎯 完成页面翻译
   */
  private finalizePage(): void {
    if (this.viewController) {
      this.viewController.setViewMode(ViewMode.DUAL);
    }
    this.stateManager.setState(TranslationState.COMPLETED, 'success');
  }

  /**
   * 📊 构建最终结果
   */
  private buildFinalResult(
    scanResult: ScanStrategyResult, 
    translationResult: ProcessorResult, 
    startTime: number
  ): BatchTranslationResult {
    const totalDuration = performance.now() - startTime;
    return {
      totalCount: scanResult.nodes.length,
      successCount: translationResult.successful,
      failureCount: translationResult.failed,
      results: translationResult.results,
      totalDuration
    };
  }

  /**
   * 📝 创建空结果
   */
  private createEmptyResult(startTime: number): BatchTranslationResult {
    return {
      totalCount: 0,
      successCount: 0,
      failureCount: 0,
      results: [],
      totalDuration: performance.now() - startTime
    };
  }

  /**
   * 🛑 停止翻译
   */
  stopTranslation(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.stateManager.setState(TranslationState.IDLE, 'user-stop');
    }
  }

  /**
   * 🧹 清除所有翻译
   */
  clearTranslations(): void {
    // 清除逻辑将委托给适当的管理器
    if (this.viewController) {
      this.viewController.setViewMode(ViewMode.ORIGIN);
    }
    this.stateManager.reset();
  }

  /**
   * 📊 获取统计信息
   */
  getStats() {
    return {
      translation: this.stateManager.getStats(),
      scanStrategy: this.scanStrategy.getStats(),
      batchProcessor: this.batchProcessor.getStats(),
      errorHandler: this.errorHandler.getStats()
    };
  }

  /**
   * 🧹 销毁编排器
   */
  destroy(): void {
    this.stopTranslation();
    this.clearTranslations();

    // 销毁专门管理器
    this.scanStrategy.destroy();
    this.batchProcessor.destroy();

    // 销毁核心组件
    this.stateManager.destroy();
    this.lifecycleManager.destroy();
    this.eventBus.destroy();

    if (this.config.debug) {
      this.logger.info('RefactoredTranslationOrchestrator destroyed');
    }
  }

  /**
   * 🔧 合并默认配置
   */
  private mergeDefaultConfig(config: OrchestratorConfig): Required<TranslationConfig> {
    return {
      targetLanguage: config.targetLanguage ?? 'zh',
      concurrency: config.concurrency ?? 3,
      debug: config.debug ?? true,
      enableAccessibility: config.enableAccessibility ?? true,
      enableLazyLoading: config.enableLazyLoading ?? true,
      enablePerformanceOptimization: config.enablePerformanceOptimization ?? true,
      enableSmartInjection: config.enableSmartInjection ?? true,
      enableAdvancedInjection: config.enableAdvancedInjection ?? false,
      translateFunction: config.translateFunction || (async (text: string) => text),
      onError: config.onError ?? (() => { }),
      onProgress: config.onProgress ?? (() => { })
    };
  }
}