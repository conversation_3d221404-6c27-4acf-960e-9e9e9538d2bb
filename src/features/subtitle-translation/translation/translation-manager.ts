import { translateService } from '@features/translate';
import { TranslateOptions } from '@features/translate/types';
import { StandardSubtitle, SubtitleTranslationConfig, TranslationProgress } from '../types';
import { SubtitleError, SubtitleErrorType } from '../utils/error-handler';

/**
 * 字幕翻译管理器
 * 专门负责字幕的批量翻译、缓存管理和进度跟踪
 */
export class SubtitleTranslationManager {
  private config: SubtitleTranslationConfig;
  private translationProgress = new Map<string, TranslationProgress>();
  private batchProcessor?: BatchProcessor;
  private isInitialized = false;

  // 内存保护常量
  private static readonly MAX_PROGRESS_RECORDS = 50;
  private static readonly PROGRESS_CLEANUP_INTERVAL = 5 * 60 * 1000; // 5分钟
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: SubtitleTranslationConfig) {
    this.config = { ...config };
    // 启动定时清理机制
    this.startPeriodicCleanup();
  }

  /**
   * 初始化翻译管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 初始化批处理器
      this.batchProcessor = new BatchProcessor({
        maxBatchSize: this.config.batchSize,
        maxConcurrency: this.config.maxConcurrency,
        maxWaitTime: 200, // 200ms 批处理等待时间
        retryCount: this.config.retryCount
      });

      this.isInitialized = true;
      console.log('✅ [SubtitleTranslationManager] 翻译管理器初始化完成');
    } catch (error) {
      console.error('❌ [SubtitleTranslationManager] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 翻译字幕列表 - 主要接口
   */
  async translateSubtitles(
    subtitles: StandardSubtitle[], 
    options: { sourceLang?: string; targetLang?: string } = {}
  ): Promise<StandardSubtitle[]> {
    if (!subtitles || subtitles.length === 0) {
      return [];
    }

    const taskId = this.generateTaskId();
    
    try {
      // 初始化翻译进度
      this.translationProgress.set(taskId, {
        taskId,
        total: subtitles.length,
        completed: 0,
        failed: 0,
        status: 'processing',
        startTime: Date.now()
      });

      // 检查内存使用并及时清理
      this.checkAndCleanupProgress();

      const translateOptions: TranslateOptions = {
        from: options.sourceLang || this.config.sourceLang,
        to: options.targetLang || this.config.targetLang,
        timeout: 15000 // 字幕翻译使用较短的超时时间
      };

      console.log(`🔄 [SubtitleTranslationManager] 开始翻译 ${subtitles.length} 条字幕`);
      console.log(`🌐 [SubtitleTranslationManager] 翻译方向: ${translateOptions.from} → ${translateOptions.to}`);

      // 提取需要翻译的文本
      const textsToTranslate = subtitles.map(subtitle => subtitle.text);

      let translatedTexts: string[] = [];

      if (this.config.batchSize <= 1) {
        // 单条翻译模式
        translatedTexts = await this.translateTextsSequentially(textsToTranslate, translateOptions, taskId);
      } else {
        // 批量翻译模式
        translatedTexts = await this.translateTextsBatch(textsToTranslate, translateOptions, taskId);
      }

      // 合并翻译结果
      const result = subtitles.map((subtitle, index) => ({
        ...subtitle,
        translatedText: translatedTexts[index] || ''
      }));

      // 完成翻译
      const progress = this.translationProgress.get(taskId);
      if (progress) {
        progress.status = 'completed';
        progress.endTime = Date.now();
        progress.duration = progress.startTime ? progress.endTime - progress.startTime : 0;
        this.translationProgress.set(taskId, progress);
      }

      console.log(`✅ [SubtitleTranslationManager] 翻译完成: ${result.filter(s => s.translatedText).length}/${subtitles.length} 条成功`);
      return result;

    } catch (error) {
      // 标记翻译失败
      const progress = this.translationProgress.get(taskId);
      if (progress) {
        progress.status = 'failed';
        progress.error = error instanceof Error ? error.message : String(error);
        progress.endTime = Date.now();
        progress.duration = progress.startTime ? progress.endTime - progress.startTime : 0;
        this.translationProgress.set(taskId, progress);
      }
      
      console.error('🚨 [SubtitleTranslationManager] 翻译失败:', error);
      
      throw new SubtitleError(
        SubtitleErrorType.TRANSLATION_FAILED,
        `字幕翻译失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 顺序翻译文本（单条模式）
   */
  private async translateTextsSequentially(
    texts: string[], 
    options: TranslateOptions, 
    taskId: string
  ): Promise<string[]> {
    const results: string[] = [];
    
    for (let i = 0; i < texts.length; i++) {
      try {
        const translation = await translateService.translateText(texts[i], options);
        results.push(translation);
        
        // 更新进度
        this.updateProgress(taskId, 1, 0);
        
        // 添加延迟避免请求过于频繁
        if (i < texts.length - 1) {
          await this.delay(100);
        }
      } catch (error) {
        console.warn(`⚠️ [SubtitleTranslationManager] 单条翻译失败 [${i}]:`, error);
        results.push('');
        this.updateProgress(taskId, 0, 1);
      }
    }
    
    return results;
  }

  /**
   * 批量翻译文本
   */
  private async translateTextsBatch(
    texts: string[], 
    options: TranslateOptions, 
    taskId: string
  ): Promise<string[]> {
    const batchSize = this.config.batchSize;
    const maxConcurrency = this.config.maxConcurrency || 2;
    const results: string[] = new Array(texts.length).fill('');
    
    // 将文本分割成批次
    const batches: { texts: string[], startIndex: number }[] = [];
    for (let i = 0; i < texts.length; i += batchSize) {
      batches.push({
        texts: texts.slice(i, i + batchSize),
        startIndex: i
      });
    }

    console.log(`🔄 [SubtitleTranslationManager] 开始并发批处理: ${batches.length} 个批次，最大并发: ${maxConcurrency}`);

    // 并发控制：使用 Promise.allSettled 控制同时处理的批次数量
    for (let i = 0; i < batches.length; i += maxConcurrency) {
      const concurrentBatches = batches.slice(i, i + maxConcurrency);
      
      console.log(`🔄 [SubtitleTranslationManager] 处理批次组 ${Math.floor(i / maxConcurrency) + 1}/${Math.ceil(batches.length / maxConcurrency)} (${concurrentBatches.length} 个并发批次)`);
      
      // 创建并发批处理任务
      const batchPromises = concurrentBatches.map((batchInfo, batchIndex) =>
        this.processSingleBatch(
          batchInfo.texts, 
          options, 
          taskId, 
          i + batchIndex + 1, 
          batches.length,
          batchInfo.startIndex
        )
      );

      // 等待当前组的所有批次完成
      const batchResults = await Promise.allSettled(batchPromises);

      // 处理批次结果
      batchResults.forEach((result, batchIndex) => {
        const currentBatch = concurrentBatches[batchIndex];
        
        if (result.status === 'fulfilled' && result.value.success) {
          // 成功的批次，存储翻译结果
          result.value.translations.forEach((translation, textIndex) => {
            results[currentBatch.startIndex + textIndex] = translation;
          });
        } else {
          // 失败的批次，标记为空字符串（已在 processSingleBatch 中处理补偿逻辑）
          if (result.status === 'fulfilled' && !result.value.success) {
            result.value.translations.forEach((translation, textIndex) => {
              results[currentBatch.startIndex + textIndex] = translation;
            });
          } else {
            // Promise 被拒绝的情况
            const reason = result.status === 'rejected' ? result.reason : 'Unknown error';
            console.error(`🚨 [SubtitleTranslationManager] 批次 ${i + batchIndex + 1} Promise被拒绝:`, reason);
            for (let j = 0; j < currentBatch.texts.length; j++) {
              results[currentBatch.startIndex + j] = '';
            }
            this.updateProgress(taskId, 0, currentBatch.texts.length);
          }
        }
      });

      // 批次组间延迟，避免过载
      if (i + maxConcurrency < batches.length) {
        await this.delay(200);
      }
    }
    
    console.log(`✅ [SubtitleTranslationManager] 并发批处理完成，成功翻译: ${results.filter(r => r !== '').length}/${texts.length}`);
    return results;
  }

  /**
   * 处理单个批次的翻译（带错误恢复）
   */
  private async processSingleBatch(
    batchTexts: string[], 
    options: TranslateOptions, 
    taskId: string,
    batchNumber: number,
    totalBatches: number,
    startIndex: number
  ): Promise<{ success: boolean; translations: string[] }> {
    try {
      console.log(`📦 [SubtitleTranslationManager] 处理批次 ${batchNumber}/${totalBatches} (${batchTexts.length} 条)`);
      
      const batchTranslations = await translateService.translateTexts(batchTexts, options);
      
      // 更新进度
      this.updateProgress(taskId, batchTexts.length, 0);
      
      console.log(`✅ [SubtitleTranslationManager] 批次 ${batchNumber} 成功完成`);
      return {
        success: true,
        translations: batchTranslations.map(t => t || '')
      };
      
    } catch (error) {
      console.warn(`⚠️ [SubtitleTranslationManager] 批次 ${batchNumber} 失败，尝试单条补偿翻译:`, error);
      
      // 批次失败时，尝试单条翻译作为补偿
      const fallbackTranslations: string[] = [];
      let successCount = 0;
      let failureCount = 0;
      
      for (let i = 0; i < batchTexts.length; i++) {
        try {
          const singleTranslation = await translateService.translateText(batchTexts[i], options);
          fallbackTranslations.push(singleTranslation || '');
          successCount++;
          
          // 单条翻译间的短暂延迟
          if (i < batchTexts.length - 1) {
            await this.delay(50);
          }
        } catch (singleError) {
          console.warn(`⚠️ [SubtitleTranslationManager] 单条补偿翻译失败 [${startIndex + i}]:`, singleError);
          fallbackTranslations.push('');
          failureCount++;
        }
      }
      
      // 更新进度
      this.updateProgress(taskId, successCount, failureCount);
      
      console.log(`🔄 [SubtitleTranslationManager] 批次 ${batchNumber} 补偿完成: ${successCount}/${batchTexts.length} 成功`);
      return {
        success: false,
        translations: fallbackTranslations
      };
    }
  }

  /**
   * 获取翻译进度
   */
  getTranslationProgress(taskId?: string): TranslationProgress | TranslationProgress[] {
    if (taskId) {
      return this.translationProgress.get(taskId) || {
        taskId,
        total: 0,
        completed: 0,
        failed: 0,
        status: 'not_found',
        startTime: Date.now()
      };
    }
    
    return Array.from(this.translationProgress.values());
  }

  /**
   * 取消翻译任务
   */
  cancelTranslation(taskId: string): void {
    const progress = this.translationProgress.get(taskId);
    if (progress && progress.status === 'processing') {
      progress.status = 'cancelled';
      progress.endTime = Date.now();
      progress.duration = progress.startTime ? progress.endTime - progress.startTime : 0;
      this.translationProgress.set(taskId, progress);
      
      console.log(`🛑 [SubtitleTranslationManager] 翻译任务已取消: ${taskId}`);
    }
  }

  /**
   * 更新翻译配置
   */
  updateConfig(config: Partial<SubtitleTranslationConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('🔧 [SubtitleTranslationManager] 配置已更新');
  }

  /**
   * 获取当前配置
   */
  getConfig(): SubtitleTranslationConfig {
    return { ...this.config };
  }

  /**
   * 启动定时清理机制
   */
  private startPeriodicCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupProgress();
    }, SubtitleTranslationManager.PROGRESS_CLEANUP_INTERVAL);
  }

  /**
   * 检查并清理过期进度记录（主动清理）
   */
  private checkAndCleanupProgress(): void {
    if (this.translationProgress.size > SubtitleTranslationManager.MAX_PROGRESS_RECORDS) {
      console.log(`🧹 [SubtitleTranslationManager] 进度记录数量过多 (${this.translationProgress.size})，开始清理`);
      this.cleanupProgress();
    }
  }

  /**
   * 清理过期的进度记录（增强版）
   */
  cleanupProgress(): void {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5分钟
    let cleanedCount = 0;

    // 1. 清理过期记录
    for (const [taskId, progress] of this.translationProgress.entries()) {
      if (progress.endTime && (now - progress.endTime) > maxAge) {
        this.translationProgress.delete(taskId);
        cleanedCount++;
      }
    }

    // 2. 如果仍然过多，清理最旧的记录
    if (this.translationProgress.size > SubtitleTranslationManager.MAX_PROGRESS_RECORDS) {
      const entries = Array.from(this.translationProgress.entries());
      const toDelete = entries
        .sort((a, b) => (a[1].startTime || 0) - (b[1].startTime || 0))
        .slice(0, entries.length - SubtitleTranslationManager.MAX_PROGRESS_RECORDS);

      toDelete.forEach(([taskId]) => {
        this.translationProgress.delete(taskId);
        cleanedCount++;
      });
    }

    if (cleanedCount > 0) {
      console.log(`🧹 [SubtitleTranslationManager] 已清理 ${cleanedCount} 条过期进度记录，当前记录数: ${this.translationProgress.size}`);
    }
  }

  /**
   * 获取内存使用统计
   */
  getMemoryStats(): { progressRecords: number; maxRecords: number; memoryUsage: string } {
    const progressRecords = this.translationProgress.size;
    const maxRecords = SubtitleTranslationManager.MAX_PROGRESS_RECORDS;
    const usagePercent = Math.round((progressRecords / maxRecords) * 100);
    
    return {
      progressRecords,
      maxRecords,
      memoryUsage: `${usagePercent}% (${progressRecords}/${maxRecords})`
    };
  }

  /**
   * 动态翻译单个文本 - 使用智能批处理器
   * 适用于实时翻译场景，请求会被加入动态队列智能调度
   */
  async translateTextDynamically(
    text: string,
    options: { sourceLang?: string; targetLang?: string } = {}
  ): Promise<string> {
    if (!this.isInitialized) {
      throw new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        '翻译管理器未初始化，请先调用 initialize()'
      );
    }

    if (!this.batchProcessor) {
      throw new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        'BatchProcessor未初始化'
      );
    }

    const translateOptions: TranslateOptions = {
      from: options.sourceLang || this.config.sourceLang,
      to: options.targetLang || this.config.targetLang,
      timeout: 10000
    };

    try {
      console.log(`🔄 [SubtitleTranslationManager] 动态翻译请求: "${text.substring(0, 50)}..."`);
      
      const translation = await this.batchProcessor.addToQueue(text, translateOptions);
      
      console.log(`✅ [SubtitleTranslationManager] 动态翻译完成: "${translation.substring(0, 50)}..."`);
      return translation;

    } catch (error) {
      console.error('🚨 [SubtitleTranslationManager] 动态翻译失败:', error);
      
      throw new SubtitleError(
        SubtitleErrorType.TRANSLATION_FAILED,
        `动态翻译失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * 批量动态翻译 - 将多个请求添加到智能队列
   * 与 translateSubtitles 不同，这个方法不会等待所有翻译完成
   */
  async translateTextsDynamically(
    texts: string[],
    options: { sourceLang?: string; targetLang?: string } = {}
  ): Promise<Promise<string>[]> {
    if (!this.isInitialized) {
      throw new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        '翻译管理器未初始化，请先调用 initialize()'
      );
    }

    if (!this.batchProcessor) {
      throw new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        'BatchProcessor未初始化'
      );
    }

    const translateOptions: TranslateOptions = {
      from: options.sourceLang || this.config.sourceLang,
      to: options.targetLang || this.config.targetLang,
      timeout: 10000
    };

    console.log(`🔄 [SubtitleTranslationManager] 批量动态翻译: ${texts.length} 个请求`);

    // 返回Promise数组，不等待完成
    return texts.map(text => this.batchProcessor!.addToQueue(text, translateOptions));
  }

  /**
   * 获取 BatchProcessor 统计信息
   */
  getBatchProcessorStats(): BatchProcessorStats | null {
    return this.batchProcessor?.getStats() || null;
  }

  /**
   * 获取 BatchProcessor 队列信息
   */
  getBatchProcessorQueueInfo(): {
    queueSize: number;
    activeRequests: number;
    cacheSize: number;
    oldestRequestAge?: number;
  } | null {
    return this.batchProcessor?.getQueueInfo() || null;
  }

  /**
   * 强制刷新 BatchProcessor 队列
   */
  async flushBatchProcessor(): Promise<void> {
    if (this.batchProcessor) {
      console.log('🚀 [SubtitleTranslationManager] 强制刷新批处理器队列');
      await this.batchProcessor.flush();
    }
  }

  /**
   * 演示实时字幕翻译场景 - 模拟动态到达的字幕数据
   * 这个方法展示了 BatchProcessor 相比传统批量翻译的优势
   */
  async demonstrateRealtimeTranslation(
    subtitles: StandardSubtitle[],
    options: { sourceLang?: string; targetLang?: string } = {},
    simulateDelay = true
  ): Promise<StandardSubtitle[]> {
    if (!this.isInitialized || !this.batchProcessor) {
      throw new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        '翻译管理器或BatchProcessor未初始化'
      );
    }

    console.log(`🎬 [SubtitleTranslationManager] 开始实时字幕翻译演示: ${subtitles.length} 条字幕`);
    
    const results: StandardSubtitle[] = [];
    const translationPromises: Promise<void>[] = [];

    // 模拟字幕数据动态到达的场景
    for (let i = 0; i < subtitles.length; i++) {
      const subtitle = subtitles[i];
      
      // 创建翻译任务
      const translationPromise = this.translateTextDynamically(subtitle.text, options)
        .then(translatedText => {
          const translatedSubtitle: StandardSubtitle = {
            ...subtitle,
            translatedText
          };
          
          results[i] = translatedSubtitle; // 保持原始顺序
          
          console.log(`📝 [实时字幕] ${i + 1}/${subtitles.length} 翻译完成: "${translatedText.substring(0, 30)}..."`);
          
          // 在实际应用中，这里会立即显示翻译结果
          // this.displaySubtitle(translatedSubtitle);
        })
        .catch(error => {
          console.warn(`⚠️ [实时字幕] ${i + 1}/${subtitles.length} 翻译失败:`, error);
          results[i] = { ...subtitle, translatedText: '' };
        });

      translationPromises.push(translationPromise);

      // 模拟字幕动态到达的延迟
      if (simulateDelay && i < subtitles.length - 1) {
        const delay = Math.random() * 200 + 100; // 100-300ms 随机延迟
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // 等待所有翻译完成
    await Promise.allSettled(translationPromises);

    // 填充任何未完成的结果
    for (let i = 0; i < subtitles.length; i++) {
      if (!results[i]) {
        results[i] = { ...subtitles[i], translatedText: '' };
      }
    }

    const successCount = results.filter(r => r.translatedText).length;
    console.log(`✅ [实时字幕翻译] 演示完成: ${successCount}/${subtitles.length} 成功翻译`);

    return results;
  }

  /**
   * 比较批量翻译 vs 动态翻译的性能
   */
  async compareTranslationMethods(
    texts: string[],
    options: { sourceLang?: string; targetLang?: string } = {}
  ): Promise<{
    batchMethod: { results: string[]; duration: number; method: string };
    dynamicMethod: { results: string[]; duration: number; method: string };
    comparison: {
      speedDifference: number;
      batchProcessorStats: BatchProcessorStats | null;
      recommendation: string;
    };
  }> {
    console.log(`📊 [SubtitleTranslationManager] 开始翻译方法对比测试: ${texts.length} 个文本`);

    // 1. 传统批量翻译测试
    const batchStartTime = Date.now();
    const batchResults = await this.translateTextsBatch(texts, {
      from: options.sourceLang || this.config.sourceLang,
      to: options.targetLang || this.config.targetLang,
      timeout: 15000
    }, 'comparison_batch_' + Date.now());
    const batchDuration = Date.now() - batchStartTime;

    // 2. 动态翻译测试
    const dynamicStartTime = Date.now();
    const dynamicPromises = await this.translateTextsDynamically(texts, options);
    const dynamicResults = await Promise.all(dynamicPromises);
    const dynamicDuration = Date.now() - dynamicStartTime;

    // 3. 性能比较
    const speedDifference = ((batchDuration - dynamicDuration) / batchDuration) * 100;
    const batchProcessorStats = this.getBatchProcessorStats();

    let recommendation: string;
    if (Math.abs(speedDifference) < 10) {
      recommendation = '两种方法性能相近，建议根据具体场景选择：批量翻译适合离线处理，动态翻译适合实时场景';
    } else if (speedDifference > 0) {
      recommendation = `动态翻译快 ${Math.abs(speedDifference).toFixed(1)}%，推荐用于实时翻译场景`;
    } else {
      recommendation = `批量翻译快 ${Math.abs(speedDifference).toFixed(1)}%，推荐用于大批量离线处理`;
    }

    const result = {
      batchMethod: {
        results: batchResults,
        duration: batchDuration,
        method: 'Traditional Batch Translation'
      },
      dynamicMethod: {
        results: dynamicResults,
        duration: dynamicDuration,
        method: 'Dynamic Queue Translation (BatchProcessor)'
      },
      comparison: {
        speedDifference,
        batchProcessorStats,
        recommendation
      }
    };

    console.log(`📊 [翻译方法对比] 测试完成:`, {
      batchDuration: `${batchDuration}ms`,
      dynamicDuration: `${dynamicDuration}ms`,
      speedDifference: `${speedDifference.toFixed(1)}%`,
      recommendation
    });

    return result;
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    console.log('🗑️ [SubtitleTranslationManager] 开始销毁翻译管理器');
    
    // 清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    
    // 异步销毁 BatchProcessor
    if (this.batchProcessor) {
      try {
        await this.batchProcessor.destroy();
        console.log('✅ [SubtitleTranslationManager] BatchProcessor 已销毁');
      } catch (error) {
        console.error('❌ [SubtitleTranslationManager] BatchProcessor 销毁失败:', error);
      }
      this.batchProcessor = undefined;
    }
    
    this.translationProgress.clear();
    this.isInitialized = false;
    
    console.log('✅ [SubtitleTranslationManager] 翻译管理器已销毁');
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    // 使用更安全的UUID生成算法，包含时间戳、随机UUID和额外随机字符串
    const timestamp = Date.now();
    const uuid = crypto.randomUUID?.() || `${Math.random().toString(36).substr(2, 9)}-${Math.random().toString(36).substr(2, 4)}`;
    const extraRandom = Math.random().toString(36).substr(2, 5);
    return `subtitle_translation_${timestamp}_${uuid}_${extraRandom}`;
  }

  /**
   * 更新翻译进度
   */
  private updateProgress(taskId: string, completed: number, failed: number): void {
    const progress = this.translationProgress.get(taskId);
    if (progress) {
      progress.completed += completed;
      progress.failed += failed;
      this.translationProgress.set(taskId, progress);
    }
  }

  /**
   * 延迟工具函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 批处理器配置接口
 */
/**
 * 翻译请求接口
 */
interface TranslationRequest {
  id: string;
  text: string;
  options: TranslateOptions;
  promise: Promise<string>;
  resolve: (value: string) => void;
  reject: (reason: any) => void;
  timestamp: number;
  retryCount: number;
}

/**
 * 批处理器配置接口
 */
interface BatchProcessorConfig {
  maxBatchSize: number;
  maxConcurrency: number;
  maxWaitTime: number;
  retryCount: number;
  enableCache?: boolean;
  cacheExpireTime?: number;
}

/**
 * 批处理统计接口
 */
interface BatchProcessorStats {
  totalRequests: number;
  completedRequests: number;
  failedRequests: number;
  queueSize: number;
  activeRequestsCount: number;
  cacheHitCount: number;
  averageWaitTime: number;
  averageBatchSize: number;
}

/**
 * 简化的批处理器类
 */
/**
 * 智能批量翻译处理器 - 动态队列式翻译调度器
 * 
 * 核心特性：
 * - 动态队列管理：请求到达时立即加入队列
 * - 智能触发机制：队列满或超时自动处理批次
 * - 真正并发控制：同时处理多个翻译批次
 * - 请求去重缓存：避免重复翻译相同内容
 * - 指数退避重试：智能错误恢复策略
 */
class BatchProcessor {
  private config: BatchProcessorConfig;
  private queue: TranslationRequest[] = [];
  private timer: NodeJS.Timeout | null = null;
  private activeRequests = new Set<Promise<any>>();
  private requestCache = new Map<string, Promise<string>>();
  private stats: BatchProcessorStats;
  private isDestroyed = false;

  constructor(config: BatchProcessorConfig) {
    this.config = {
      enableCache: true,
      cacheExpireTime: 5 * 60 * 1000, // 5分钟缓存过期
      ...config
    };
    
    this.stats = {
      totalRequests: 0,
      completedRequests: 0,
      failedRequests: 0,
      queueSize: 0,
      activeRequestsCount: 0,
      cacheHitCount: 0,
      averageWaitTime: 0,
      averageBatchSize: 0
    };

    // 启动定期缓存清理
    if (this.config.enableCache) {
      this.startCacheCleanup();
    }
  }

  /**
   * 添加翻译请求到队列 - 主要入口方法
   */
  async addToQueue(text: string, options: TranslateOptions): Promise<string> {
    if (this.isDestroyed) {
      throw new Error('BatchProcessor has been destroyed');
    }

    // 生成缓存键
    const cacheKey = this.generateCacheKey(text, options);

    // 检查缓存
    if (this.config.enableCache && this.requestCache.has(cacheKey)) {
      this.stats.cacheHitCount++;
      console.log('🎯 [BatchProcessor] 缓存命中，直接返回结果');
      return this.requestCache.get(cacheKey)!;
    }

    // 创建新的翻译请求
    const request = this.createTranslationRequest(text, options);
    
    // 缓存Promise（如果启用缓存）
    if (this.config.enableCache) {
      this.requestCache.set(cacheKey, request.promise);
    }

    // 加入队列
    this.queue.push(request);
    this.stats.totalRequests++;
    this.stats.queueSize = this.queue.length;

    console.log(`📥 [BatchProcessor] 请求已加入队列 (队列长度: ${this.queue.length}/${this.config.maxBatchSize})`);

    // 智能批次触发逻辑
    if (this.queue.length >= this.config.maxBatchSize) {
      // 队列满了，立即处理
      console.log('🚀 [BatchProcessor] 队列已满，立即处理批次');
      this.processBatch();
    } else if (!this.timer) {
      // 启动等待定时器，等待更多请求到达
      console.log(`⏰ [BatchProcessor] 启动等待定时器 (${this.config.maxWaitTime}ms)`);
      this.timer = setTimeout(() => {
        console.log('⏰ [BatchProcessor] 等待超时，处理当前批次');
        this.processBatch();
      }, this.config.maxWaitTime);
    }

    return request.promise;
  }

  /**
   * 处理批次 - 核心调度逻辑
   */
  private async processBatch(): Promise<void> {
    // 清除定时器
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    if (this.queue.length === 0) {
      return;
    }

    // 检查并发限制
    while (this.activeRequests.size < this.config.maxConcurrency && this.queue.length > 0) {
      // 提取一个批次
      const batchSize = Math.min(this.config.maxBatchSize, this.queue.length);
      const batch = this.queue.splice(0, batchSize);
      this.stats.queueSize = this.queue.length;

      console.log(`🔄 [BatchProcessor] 开始处理批次: ${batch.length} 个请求 (活跃批次: ${this.activeRequests.size + 1}/${this.config.maxConcurrency})`);

      // 异步处理批次
      const batchPromise = this.executeBatch(batch);
      this.activeRequests.add(batchPromise);
      this.stats.activeRequestsCount = this.activeRequests.size;

      // 批次完成后清理
      batchPromise.finally(() => {
        this.activeRequests.delete(batchPromise);
        this.stats.activeRequestsCount = this.activeRequests.size;
        
        // 如果还有队列中的请求，继续处理
        if (this.queue.length > 0 && !this.isDestroyed) {
          setImmediate(() => this.processBatch());
        }
      });
    }
  }

  /**
   * 执行单个批次的翻译
   */
  private async executeBatch(batch: TranslationRequest[]): Promise<void> {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
    console.log(`📦 [BatchProcessor] 执行批次 ${batchId}: ${batch.length} 个请求`);

    try {
      // 准备批次数据
      const texts = batch.map(req => req.text);
      const options = batch[0].options; // 假设同批次使用相同的翻译选项

      // 执行批量翻译
      const startTime = Date.now();
      const translations = await translateService.translateTexts(texts, options);
      const duration = Date.now() - startTime;

      // 更新统计
      this.updateBatchStats(batch.length, duration);

      // 分发结果到各个请求
      batch.forEach((request, index) => {
        const translation = translations[index] || '';
        request.resolve(translation);
        this.stats.completedRequests++;
      });

      console.log(`✅ [BatchProcessor] 批次 ${batchId} 完成: ${translations.length}/${batch.length} 成功 (耗时: ${duration}ms)`);

    } catch (error) {
      console.warn(`⚠️ [BatchProcessor] 批次 ${batchId} 失败，开始重试逻辑:`, error);
      
      // 批次失败时的处理逻辑
      await this.handleBatchFailure(batch, error);
    }
  }

  /**
   * 处理批次失败 - 智能重试和降级策略
   */
  private async handleBatchFailure(batch: TranslationRequest[], error: any): Promise<void> {
    // 按重试次数分组
    const retryableRequests: TranslationRequest[] = [];
    const finalFailures: TranslationRequest[] = [];

    batch.forEach(request => {
      if (request.retryCount < this.config.retryCount) {
        request.retryCount++;
        retryableRequests.push(request);
      } else {
        finalFailures.push(request);
      }
    });

    // 处理最终失败的请求
    finalFailures.forEach(request => {
      request.reject(new Error(`翻译失败: ${error.message || error}`));
      this.stats.failedRequests++;
    });

    if (retryableRequests.length > 0) {
      console.log(`🔄 [BatchProcessor] 重试 ${retryableRequests.length} 个请求 (剩余重试次数: ${this.config.retryCount - retryableRequests[0].retryCount})`);
      
      // 使用指数退避重试
      const retryDelay = Math.min(1000 * Math.pow(2, retryableRequests[0].retryCount - 1), 10000);
      
      setTimeout(async () => {
        // 尝试单个请求重试（降级策略）
        for (const request of retryableRequests) {
          try {
            const translation = await translateService.translateText(request.text, request.options);
            request.resolve(translation);
            this.stats.completedRequests++;
          } catch (singleError) {
            // 单个请求也失败，继续重试流程
            if (request.retryCount < this.config.retryCount) {
              request.retryCount++;
              this.queue.unshift(request); // 放回队列头部优先处理
            } else {
              request.reject(singleError);
              this.stats.failedRequests++;
            }
          }
          
          // 单个重试间的延迟
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }, retryDelay);
    }
  }

  /**
   * 创建翻译请求对象
   */
  private createTranslationRequest(text: string, options: TranslateOptions): TranslationRequest {
    let resolve: (value: string) => void;
    let reject: (reason: any) => void;
    
    const promise = new Promise<string>((res, rej) => {
      resolve = res;
      reject = rej;
    });

    return {
      id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      text,
      options,
      promise,
      resolve: resolve!,
      reject: reject!,
      timestamp: Date.now(),
      retryCount: 0
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(text: string, options: TranslateOptions): string {
    return `${text}_${options.from}_${options.to}`;
  }

  /**
   * 更新批次统计信息
   */
  private updateBatchStats(batchSize: number, duration: number): void {
    // 更新平均等待时间（简化计算）
    this.stats.averageWaitTime = (this.stats.averageWaitTime + duration) / 2;
    
    // 更新平均批次大小
    this.stats.averageBatchSize = (this.stats.averageBatchSize + batchSize) / 2;
  }

  /**
   * 启动缓存清理机制
   */
  private startCacheCleanup(): void {
    const cleanupInterval = setInterval(() => {
      if (this.isDestroyed) {
        clearInterval(cleanupInterval);
        return;
      }
      
      const now = Date.now();
      let cleanedCount = 0;
      
      // 这里简化实现，实际应该跟踪每个缓存项的创建时间
      if (this.requestCache.size > 1000) { // 简单的大小限制
        this.requestCache.clear();
        cleanedCount = this.requestCache.size;
      }
      
      if (cleanedCount > 0) {
        console.log(`🧹 [BatchProcessor] 缓存清理完成: 清理 ${cleanedCount} 条记录`);
      }
    }, this.config.cacheExpireTime || 5 * 60 * 1000);
  }

  /**
   * 获取当前统计信息
   */
  getStats(): BatchProcessorStats {
    return {
      ...this.stats,
      queueSize: this.queue.length,
      activeRequestsCount: this.activeRequests.size
    };
  }

  /**
   * 获取队列状态
   */
  getQueueInfo(): {
    queueSize: number;
    activeRequests: number;
    cacheSize: number;
    oldestRequestAge?: number;
  } {
    const oldestRequest = this.queue.length > 0 ? this.queue[0] : null;
    
    return {
      queueSize: this.queue.length,
      activeRequests: this.activeRequests.size,
      cacheSize: this.requestCache.size,
      oldestRequestAge: oldestRequest ? Date.now() - oldestRequest.timestamp : undefined
    };
  }

  /**
   * 强制处理当前队列中的所有请求
   */
  async flush(): Promise<void> {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    
    console.log(`🚀 [BatchProcessor] 强制刷新队列: ${this.queue.length} 个待处理请求`);
    
    while (this.queue.length > 0 && !this.isDestroyed) {
      await this.processBatch();
    }
    
    // 等待所有活跃请求完成
    await Promise.allSettled(Array.from(this.activeRequests));
  }

  /**
   * 销毁批处理器
   */
  async destroy(): Promise<void> {
    console.log('🗑️ [BatchProcessor] 开始销毁批处理器');
    
    this.isDestroyed = true;
    
    // 清除定时器
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    
    // 拒绝所有排队的请求
    this.queue.forEach(request => {
      request.reject(new Error('BatchProcessor is being destroyed'));
    });
    this.queue = [];
    
    // 等待活跃请求完成
    if (this.activeRequests.size > 0) {
      console.log(`⏳ [BatchProcessor] 等待 ${this.activeRequests.size} 个活跃请求完成`);
      await Promise.allSettled(Array.from(this.activeRequests));
    }
    
    // 清理缓存
    this.requestCache.clear();
    
    console.log('✅ [BatchProcessor] 批处理器已销毁');
  }
}

// 导出翻译管理器的默认实例
export const subtitleTranslationManager = new SubtitleTranslationManager({
  enabled: true,
  sourceLang: 'auto',
  targetLang: 'zh-CN',
  showOriginal: false,
  showTranslated: true,
  batchSize: 10,
  maxConcurrency: 2,
  cacheEnabled: true,
  retryCount: 2
});