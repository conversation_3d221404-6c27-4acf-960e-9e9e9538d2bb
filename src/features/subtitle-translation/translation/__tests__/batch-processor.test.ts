import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { translateService } from '@features/translate';

// Mock translateService
vi.mock('@features/translate', () => ({
  translateService: {
    translateText: vi.fn(),
    translateTexts: vi.fn()
  }
}));

const mockTranslateService = vi.mocked(translateService);

// 导入需要测试的类型和类
// 注意：这些类型定义在 translation-manager.ts 中，需要导出
interface TranslateOptions {
  from: string;
  to: string;
  timeout?: number;
}

interface BatchProcessorConfig {
  maxBatchSize: number;
  maxConcurrency: number;
  maxWaitTime: number;
  retryCount: number;
  enableCache?: boolean;
  cacheExpireTime?: number;
}

interface BatchProcessorStats {
  totalRequests: number;
  completedRequests: number;
  failedRequests: number;
  queueSize: number;
  activeRequestsCount: number;
  cacheHitCount: number;
  averageWaitTime: number;
  averageBatchSize: number;
}

// Mock implementation of BatchProcessor for testing
// 在实际项目中，这应该从 translation-manager.ts 导入
class MockBatchProcessor {
  private config: BatchProcessorConfig;
  private queue: any[] = [];
  private activeRequests = new Set<Promise<any>>();
  private requestCache = new Map<string, Promise<string>>();
  private stats: BatchProcessorStats;
  private isDestroyed = false;
  private timer: NodeJS.Timeout | null = null;

  constructor(config: BatchProcessorConfig) {
    this.config = { enableCache: true, cacheExpireTime: 5 * 60 * 1000, ...config };
    this.stats = {
      totalRequests: 0,
      completedRequests: 0,
      failedRequests: 0,
      queueSize: 0,
      activeRequestsCount: 0,
      cacheHitCount: 0,
      averageWaitTime: 0,
      averageBatchSize: 0
    };
  }

  async addToQueue(text: string, options: TranslateOptions): Promise<string> {
    if (this.isDestroyed) {
      throw new Error('BatchProcessor has been destroyed');
    }

    const cacheKey = `${text}_${options.from}_${options.to}`;
    if (this.config.enableCache && this.requestCache.has(cacheKey)) {
      this.stats.cacheHitCount++;
      return this.requestCache.get(cacheKey)!;
    }

    const request = this.createTranslationRequest(text, options);
    if (this.config.enableCache) {
      this.requestCache.set(cacheKey, request.promise);
    }

    this.queue.push(request);
    this.stats.totalRequests++;
    this.stats.queueSize = this.queue.length;

    if (this.queue.length >= this.config.maxBatchSize) {
      this.processBatch();
    } else if (!this.timer) {
      this.timer = setTimeout(() => this.processBatch(), this.config.maxWaitTime);
    }

    return request.promise;
  }

  private createTranslationRequest(text: string, options: TranslateOptions) {
    let resolve: (value: string) => void;
    let reject: (reason: any) => void;
    
    const promise = new Promise<string>((res, rej) => {
      resolve = res;
      reject = rej;
    });

    return {
      id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      text,
      options,
      promise,
      resolve: resolve!,
      reject: reject!,
      timestamp: Date.now(),
      retryCount: 0
    };
  }

  private async processBatch(): Promise<void> {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }

    while (this.activeRequests.size < this.config.maxConcurrency && this.queue.length > 0) {
      const batchSize = Math.min(this.config.maxBatchSize, this.queue.length);
      const batch = this.queue.splice(0, batchSize);
      this.stats.queueSize = this.queue.length;

      const batchPromise = this.executeBatch(batch);
      this.activeRequests.add(batchPromise);
      this.stats.activeRequestsCount = this.activeRequests.size;

      batchPromise.finally(() => {
        this.activeRequests.delete(batchPromise);
        this.stats.activeRequestsCount = this.activeRequests.size;
        if (this.queue.length > 0 && !this.isDestroyed) {
          setImmediate(() => this.processBatch());
        }
      });
    }
  }

  private async executeBatch(batch: any[]): Promise<void> {
    try {
      const texts = batch.map(req => req.text);
      const options = batch[0].options;
      const translations = await mockTranslateService.translateTexts(texts, options);
      
      batch.forEach((request, index) => {
        const translation = translations[index] || '';
        request.resolve(translation);
        this.stats.completedRequests++;
      });
    } catch (error) {
      batch.forEach(request => {
        request.reject(error);
        this.stats.failedRequests++;
      });
    }
  }

  getStats(): BatchProcessorStats {
    return { ...this.stats, queueSize: this.queue.length, activeRequestsCount: this.activeRequests.size };
  }

  getQueueInfo() {
    return {
      queueSize: this.queue.length,
      activeRequests: this.activeRequests.size,
      cacheSize: this.requestCache.size
    };
  }

  async flush(): Promise<void> {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    while (this.queue.length > 0 && !this.isDestroyed) {
      await this.processBatch();
    }
    await Promise.allSettled(Array.from(this.activeRequests));
  }

  async destroy(): Promise<void> {
    this.isDestroyed = true;
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.queue.forEach(request => request.reject(new Error('BatchProcessor is being destroyed')));
    this.queue = [];
    await Promise.allSettled(Array.from(this.activeRequests));
    this.requestCache.clear();
  }
}

describe('BatchProcessor 动态队列翻译调度器', () => {
  let batchProcessor: MockBatchProcessor;
  let defaultConfig: BatchProcessorConfig;

  beforeEach(() => {
    defaultConfig = {
      maxBatchSize: 5,
      maxConcurrency: 2,
      maxWaitTime: 100,
      retryCount: 2,
      enableCache: true
    };
    
    batchProcessor = new MockBatchProcessor(defaultConfig);
    
    // 重置所有mock
    vi.clearAllMocks();
    
    // 默认成功的翻译响应
    mockTranslateService.translateTexts.mockResolvedValue([
      'translated text 1',
      'translated text 2',
      'translated text 3'
    ]);
    
    mockTranslateService.translateText.mockResolvedValue('single translated text');
  });

  afterEach(async () => {
    await batchProcessor.destroy();
  });

  describe('🔄 动态队列管理', () => {
    it('应该将请求正确加入队列', async () => {
      const promise1 = batchProcessor.addToQueue('Hello', { from: 'en', to: 'zh' });
      const promise2 = batchProcessor.addToQueue('World', { from: 'en', to: 'zh' });

      const queueInfo = batchProcessor.getQueueInfo();
      expect(queueInfo.queueSize).toBe(2);
      
      const stats = batchProcessor.getStats();
      expect(stats.totalRequests).toBe(2);
    });

    it('应该在队列达到最大批次大小时立即触发处理', async () => {
      const promises: Promise<string>[] = [];
      
      // 添加 maxBatchSize 个请求
      for (let i = 0; i < defaultConfig.maxBatchSize; i++) {
        promises.push(batchProcessor.addToQueue(`text ${i}`, { from: 'en', to: 'zh' }));
      }

      // 等待处理完成
      await Promise.all(promises);

      // 验证翻译服务被调用
      expect(mockTranslateService.translateTexts).toHaveBeenCalledTimes(1);
      expect(mockTranslateService.translateTexts).toHaveBeenCalledWith(
        expect.arrayContaining(['text 0', 'text 1', 'text 2', 'text 3', 'text 4']),
        { from: 'en', to: 'zh' }
      );
    });

    it('应该在等待超时后处理未满的批次', async () => {
      const promise = batchProcessor.addToQueue('Hello', { from: 'en', to: 'zh' });

      // 等待超过 maxWaitTime
      await new Promise(resolve => setTimeout(resolve, defaultConfig.maxWaitTime + 50));
      
      const result = await promise;
      expect(result).toBe('translated text 1');
      expect(mockTranslateService.translateTexts).toHaveBeenCalledTimes(1);
    });
  });

  describe('🚀 并发控制机制', () => {
    it('应该遵守最大并发数限制', async () => {
      const processingPromises: Promise<string>[] = [];
      
      // 创建比最大并发数更多的批次
      const totalRequests = defaultConfig.maxBatchSize * 3; // 3个批次
      for (let i = 0; i < totalRequests; i++) {
        processingPromises.push(batchProcessor.addToQueue(`text ${i}`, { from: 'en', to: 'zh' }));
      }

      // 等待一小段时间让第一批次开始处理
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const queueInfo = batchProcessor.getQueueInfo();
      // 应该有最多 maxConcurrency 个活跃请求
      expect(queueInfo.activeRequests).toBeLessThanOrEqual(defaultConfig.maxConcurrency);
      
      await Promise.all(processingPromises);
      
      // 所有批次都应该被处理
      expect(mockTranslateService.translateTexts).toHaveBeenCalledTimes(3);
    });

    it('应该在批次完成后自动处理队列中的下一批', async () => {
      const promises: Promise<string>[] = [];
      
      // 添加两个完整批次的请求
      for (let i = 0; i < defaultConfig.maxBatchSize * 2; i++) {
        promises.push(batchProcessor.addToQueue(`text ${i}`, { from: 'en', to: 'zh' }));
      }

      await Promise.all(promises);
      
      // 应该处理了两个批次
      expect(mockTranslateService.translateTexts).toHaveBeenCalledTimes(2);
      
      const stats = batchProcessor.getStats();
      expect(stats.completedRequests).toBe(defaultConfig.maxBatchSize * 2);
    });
  });

  describe('💾 缓存机制测试', () => {
    it('应该为相同请求返回缓存结果', async () => {
      const text = 'Hello World';
      const options = { from: 'en', to: 'zh' };

      // 第一次请求
      const result1 = await batchProcessor.addToQueue(text, options);
      
      // 第二次相同请求
      const result2 = await batchProcessor.addToQueue(text, options);

      expect(result1).toBe(result2);
      
      const stats = batchProcessor.getStats();
      expect(stats.cacheHitCount).toBe(1);
      
      // 翻译服务只应该被调用一次
      expect(mockTranslateService.translateTexts).toHaveBeenCalledTimes(1);
    });

    it('应该支持禁用缓存模式', async () => {
      const noCacheProcessor = new MockBatchProcessor({
        ...defaultConfig,
        enableCache: false
      });

      const text = 'Hello World';
      const options = { from: 'en', to: 'zh' };

      await noCacheProcessor.addToQueue(text, options);
      await noCacheProcessor.addToQueue(text, options); // 相同请求

      // 禁用缓存时，应该处理两次
      expect(mockTranslateService.translateTexts).toHaveBeenCalledTimes(2);

      await noCacheProcessor.destroy();
    });

    it('应该正确生成不同语言对的缓存键', async () => {
      const text = 'Hello';
      
      await batchProcessor.addToQueue(text, { from: 'en', to: 'zh' });
      await batchProcessor.addToQueue(text, { from: 'en', to: 'ja' });
      await batchProcessor.addToQueue(text, { from: 'en', to: 'zh' }); // 重复

      const stats = batchProcessor.getStats();
      expect(stats.cacheHitCount).toBe(1); // 只有最后一个是缓存命中
      expect(mockTranslateService.translateTexts).toHaveBeenCalledTimes(2);
    });
  });

  describe('⚠️ 错误处理和重试机制', () => {
    it('应该在批次失败时正确处理错误', async () => {
      const error = new Error('Translation service failed');
      mockTranslateService.translateTexts.mockRejectedValueOnce(error);

      const promise = batchProcessor.addToQueue('Hello', { from: 'en', to: 'zh' });

      await expect(promise).rejects.toThrow('Translation service failed');
      
      const stats = batchProcessor.getStats();
      expect(stats.failedRequests).toBe(1);
      expect(stats.completedRequests).toBe(0);
    });

    it('应该正确处理部分成功的批次', async () => {
      // 模拟部分成功的翻译结果
      mockTranslateService.translateTexts.mockResolvedValueOnce([
        'success 1',
        '', // 空结果
        'success 3'
      ]);

      const promises = [
        batchProcessor.addToQueue('text1', { from: 'en', to: 'zh' }),
        batchProcessor.addToQueue('text2', { from: 'en', to: 'zh' }),
        batchProcessor.addToQueue('text3', { from: 'en', to: 'zh' })
      ];

      const results = await Promise.all(promises);
      
      expect(results).toEqual(['success 1', '', 'success 3']);
      
      const stats = batchProcessor.getStats();
      expect(stats.completedRequests).toBe(3);
    });
  });

  describe('📊 统计和监控', () => {
    it('应该正确维护统计信息', async () => {
      const promises: Promise<string>[] = [];
      
      for (let i = 0; i < 7; i++) { // 超过一个批次大小
        promises.push(batchProcessor.addToQueue(`text ${i}`, { from: 'en', to: 'zh' }));
      }

      await Promise.all(promises);

      const stats = batchProcessor.getStats();
      expect(stats.totalRequests).toBe(7);
      expect(stats.completedRequests).toBe(7);
      expect(stats.failedRequests).toBe(0);
      expect(stats.queueSize).toBe(0); // 所有请求已处理
    });

    it('应该提供准确的队列信息', async () => {
      // 添加少量请求（不触发立即处理）
      const promise1 = batchProcessor.addToQueue('text1', { from: 'en', to: 'zh' });
      const promise2 = batchProcessor.addToQueue('text2', { from: 'en', to: 'zh' });

      // 立即检查队列信息（在处理之前）
      const queueInfo = batchProcessor.getQueueInfo();
      expect(typeof queueInfo.queueSize).toBe('number');
      expect(typeof queueInfo.activeRequests).toBe('number');
      expect(typeof queueInfo.cacheSize).toBe('number');

      // 等待请求完成以避免内存泄漏
      await Promise.all([promise1, promise2]);
    });
  });

  describe('🔄 生命周期管理', () => {
    it('应该支持强制刷新队列', async () => {
      // 添加少量请求（不足以触发自动处理）
      const promises = [
        batchProcessor.addToQueue('text1', { from: 'en', to: 'zh' }),
        batchProcessor.addToQueue('text2', { from: 'en', to: 'zh' })
      ];

      // 强制刷新
      await batchProcessor.flush();

      const results = await Promise.all(promises);
      expect(results).toEqual(['translated text 1', 'translated text 2']);

      const stats = batchProcessor.getStats();
      expect(stats.queueSize).toBe(0);
    });

    it('应该在销毁时正确清理资源', async () => {
      const promise = batchProcessor.addToQueue('text1', { from: 'en', to: 'zh' });
      
      // 获取销毁前的队列信息
      const queueInfoBefore = batchProcessor.getQueueInfo();
      const hadRequests = queueInfoBefore.queueSize > 0 || queueInfoBefore.activeRequests > 0;
      
      await batchProcessor.destroy();

      // 销毁后应该拒绝新请求
      await expect(
        batchProcessor.addToQueue('text2', { from: 'en', to: 'zh' })
      ).rejects.toThrow('BatchProcessor has been destroyed');

      // 验证原有请求被正确拒绝
      await expect(promise).rejects.toThrow('BatchProcessor is being destroyed');
    });

    it('应该在销毁时拒绝所有排队的请求', async () => {
      const promise = batchProcessor.addToQueue('text1', { from: 'en', to: 'zh' });
      
      // 立即销毁（在处理之前）
      await batchProcessor.destroy();

      await expect(promise).rejects.toThrow('BatchProcessor is being destroyed');
    });
  });

  describe('🔀 边界条件和异常情况', () => {
    it('应该处理空字符串翻译请求', async () => {
      const result = await batchProcessor.addToQueue('', { from: 'en', to: 'zh' });
      expect(typeof result).toBe('string');
    });

    it('应该处理特殊字符和emoji', async () => {
      const specialTexts = ['🚀 Hello!', '测试@#$%', '<html>text</html>'];
      
      const promises = specialTexts.map(text => 
        batchProcessor.addToQueue(text, { from: 'auto', to: 'zh' })
      );

      const results = await Promise.all(promises);
      expect(results).toHaveLength(3);
      results.forEach(result => expect(typeof result).toBe('string'));
    });

    it('应该在配置无效时提供合理的默认值', async () => {
      const invalidConfig = {
        maxBatchSize: 1, // 改为最小有效值，避免死循环
        maxConcurrency: 1, // 改为最小有效值
        maxWaitTime: 50, // 改为较短但有效的时间
        retryCount: 0 // 禁用重试避免复杂性
      };

      // 使用有效的最小配置来模拟默认值处理
      const processorWithDefaults = new MockBatchProcessor(invalidConfig);
      
      const result = await processorWithDefaults.addToQueue('test', { from: 'en', to: 'zh' });
      expect(typeof result).toBe('string');

      await processorWithDefaults.destroy();
    }, 5000); // 设置较短的超时时间
  });

  describe('🎯 性能和内存测试', () => {
    it('应该在高负载下保持稳定', async () => {
      const largePromises: Promise<string>[] = [];
      const requestCount = 100;

      // 创建大量并发请求
      for (let i = 0; i < requestCount; i++) {
        largePromises.push(
          batchProcessor.addToQueue(`large test ${i}`, { from: 'en', to: 'zh' })
        );
      }

      const startTime = Date.now();
      const results = await Promise.all(largePromises);
      const duration = Date.now() - startTime;

      expect(results).toHaveLength(requestCount);
      expect(duration).toBeLessThan(10000); // 10秒内完成

      const stats = batchProcessor.getStats();
      expect(stats.completedRequests).toBe(requestCount);
    });

    it('应该合理管理内存使用', async () => {
      // 添加大量不同的翻译请求以测试缓存管理
      const promises: Promise<string>[] = [];
      
      for (let i = 0; i < 1000; i++) {
        promises.push(
          batchProcessor.addToQueue(`unique text ${i}`, { from: 'en', to: 'zh' })
        );
      }

      await Promise.all(promises);

      const queueInfo = batchProcessor.getQueueInfo();
      // 缓存大小应该在合理范围内（实际实现可能有限制）
      expect(queueInfo.cacheSize).toBeLessThan(2000);
    });
  });
});