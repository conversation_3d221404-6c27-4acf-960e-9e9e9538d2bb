/**
 * 字幕管理器 - 字幕翻译功能的核心协调器
 * 
 * 职责：
 * - 统一管理网络拦截、解析、翻译的完整流程
 * - 与现有 content script 架构集成
 * - 提供生命周期管理和错误处理
 * - 遵循 Lucid Extension 的 Manager Pattern
 */

import { SubtitleNetworkInterceptor } from './network';
import { SubtitleParserFactory } from './parsers';
import { SubtitleTranslationManager } from './translation';
import {
  StandardSubtitle,
  SubtitleData,
  SubtitleTranslationConfig,
  SubtitleManagerStatus,
  ISubtitleManager,
  SubtitleError,
  SubtitleErrorType,
  SupportedPlatform,
  SubtitleFormat,
  TranslationProgress
} from './types';

export class SubtitleManager implements ISubtitleManager {
  private networkInterceptor: SubtitleNetworkInterceptor;
  private translationManager: SubtitleTranslationManager;
  private config: SubtitleTranslationConfig;
  private status: SubtitleManagerStatus = 'idle';
  private isInitialized = false;
  private currentSubtitles: StandardSubtitle[] = [];
  private eventListeners = new Map<string, Set<(...args: any[]) => void>>();

  constructor(config?: SubtitleTranslationConfig) {
    this.config = config || this.getDefaultConfig();
    this.networkInterceptor = new SubtitleNetworkInterceptor();
    this.translationManager = new SubtitleTranslationManager(this.config);
  }

  /**
   * 初始化字幕管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('⚠️ [SubtitleManager] 管理器已初始化');
      return;
    }

    this.status = 'initializing';

    try {
      // 初始化网络拦截器
      await this.networkInterceptor.initialize();
      
      // 初始化翻译管理器
      await this.translationManager.initialize();
      
      // 设置字幕捕获回调
      this.networkInterceptor.setCaptureCallback(this.handleSubtitleCapture.bind(this));

      this.isInitialized = true;
      this.status = 'ready';

      console.log('✅ [SubtitleManager] 字幕管理器初始化完成');
      this.emit('initialized');

    } catch (error) {
      this.status = 'error';
      const subtitleError = new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        '字幕管理器初始化失败',
        error instanceof Error ? error : new Error(String(error))
      );
      
      console.error('❌ [SubtitleManager] 初始化失败:', subtitleError);
      this.emit('error', subtitleError);
      throw subtitleError;
    }
  }

  /**
   * 启动字幕翻译服务
   */
  async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        '管理器未初始化，请先调用 initialize()'
      );
    }

    if (!this.config.enabled) {
      console.log('📴 [SubtitleManager] 字幕翻译功能已禁用');
      return;
    }

    if (this.status === 'running') {
      console.warn('⚠️ [SubtitleManager] 服务已在运行中');
      return;
    }

    try {
      // 启动网络拦截
      await this.networkInterceptor.startInterception();
      
      this.status = 'running';
      console.log('🚀 [SubtitleManager] 字幕翻译服务已启动');
      this.emit('started');

    } catch (error) {
      this.status = 'error';
      const subtitleError = new SubtitleError(
        SubtitleErrorType.NETWORK_INTERCEPTION_FAILED,
        '启动字幕翻译服务失败',
        error instanceof Error ? error : new Error(String(error))
      );
      
      console.error('❌ [SubtitleManager] 启动失败:', subtitleError);
      this.emit('error', subtitleError);
      throw subtitleError;
    }
  }

  /**
   * 停止字幕翻译服务
   */
  stop(): void {
    if (this.status !== 'running') {
      console.warn('⚠️ [SubtitleManager] 服务未在运行');
      return;
    }

    try {
      // 停止网络拦截
      this.networkInterceptor.stopInterception();
      
      // 清理当前字幕
      this.currentSubtitles = [];
      
      this.status = 'ready';
      console.log('🛑 [SubtitleManager] 字幕翻译服务已停止');
      this.emit('stopped');

    } catch (error) {
      console.error('❌ [SubtitleManager] 停止服务时出错:', error);
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: SubtitleTranslationConfig): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...config };
    
    // 同步更新翻译管理器配置
    this.translationManager.updateConfig(this.config);
    
    console.log('🔧 [SubtitleManager] 配置已更新');
    this.emit('config-updated', { oldConfig, newConfig: this.config });

    // 如果服务正在运行，需要重新启动以应用新配置
    if (this.status === 'running') {
      this.stop();
      this.start().catch(error => {
        console.error('❌ [SubtitleManager] 应用新配置时重启失败:', error);
      });
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): SubtitleManagerStatus {
    return this.status;
  }

  /**
   * 获取当前字幕
   */
  getCurrentSubtitles(): StandardSubtitle[] {
    return [...this.currentSubtitles];
  }

  /**
   * 获取配置
   */
  getConfig(): SubtitleTranslationConfig {
    return { ...this.config };
  }

  /**
   * 获取翻译进度（第二阶段新增功能）
   */
  getTranslationProgress(taskId?: string): TranslationProgress | TranslationProgress[] {
    return this.translationManager.getTranslationProgress(taskId);
  }

  /**
   * 取消翻译任务（第二阶段新增功能）
   */
  cancelTranslation(taskId: string): void {
    this.translationManager.cancelTranslation(taskId);
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    try {
      this.stop();
      this.networkInterceptor.destroy();
      this.translationManager.destroy();
      this.eventListeners.clear();
      this.currentSubtitles = [];
      this.isInitialized = false;
      this.status = 'idle';
      
      console.log('🗑️ [SubtitleManager] 字幕管理器已销毁');
      this.emit('destroyed');

    } catch (error) {
      console.error('❌ [SubtitleManager] 销毁时出错:', error);
    }
  }

  /**
   * 处理字幕数据捕获 - 核心处理流程
   */
  private async handleSubtitleCapture(data: SubtitleData): Promise<void> {
    try {
      console.log('🎯 [SubtitleManager] 捕获到字幕数据:', {
        platform: data.platform,
        format: data.format,
        dataLength: data.rawData.length,
        url: data.url
      });

      this.emit('subtitle-captured', data);

      // 1. 解析字幕
      const subtitles = await this.parseSubtitles(data);
      if (subtitles.length === 0) {
        console.warn('⚠️ [SubtitleManager] 解析后字幕为空');
        return;
      }

      console.log(`📝 [SubtitleManager] 解析完成: ${subtitles.length} 条字幕`);
      this.emit('subtitle-parsed', subtitles);

      // 2. 翻译字幕（第二阶段功能）
      let translatedSubtitles = subtitles;
      if (this.config.enabled && (this.config.showTranslated || this.config.showOriginal)) {
        try {
          console.log(`🔄 [SubtitleManager] 开始翻译字幕 (${this.config.sourceLang} → ${this.config.targetLang})`);
          
          translatedSubtitles = await this.translationManager.translateSubtitles(subtitles, {
            sourceLang: this.config.sourceLang,
            targetLang: this.config.targetLang
          });
          
          console.log(`✅ [SubtitleManager] 翻译完成: ${translatedSubtitles.filter(s => s.translatedText).length}/${subtitles.length} 条成功`);
          this.emit('subtitle-translated', translatedSubtitles);
          
        } catch (translationError) {
          // 构建错误上下文信息
          const errorContext = {
            subtitleCount: subtitles.length,
            sourceLang: this.config.sourceLang,
            targetLang: this.config.targetLang,
            timestamp: Date.now(),
            configSnapshot: {
              batchSize: this.config.batchSize,
              maxConcurrency: this.config.maxConcurrency,
              retryCount: this.config.retryCount
            }
          };

          // 详细错误分类和处理
          if (translationError instanceof Error) {
            const errorMessage = translationError.message.toLowerCase();
            
            if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
              // 速率限制错误
              console.warn('⚠️ [SubtitleManager] 翻译速率限制，将使用原始字幕');
              this.emit('translation-rate-limited', { 
                error: translationError, 
                context: errorContext,
                suggestion: '建议降低批处理大小或增加延迟时间'
              });
            } else if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout')) {
              // 网络相关错误
              console.warn('⚠️ [SubtitleManager] 翻译网络错误，将使用原始字幕');
              this.emit('translation-network-error', { 
                error: translationError, 
                context: errorContext,
                suggestion: '请检查网络连接或稍后重试'
              });
            } else if (errorMessage.includes('quota') || errorMessage.includes('limit exceeded')) {
              // 配额超限错误
              console.warn('⚠️ [SubtitleManager] 翻译配额超限，将使用原始字幕');
              this.emit('translation-quota-exceeded', { 
                error: translationError, 
                context: errorContext,
                suggestion: '请检查翻译服务配额或稍后重试'
              });
            } else if (errorMessage.includes('auth') || errorMessage.includes('unauthorized') || errorMessage.includes('403')) {
              // 认证错误
              console.warn('⚠️ [SubtitleManager] 翻译认证错误，将使用原始字幕');
              this.emit('translation-auth-error', { 
                error: translationError, 
                context: errorContext,
                suggestion: '请检查翻译服务API密钥配置'
              });
            } else {
              // 其他未分类翻译错误
              console.warn('⚠️ [SubtitleManager] 翻译失败，将使用原始字幕:', translationError);
              this.emit('translation-error', { 
                error: translationError, 
                context: errorContext,
                suggestion: '请检查字幕内容或翻译服务配置'
              });
            }
          } else {
            // 非Error类型的异常
            console.warn('⚠️ [SubtitleManager] 翻译发生未知异常，将使用原始字幕:', translationError);
            this.emit('translation-unknown-error', { 
              error: translationError, 
              context: errorContext 
            });
          }

          // 所有翻译错误都继续使用原始字幕，不中断流程
        }
      }

      // 3. 更新当前字幕
      this.currentSubtitles = translatedSubtitles;

      console.log('✅ [SubtitleManager] 字幕处理完成（包含翻译）');
      this.emit('subtitle-ready', translatedSubtitles);

    } catch (error) {
      const subtitleError = new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        '字幕处理失败',
        error instanceof Error ? error : new Error(String(error)),
        { platform: data.platform, format: data.format }
      );
      
      console.error('🚨 [SubtitleManager] 字幕处理失败:', subtitleError);
      this.emit('error', subtitleError);
    }
  }

  /**
   * 解析字幕数据
   */
  private async parseSubtitles(data: SubtitleData): Promise<StandardSubtitle[]> {
    try {
      // 使用工厂类解析字幕
      const result = await SubtitleParserFactory.autoParse(data.rawData);
      
      // 验证解析结果
      const validation = SubtitleParserFactory.validate(result.subtitles);
      if (!validation.valid) {
        console.warn('⚠️ [SubtitleManager] 字幕验证警告:', validation.errors);
        // 继续处理，只是记录警告
      }

      return result.subtitles;

    } catch (error) {
      throw new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        '字幕解析失败',
        error instanceof Error ? error : new Error(String(error)),
        { platform: data.platform, format: data.format }
      );
    }
  }

  /**
   * 手动处理字幕数据（用于测试和调试）
   */
  async processSubtitleData(rawData: string, format?: SubtitleFormat): Promise<StandardSubtitle[]> {
    try {
      const mockData: SubtitleData = {
        platform: SupportedPlatform.GENERIC,
        format: format || SubtitleFormat.UNKNOWN,
        rawData,
        url: 'manual-input',
        timestamp: Date.now()
      };

      await this.handleSubtitleCapture(mockData);
      return this.currentSubtitles;

    } catch (error) {
      throw new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        '手动处理字幕失败',
        error instanceof Error ? error : new Error(String(error))
      );
    }
  }

  /**
   * 事件监听器管理
   */
  on(event: string, listener: (...args: any[]) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  off(event: string, listener: (...args: any[]) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`❌ [SubtitleManager] 事件监听器错误 (${event}):`, error);
        }
      });
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): SubtitleTranslationConfig {
    return {
      enabled: true,
      sourceLang: 'auto',
      targetLang: 'zh-CN',
      showOriginal: false,
      showTranslated: true,
      batchSize: 20,
      maxConcurrency: 3,
      cacheEnabled: true,
      retryCount: 2
    };
  }

  /**
   * 获取管理器统计信息
   */
  getStats(): {
    status: SubtitleManagerStatus;
    isInitialized: boolean;
    currentSubtitleCount: number;
    interceptorStatus: string;
    eventListenerCount: number;
    translationProgress: TranslationProgress[];
  } {
    return {
      status: this.status,
      isInitialized: this.isInitialized,
      currentSubtitleCount: this.currentSubtitles.length,
      interceptorStatus: this.networkInterceptor.getInterceptionStatus(),
      eventListenerCount: Array.from(this.eventListeners.values())
        .reduce((total, listeners) => total + listeners.size, 0),
      translationProgress: this.translationManager.getTranslationProgress() as TranslationProgress[]
    };
  }

  /**
   * 检查平台支持
   */
  isPlatformSupported(platform: SupportedPlatform): boolean {
    // 第一阶段支持所有定义的平台
    return Object.values(SupportedPlatform).includes(platform);
  }

  /**
   * 检查格式支持
   */
  isFormatSupported(format: SubtitleFormat): boolean {
    return SubtitleParserFactory.getSupportedFormats().includes(format);
  }
}