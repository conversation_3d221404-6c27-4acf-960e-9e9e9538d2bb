/**
 * 字幕错误处理器单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  SubtitleErrorHandler,
  handleSubtitleErrors,
  subtitleErrorHandler,
  criticalErrorHandler
} from '../error-handler';
import { SubtitleError, SubtitleErrorType, SupportedPlatform } from '../../types';

// 扩展全局类型以支持 browser API
interface Window {
  browser?: any;
}

// Mock browser runtime API
const mockBrowserRuntime = {
  sendMessage: vi.fn()
};

describe('SubtitleErrorHandler', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    SubtitleErrorHandler.clearStats();
    
    // Mock browser API
    (globalThis as any).browser = {
      runtime: mockBrowserRuntime
    };
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
    delete (global as any).browser;
  });

  describe('error normalization', () => {
    it('应该保持 SubtitleError 实例不变', () => {
      const originalError = new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        'Test error'
      );

      const result = SubtitleErrorHandler.handleError(originalError);

      expect(result).toBe(originalError);
      expect(result instanceof SubtitleError).toBe(true);
    });

    it('应该将普通错误转换为 SubtitleError', () => {
      const originalError = new Error('Generic error');

      const result = SubtitleErrorHandler.handleError(originalError);

      expect(result instanceof SubtitleError).toBe(true);
      expect(result.type).toBe(SubtitleErrorType.UNKNOWN_ERROR);
      expect(result.message).toBe('Generic error');
      expect(result.originalError).toBe(originalError);
    });

    it('应该添加上下文信息', () => {
      const error = new Error('Test');
      const context = { platform: SupportedPlatform.YOUTUBE, userId: '123' };

      const result = SubtitleErrorHandler.handleError(error, context);

      expect(result.context).toEqual(context);
    });
  });

  describe('error statistics', () => {
    it('应该更新错误统计信息', () => {
      const error = new SubtitleError(SubtitleErrorType.PARSING_ERROR, 'Test');
      
      SubtitleErrorHandler.handleError(error);

      const stats = SubtitleErrorHandler.getStats();
      expect(stats.totalErrors).toBe(1);
      expect(stats.errorsByType[SubtitleErrorType.PARSING_ERROR as keyof typeof stats.errorsByType]).toBe(1);
      expect(stats.recentErrors).toHaveLength(1);
      expect(stats.lastErrorTime).toBeGreaterThan(0);
    });

    it('应该按平台统计错误', () => {
      const error = new SubtitleError(
        SubtitleErrorType.NETWORK_TIMEOUT,
        'Timeout',
        undefined,
        { platform: SupportedPlatform.NETFLIX }
      );

      SubtitleErrorHandler.handleError(error);

      const stats = SubtitleErrorHandler.getStats();
      expect(stats.errorsByPlatform[SupportedPlatform.NETFLIX as keyof typeof stats.errorsByPlatform]).toBe(1);
    });

    it('应该限制最近错误列表大小', () => {
      // 生成超过限制的错误
      for (let i = 0; i < 60; i++) {
        const error = new SubtitleError(SubtitleErrorType.PARSING_ERROR, `Error ${i}`);
        SubtitleErrorHandler.handleError(error, {}, { logToConsole: false });
      }

      const stats = SubtitleErrorHandler.getStats();
      expect(stats.recentErrors.length).toBe(50); // MAX_RECENT_ERRORS
      expect(stats.totalErrors).toBe(60);
    });

    it('应该清空统计信息', () => {
      const error = new SubtitleError(SubtitleErrorType.PARSING_ERROR, 'Test');
      SubtitleErrorHandler.handleError(error);

      SubtitleErrorHandler.clearStats();

      const stats = SubtitleErrorHandler.getStats();
      expect(stats.totalErrors).toBe(0);
      expect(stats.errorsByType).toEqual({});
      expect(stats.recentErrors).toHaveLength(0);
    });
  });

  describe('error logging', () => {
    it('应该根据错误类型使用不同的日志级别', () => {
      const criticalError = new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        'Critical error'
      );
      const warningError = new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        'Warning error'
      );

      SubtitleErrorHandler.handleError(criticalError);
      SubtitleErrorHandler.handleError(warningError);

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('[CONFIG_ERROR]'),
        expect.any(Object)
      );
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('[PARSING_ERROR]'),
        expect.any(Object)
      );
    });

    it('应该可以禁用控制台日志', () => {
      const error = new SubtitleError(SubtitleErrorType.PARSING_ERROR, 'Test');

      SubtitleErrorHandler.handleError(error, {}, { logToConsole: false });

      expect(console.error).not.toHaveBeenCalled();
      expect(console.warn).not.toHaveBeenCalled();
    });

    it('应该记录完整的错误信息', () => {
      const error = new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        'Test error',
        new Error('Original error'),
        { platform: SupportedPlatform.YOUTUBE }
      );

      SubtitleErrorHandler.handleError(error, { additionalInfo: 'test' });

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('🚨 [SubtitleErrorHandler] [PARSING_ERROR]'),
        expect.objectContaining({
          message: 'Test error',
          type: SubtitleErrorType.PARSING_ERROR,
          context: expect.objectContaining({
            platform: SupportedPlatform.YOUTUBE,
            additionalInfo: 'test'
          }),
          stack: expect.any(String),
          timestamp: expect.any(String)
        })
      );
    });
  });

  describe('error reporting', () => {
    it('应该上报错误到扩展系统', () => {
      mockBrowserRuntime.sendMessage.mockResolvedValue(undefined);
      
      const error = new SubtitleError(
        SubtitleErrorType.NETWORK_INTERCEPTION_FAILED,
        'Network error'
      );

      SubtitleErrorHandler.handleError(error, {}, { reportToExtension: true });

      expect(mockBrowserRuntime.sendMessage).toHaveBeenCalledWith({
        type: 'subtitle-error-report',
        error: expect.objectContaining({
          type: SubtitleErrorType.NETWORK_INTERCEPTION_FAILED,
          message: 'Network error',
          timestamp: expect.any(Number)
        })
      });
    });

    it('应该处理上报失败', () => {
      mockBrowserRuntime.sendMessage.mockRejectedValue(new Error('Report failed'));
      
      const error = new SubtitleError(SubtitleErrorType.PARSING_ERROR, 'Test');
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      SubtitleErrorHandler.handleError(error, {}, { reportToExtension: true });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚠️ [SubtitleErrorHandler] 错误上报失败:'),
        expect.any(Error)
      );
    });
  });

  describe('recovery strategies', () => {
    it('应该触发重试策略', () => {
      const error = new SubtitleError(SubtitleErrorType.NETWORK_TIMEOUT, 'Timeout');
      const mockListener = vi.fn();
      
      SubtitleErrorHandler.on('retry-requested', mockListener);
      
      SubtitleErrorHandler.handleError(error, {}, { 
        recoveryStrategy: 'retry', 
        retryCount: 3 
      });

      expect(mockListener).toHaveBeenCalledWith({
        error,
        retryCount: 3,
        context: {}
      });
    });

    it('应该触发回退策略', () => {
      const error = new SubtitleError(SubtitleErrorType.PARSING_ERROR, 'Parse failed');
      const mockListener = vi.fn();
      
      SubtitleErrorHandler.on('fallback-parser-requested', mockListener);
      
      SubtitleErrorHandler.handleError(error, {}, { recoveryStrategy: 'fallback' });

      expect(mockListener).toHaveBeenCalledWith({
        error,
        context: {}
      });
    });

    it('应该触发重置策略', () => {
      const error = new SubtitleError(SubtitleErrorType.CONFIG_ERROR, 'Config error');
      const mockListener = vi.fn();
      
      SubtitleErrorHandler.on('system-reset-requested', mockListener);
      
      SubtitleErrorHandler.handleError(error, {}, { recoveryStrategy: 'reset' });

      expect(mockListener).toHaveBeenCalledWith({
        error,
        context: {},
        resetType: 'hard'
      });
    });
  });

  describe('utility methods', () => {
    it('应该识别关键错误', () => {
      const criticalError = new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        'Critical'
      );
      const normalError = new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        'Normal'
      );

      expect(SubtitleErrorHandler.isCriticalError(criticalError)).toBe(true);
      expect(SubtitleErrorHandler.isCriticalError(normalError)).toBe(false);
    });

    it('应该检测高错误频率', () => {
      // 生成多个错误
      for (let i = 0; i < 12; i++) {
        const error = new SubtitleError(SubtitleErrorType.PARSING_ERROR, `Error ${i}`);
        SubtitleErrorHandler.handleError(error, {}, { logToConsole: false });
      }

      expect(SubtitleErrorHandler.isErrorRateHigh(60000)).toBe(true);
    });

    it('应该推荐恢复策略', () => {
      const criticalError = new SubtitleError(
        SubtitleErrorType.CONFIG_ERROR,
        'Critical'
      );
      const newError = new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        'New error'
      );

      expect(SubtitleErrorHandler.getRecommendedRecoveryStrategy(criticalError)).toBe('reset');
      expect(SubtitleErrorHandler.getRecommendedRecoveryStrategy(newError)).toBe('retry');
    });
  });

  describe('convenience methods', () => {
    it('应该创建错误处理器实例', () => {
      const handler = SubtitleErrorHandler.createErrorHandler({
        logToConsole: false,
        recoveryStrategy: 'retry'
      });

      const error = new Error('Test');
      const result = handler(error);

      expect(result instanceof SubtitleError).toBe(true);
      expect(console.error).not.toHaveBeenCalled(); // logToConsole: false
    });

    it('应该包装异步函数', async () => {
      const mockAsyncFn = vi.fn().mockRejectedValue(new Error('Async error'));
      const wrappedFn = SubtitleErrorHandler.wrapAsync(mockAsyncFn);

      await expect(wrappedFn('arg1', 'arg2')).rejects.toThrow(SubtitleError);
      expect(mockAsyncFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('应该包装同步函数', () => {
      const mockSyncFn = vi.fn().mockImplementation(() => {
        throw new Error('Sync error');
      });
      const wrappedFn = SubtitleErrorHandler.wrapSync(mockSyncFn);

      expect(() => wrappedFn('arg1')).toThrow(SubtitleError);
      expect(mockSyncFn).toHaveBeenCalledWith('arg1');
    });
  });

  describe('predefined handlers', () => {
    it('应该使用预定义的默认处理器', () => {
      const error = new Error('Test error');
      
      const result = subtitleErrorHandler(error);

      expect(result instanceof SubtitleError).toBe(true);
      expect(console.warn).toHaveBeenCalled(); // logToConsole: true
    });

    it('应该使用预定义的关键错误处理器', () => {
      const error = new Error('Critical error');
      const mockListener = vi.fn();
      
      SubtitleErrorHandler.on('system-reset-requested', mockListener);
      
      criticalErrorHandler(error);

      // 关键错误处理器使用 reset 策略
      expect(mockListener).toHaveBeenCalled();
    });
  });

  describe('decorator function', () => {
    it('应该装饰同步方法', () => {
      class TestClass {
        @handleSubtitleErrors({ logToConsole: false })
        testMethod() {
          throw new Error('Decorated error');
        }
      }

      const instance = new TestClass();
      
      expect(() => instance.testMethod()).toThrow(SubtitleError);
      expect(console.error).not.toHaveBeenCalled();
    });

    it('应该装饰异步方法', async () => {
      class TestClass {
        @handleSubtitleErrors({ logToConsole: false })
        async testAsyncMethod() {
          throw new Error('Async decorated error');
        }
      }

      const instance = new TestClass();
      
      await expect(instance.testAsyncMethod()).rejects.toThrow(SubtitleError);
      expect(console.error).not.toHaveBeenCalled();
    });

    it('应该正常执行不抛错误的方法', async () => {
      class TestClass {
        @handleSubtitleErrors()
        normalMethod() {
          return 'success';
        }

        @handleSubtitleErrors()
        async asyncNormalMethod() {
          return 'async success';
        }
      }

      const instance = new TestClass();
      
      expect(instance.normalMethod()).toBe('success');
      await expect(instance.asyncNormalMethod()).resolves.toBe('async success');
    });
  });

  describe('event system', () => {
    it('应该支持事件监听和触发', () => {
      const mockListener1 = vi.fn();
      const mockListener2 = vi.fn();
      
      SubtitleErrorHandler.on('test-event', mockListener1);
      SubtitleErrorHandler.on('test-event', mockListener2);
      
      // 手动触发事件（通过内部方法）
      (SubtitleErrorHandler as any).emit('test-event', { data: 'test' });

      expect(mockListener1).toHaveBeenCalledWith({ data: 'test' });
      expect(mockListener2).toHaveBeenCalledWith({ data: 'test' });
    });

    it('应该支持移除事件监听器', () => {
      const mockListener = vi.fn();
      
      SubtitleErrorHandler.on('test-event', mockListener);
      SubtitleErrorHandler.off('test-event', mockListener);
      
      (SubtitleErrorHandler as any).emit('test-event', { data: 'test' });

      expect(mockListener).not.toHaveBeenCalled();
    });

    it('应该处理监听器中的错误', () => {
      const errorListener = vi.fn().mockImplementation(() => {
        throw new Error('Listener error');
      });
      const normalListener = vi.fn();
      
      SubtitleErrorHandler.on('test-event', errorListener);
      SubtitleErrorHandler.on('test-event', normalListener);
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      (SubtitleErrorHandler as any).emit('test-event', { data: 'test' });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('❌ [SubtitleErrorHandler] 事件监听器错误'),
        expect.any(Error)
      );
      expect(normalListener).toHaveBeenCalled(); // 其他监听器仍然执行
    });
  });
});