/**
 * 直接DOM字幕翻译管理器 - 不使用Shadow DOM
 * 直接创建和管理DOM元素，使用CSS类名进行样式控制
 */

export interface SubtitleTranslationConfig {
  enabled: boolean;
  primaryLanguage: string;
  translationLanguage: string;
  translationEngine: string;
  displayMode: 'bilingual' | 'original' | 'translation';
  autoTranslate: boolean;
}

export class DirectSubtitleManager {
  private config: SubtitleTranslationConfig;
  private subtitleElement: HTMLElement | null = null;
  private settingsElement: HTMLElement | null = null;
  private currentSubtitle: { original: string; translated: string } | null = null;
  private isSettingsVisible = false;
  private isDragging = false;
  private resizeObserver: ResizeObserver | null = null;
  private resizeObserverErrorCount = 0;
  private maxResizeObserverErrors = 3;
  private fallbackResizeHandler: (() => void) | null = null;

  constructor() {
    this.config = {
      enabled: true,
      primaryLanguage: 'English',
      translationLanguage: '中文',
      translationEngine: 'Microsoft',
      displayMode: 'bilingual',
      autoTranslate: true
    };

    this.injectStyles();
    this.setupEventListeners();
    this.initializeResizeObserver();
  }

  /**
   * 注入所需的CSS样式到页面
   */
  private injectStyles() {
    // 检查是否已经注入过样式
    if (document.getElementById('lucid-subtitle-styles')) return;

    const styleElement = document.createElement('style');
    styleElement.id = 'lucid-subtitle-styles';
    styleElement.textContent = `
      /* 字幕窗口容器 - 基于用户实际结构 */
      .lucid-caption-window {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 9999;
        
        /* 动态尺寸由JavaScript设置 */
        width: 100%;
        height: 100%;
        
        opacity: 0;
        transition: all 0.3s ease-out;
      }
      
      .lucid-caption-window.visible {
        opacity: 1;
      }
      
      .lucid-caption-window.dragging {
        transition: none;
        user-select: none;
      }
      
      /* 字幕容器 */
      .lucid-caption-container {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        pointer-events: auto;
      }
      
      /* 主字幕组件 */
      .lucid-caption {
        position: relative;
        cursor: default; /* 字幕主体显示正常光标 */
        min-width: 100px;
        max-width: 80vw;
        word-wrap: break-word;
      }
      
      /* 拖拽控制器 - hover时显示 */
      .lucid-caption-drag {
        position: absolute;
        top: -25px; /* 稍微向上移动一点，增加与字幕的连接 */
        left: 50%;
        transform: translateX(-50%);
        
        height: 20px; /* 增加高度提升触发区域 */
        padding: 2px 9px; /* 增加上下padding */
        background: rgba(0, 0, 0, 0.80);
        border-radius: 20px;
        outline: 1px rgba(255, 255, 255, 0.20) solid;
        outline-offset: -1px;
        backdrop-filter: blur(5px);
        
        display: flex;
        justify-content: center;
        align-items: center;
        
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
        cursor: grab;
      }
      
      .lucid-caption-drag:active {
        cursor: grabbing;
      }
      
      .lucid-caption:hover .lucid-caption-drag,
      .lucid-caption-drag:hover {
        opacity: 1;
        pointer-events: auto;
      }
      
      .lucid-svg-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.8);
      }
      
      .lucid-svg-icon.icon-18 {
        width: 18px;
        height: 18px;
      }
      
      /* 字幕内容容器 */
      .lucid-caption-content {
        padding: 5.24px 3.90px 6.24px 3.90px;
        background: rgba(0, 0, 0, 0.70);
        border-radius: 12px;
        
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 2.10px;
        
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(2px);
      }
      
      /* 文本阴影工具类 */
      .lucid-text-shadow {
        text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
      }
      
      /* 主字幕文本 */
      .lucid-caption-primary {
        width: 100%;
        text-align: center;
        color: white;
        font-size: 15.60px;
        font-weight: 400;
        line-height: 21.86px;
        margin: 0;
        padding: 0;
        white-space: pre-wrap;
      }
      
      .lucid-caption-primary.inter {
        font-family: 'Inter', 'Microsoft YaHei', sans-serif;
      }
      
      .lucid-caption-primary.roboto {
        font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
      }
      
      /* 翻译字幕文本 */
      .lucid-caption-secondary {
        width: 100%;
        text-align: center;
        color: white;
        font-size: 15.60px;
        font-weight: 400;
        line-height: 21.86px;
        margin: 0;
        padding: 0;
        white-space: pre-wrap;
        margin-top: 2.10px;
      }
      
      .lucid-caption-secondary.inter {
        font-family: 'Inter', 'Microsoft YaHei', sans-serif;
      }
      
      .lucid-caption-secondary.roboto {
        font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
      }
      
      /* 设置面板样式 - 基于设计稿毛玻璃效果 */
      .lucid-settings-panel {
        position: fixed;
        top: 70px;
        right: 20px;
        width: 282px;
        z-index: 10001;
        
        padding: 1px;
        background: rgba(28, 28, 28, 0.90);
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
        border-radius: 12px;
        outline: 1px rgba(255, 255, 255, 0.10) solid;
        outline-offset: -1px;
        backdrop-filter: blur(5px);
        
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
        transition: all 0.2s ease-out;
        pointer-events: none;
      }
      
      .lucid-settings-panel.visible {
        opacity: 1;
        transform: scale(1) translateY(0);
        pointer-events: auto;
      }
      
      .lucid-settings-content {
        width: 100%;
        display: flex;
        flex-direction: column;
      }
      
      .lucid-setting-item {
        width: 100%;
        height: 40px;
        padding: 0 8px 0 12px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        transition: background-color 0.2s ease;
      }
      
      .lucid-setting-item.clickable {
        cursor: pointer;
      }
      
      .lucid-setting-item.clickable:hover {
        background: rgba(255, 255, 255, 0.05);
      }
      
      .lucid-setting-icon {
        width: 28px;
        height: 20px;
        padding-right: 8px;
        display: flex;
        align-items: center;
      }
      
      .lucid-setting-icon svg {
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      
      .lucid-setting-label {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex: 1;
        
        color: rgba(255, 255, 255, 0.80);
        font-size: 13px;
        font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
        font-weight: 400;
        line-height: 13px;
        text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
        white-space: nowrap;
      }
      
      .lucid-setting-value {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 4px;
        
        max-width: 110px;
        padding-right: 4px;
        color: rgba(255, 255, 255, 0.90);
        font-size: 12.90px;
        font-family: 'Roboto', 'Microsoft YaHei', sans-serif;
        font-weight: 400;
        line-height: 15.60px;
        text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      
      .lucid-setting-separator {
        width: 100%;
        height: 1px;
        background: rgba(255, 255, 255, 0.08);
      }
      
      /* 开关组件样式 */
      .lucid-toggle {
        width: 56px;
        height: 38px;
        padding: 12px;
        position: relative;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        cursor: pointer;
      }
      
      .lucid-toggle-track {
        width: 32px;
        height: 14px;
        border-radius: 7px;
        position: relative;
        transition: background-color 0.3s ease;
      }
      
      .lucid-toggle-track.enabled {
        background: rgba(255, 200, 98, 0.5);
      }
      
      .lucid-toggle-track.disabled {
        background: rgba(255, 255, 255, 0.3);
      }
      
      .lucid-toggle-thumb {
        width: 18px;
        height: 18px;
        border-radius: 9px;
        background: #FFC862;
        position: absolute;
        top: -2px;
        left: -1px;
        transition: transform 0.3s ease;
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.2);
      }
      
      .lucid-toggle-thumb.enabled {
        transform: translateX(15px);
      }
      
      .lucid-toggle-thumb.disabled {
        transform: translateX(0);
      }
      
      /* 响应式适配 */
      @media (max-width: 768px) {
        .lucid-settings-panel {
          right: 10px;
          left: 10px;
          width: auto;
        }
        
        .lucid-subtitle-overlay {
          max-width: 90%;
          bottom: 20px;
        }
      }
      
      @media (max-width: 480px) {
        .lucid-subtitle-text {
          font-size: 14px;
          line-height: 20px;
        }
        
        .lucid-subtitle-overlay {
          bottom: 15px;
        }
      }
    `;
    
    document.head.appendChild(styleElement);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners() {
    // 键盘快捷键
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault();
        this.toggleSettings();
      }
      
      if (event.key === 'Escape') {
        this.hideSettings();
      }
    });

    // 点击外部关闭设置面板 - 使用延迟来避免立即触发
    document.addEventListener('click', (event) => {
      // 使用延迟确保设置面板已经完全显示
      setTimeout(() => {
        if (this.isSettingsVisible && this.settingsElement) {
          const target = event.target as Node;
          // 检查点击是否在设置面板内部或触发按钮
          if (!this.settingsElement.contains(target)) {
            // 检查是否点击了设置按钮或其父元素
            const isSettingsButton = (target as Element)?.closest?.('[onclick*="Settings"]') ||
                                   (target as Element)?.closest?.('.settings-trigger');
            
            if (!isSettingsButton) {
              this.hideSettings();
            }
          }
        }
      }, 100); // 100ms延迟避免立即触发
    });
  }

  /**
   * 显示字幕
   */
  public showSubtitle(originalText: string, translatedText: string, position?: { x: number; y: number }) {
    if (!this.config.enabled) return;

    this.currentSubtitle = { original: originalText, translated: translatedText };

    // 移除现有字幕
    this.hideSubtitle();

    // 根据显示模式决定显示内容
    let displayOriginal = '';
    let displayTranslated = '';

    switch (this.config.displayMode) {
      case 'bilingual':
        displayOriginal = originalText;
        displayTranslated = translatedText;
        break;
      case 'original':
        displayOriginal = originalText;
        break;
      case 'translation':
        displayTranslated = translatedText;
        break;
    }

    // 创建字幕窗口容器 - 使用新的类名结构
    this.subtitleElement = document.createElement('div');
    this.subtitleElement.className = 'lucid-caption-window';
    this.subtitleElement.id = 'lucid-caption-window';

    // 创建字幕容器
    const captionContainer = document.createElement('div');
    captionContainer.className = 'lucid-caption-container';

    // 创建主字幕组件
    const caption = document.createElement('div');
    caption.className = 'lucid-caption';

    // 创建拖拽控制器
    const dragHandle = document.createElement('div');
    dragHandle.className = 'lucid-caption-drag';
    
    // 添加6点拖拽图标 - 使用用户提供的结构
    const dragIcon = document.createElement('div');
    dragIcon.className = 'lucid-svg-icon icon-18';
    dragIcon.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.38465 9C4.38465 8.55578 4.54282 8.1755 4.85915 7.85915C5.1755 7.54282 5.55579 7.38465 6 7.38465C6.44422 7.38465 6.8245 7.54282 7.14085 7.85915C7.45719 8.1755 7.61535 8.55578 7.61535 9C7.61535 9.44422 7.45719 9.8245 7.14085 10.1408C6.8245 10.4572 6.44422 10.6153 6 10.6153C5.55579 10.6153 5.1755 10.4572 4.85915 10.1408C4.54282 9.8245 4.38465 9.44422 4.38465 9ZM4.38465 15C4.38465 14.5558 4.54282 14.1755 4.85915 13.8591C5.1755 13.5428 5.55579 13.3846 6 13.3846C6.44422 13.3846 6.8245 13.5428 7.14085 13.8591C7.45719 14.1755 7.61535 14.5558 7.61535 15C7.61535 15.4442 7.45719 15.8245 7.14085 16.1408C6.8245 16.4572 6.44422 16.6153 6 16.6153C5.55578 16.6153 5.1755 16.4572 4.85915 16.1408C4.54282 15.8245 4.38465 15.4442 4.38465 15ZM10.3847 9C10.3847 8.55578 10.5428 8.1755 10.8592 7.85915C11.1755 7.54282 11.5558 7.38465 12 7.38465C12.4442 7.38465 12.8245 7.54282 13.1409 7.85915C13.4572 8.1755 13.6154 8.55578 13.6154 9C13.6154 9.44422 13.4572 9.8245 13.1409 10.1408C12.8245 10.4572 12.4442 10.6153 12 10.6153C11.5558 10.6153 11.1755 10.4572 10.8592 10.1408C10.5428 9.8245 10.3847 9.44422 10.3847 9ZM10.3847 15C10.3847 14.5558 10.5428 14.1755 10.8592 13.8592C11.1755 13.5428 11.5558 13.3846 12 13.3846C12.4442 13.3846 12.8245 13.5428 13.1409 13.8592C13.4572 14.1755 13.6154 14.5558 13.6154 15C13.6154 15.4442 13.4572 15.8245 13.1409 16.1408C12.8245 16.4572 12.4442 16.6153 12 16.6153C11.5558 16.6153 11.1755 16.4572 10.8592 16.1408C10.5428 15.8245 10.3847 15.4442 10.3847 15ZM16.3847 9C16.3847 8.55578 16.5428 8.1755 16.8592 7.85915C17.1755 7.54282 17.5558 7.38465 18 7.38465C18.4442 7.38465 18.8245 7.54282 19.1409 7.85915C19.4572 8.1755 19.6154 8.55578 19.6154 9C19.6154 9.44422 19.4572 9.8245 19.1409 10.1408C18.8245 10.4572 18.4442 10.6153 18 10.6153C17.5558 10.6153 17.1755 10.4572 16.8592 10.1408C16.5428 9.8245 16.3847 9.44422 16.3847 9ZM16.3847 15C16.3847 14.5558 16.5428 14.1755 16.8592 13.8592C17.1755 13.5428 17.5558 13.3846 18 13.3846C18.4442 13.3846 18.8245 13.5428 19.1409 13.8592C19.4572 14.1755 19.6154 14.5558 19.6154 15C19.6154 15.4442 19.4572 15.8245 19.1409 16.1408C18.8245 16.4572 18.4442 16.6154 18 16.6154C17.5558 16.6154 17.1755 16.4572 16.8592 16.1408C16.5428 15.8245 16.3847 15.4442 16.3847 15Z"></path>
      </svg>
    `;
    
    dragHandle.appendChild(dragIcon);
    caption.appendChild(dragHandle);

    // 创建字幕内容容器
    const captionContent = document.createElement('div');
    captionContent.className = 'lucid-caption-content lucid-text-shadow';

    // 添加原文
    if (displayOriginal) {
      const originalDiv = document.createElement('div');
      originalDiv.className = 'lucid-caption-primary roboto';
      originalDiv.textContent = displayOriginal;
      captionContent.appendChild(originalDiv);
    }

    // 添加译文
    if (displayTranslated) {
      const translatedDiv = document.createElement('div');
      translatedDiv.className = 'lucid-caption-secondary inter';
      translatedDiv.textContent = displayTranslated;
      captionContent.appendChild(translatedDiv);
    }

    // 组装DOM结构
    caption.appendChild(captionContent);
    captionContainer.appendChild(caption);
    this.subtitleElement.appendChild(captionContainer);

    // 自定义位置（如果提供）
    if (position) {
      this.subtitleElement.style.position = 'fixed';
      this.subtitleElement.style.left = `${position.x}px`;
      this.subtitleElement.style.top = `${position.y}px`;
      this.subtitleElement.style.transform = 'translateX(-50%)';
      this.subtitleElement.style.bottom = 'auto';
    }

    // 设置拖拽功能
    this.setupDragFunctionality(this.subtitleElement, dragHandle);

    // 查找视频容器并设置相对定位
    const videoContainer = this.findVideoContainer();
    if (videoContainer && !position) {
      // 将字幕定位相对于视频容器而不是viewport
      videoContainer.style.position = 'relative';
      
      // 动态计算并设置字幕窗口尺寸以匹配视频播放器
      const containerRect = videoContainer.getBoundingClientRect();
      this.subtitleElement.style.width = `${containerRect.width}px`;
      this.subtitleElement.style.height = `${containerRect.height}px`;
      
      videoContainer.appendChild(this.subtitleElement);
      
      // 开始监听容器尺寸变化
      if (this.resizeObserver) {
        this.resizeObserver.observe(videoContainer);
      }
    } else {
      // 回退到body
      document.body.appendChild(this.subtitleElement);
    }

    // 触发显示动画
    requestAnimationFrame(() => {
      if (this.subtitleElement) {
        this.subtitleElement.classList.add('visible');
      }
    });
  }

  /**
   * 设置拖拽功能
   */
  private setupDragFunctionality(subtitleElement: HTMLElement, dragHandle: HTMLElement) {
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let initialX = 0;
    let initialY = 0;

    // 鼠标按下开始拖拽 - 只在拖拽手柄上监听
    const handleMouseDown = (e: MouseEvent) => {
      isDragging = true;
      this.isDragging = true;
      dragHandle.style.cursor = 'grabbing';
      dragHandle.style.opacity = '1';
      
      startX = e.clientX;
      startY = e.clientY;
      
      // 获取字幕容器的当前位置
      const captionContainer = subtitleElement.querySelector('.lucid-caption-container') as HTMLElement;
      if (captionContainer) {
        const rect = captionContainer.getBoundingClientRect();
        const videoContainer = this.findVideoContainer();
        const videoRect = videoContainer ? videoContainer.getBoundingClientRect() : { left: 0, top: 0 };
        
        // 计算相对于视频容器的位置
        initialX = rect.left - videoRect.left;
        initialY = rect.top - videoRect.top;
      }
      
      // 阻止文本选择和事件冒泡
      e.preventDefault();
      e.stopPropagation(); // 防止事件冒泡到字幕主体
      
      // 添加全局事件监听器
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    // 鼠标移动处理拖拽
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      
      const newX = initialX + deltaX;
      const newY = initialY + deltaY;
      
      // 更新字幕容器位置（相对于视频容器）
      const captionContainer = subtitleElement.querySelector('.lucid-caption-container') as HTMLElement;
      if (captionContainer) {
        captionContainer.style.left = `${newX}px`;
        captionContainer.style.top = `${newY}px`;
        captionContainer.style.transform = 'none';
      }
    };

    // 鼠标松开结束拖拽
    const handleMouseUp = () => {
      isDragging = false;
      this.isDragging = false;
      dragHandle.style.cursor = 'grab';
      
      // 延迟隐藏拖拽手柄
      setTimeout(() => {
        if (!this.isDragging) {
          dragHandle.style.opacity = '0';
        }
      }, 500);
      
      // 移除全局事件监听器
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    // 只在拖拽手柄上添加鼠标事件
    dragHandle.addEventListener('mousedown', handleMouseDown);
    
    // 确保字幕主体不可拖拽 - 查找caption元素
    const caption = subtitleElement.querySelector('.lucid-caption') as HTMLElement;
    if (caption) {
      caption.addEventListener('mousedown', (e: MouseEvent) => {
        // 如果点击的不是拖拽手柄，阻止任何拖拽行为
        if (!dragHandle.contains(e.target as Node)) {
          e.stopPropagation();
        }
      });
    }
    
    // 触摸事件支持（移动端）- 同样只在拖拽手柄上
    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length !== 1) return;
      
      const touch = e.touches[0];
      isDragging = true;
      subtitleElement.classList.add('dragging');
      
      startX = touch.clientX;
      startY = touch.clientY;
      
      const rect = subtitleElement.getBoundingClientRect();
      initialX = rect.left + rect.width / 2;
      initialY = rect.top;
      
      e.preventDefault();
      e.stopPropagation();
      
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleTouchEnd);
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDragging || e.touches.length !== 1) return;
      
      const touch = e.touches[0];
      const deltaX = touch.clientX - startX;
      const deltaY = touch.clientY - startY;
      
      const newX = initialX + deltaX;
      const newY = initialY + deltaY;
      
      subtitleElement.style.position = 'fixed';
      subtitleElement.style.left = `${newX}px`;
      subtitleElement.style.top = `${newY}px`;
      subtitleElement.style.transform = 'translateX(-50%)';
      subtitleElement.style.bottom = 'auto';
      
      e.preventDefault();
    };

    const handleTouchEnd = () => {
      isDragging = false;
      subtitleElement.classList.remove('dragging');
      
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };

    dragHandle.addEventListener('touchstart', handleTouchStart, { passive: false });
  }

  /**
   * 初始化ResizeObserver来监听容器尺寸变化
   * 增强版本：改进防抖机制、添加节流和自适应延迟
   */
  private initializeResizeObserver() {
    if ('ResizeObserver' in window) {
      // 防抖和节流计时器
      let debounceTimer: number | null = null;
      let throttleTimer: number | null = null;
      let lastUpdateTime = 0;
      let updateCount = 0;
      
      // 自适应延迟参数
      const baseDelay = 100; // 基础延迟100ms
      const maxDelay = 300;   // 最大延迟300ms
      const throttleInterval = 50; // 节流间隔50ms
      
      this.resizeObserver = new ResizeObserver((entries) => {
        try {
          const currentTime = Date.now();
          
          // 节流机制：限制处理频率
          if (throttleTimer) {
            return; // 如果节流器激活，直接返回
          }
          
          // 激活节流器
          throttleTimer = window.setTimeout(() => {
            throttleTimer = null;
          }, throttleInterval);
          
          // 清除之前的防抖计时器
          if (debounceTimer) {
            clearTimeout(debounceTimer);
          }
          
          // 计算自适应延迟时间
          updateCount++;
          const timeSinceLastUpdate = currentTime - lastUpdateTime;
          
          let adaptiveDelay = baseDelay;
          
          // 如果更新很频繁，增加延迟时间
          if (timeSinceLastUpdate < 200) {
            adaptiveDelay = Math.min(maxDelay, baseDelay + (updateCount * 20));
          } else {
            // 重置更新计数
            updateCount = Math.max(0, updateCount - 1);
          }
          
          console.log(`ResizeObserver触发 - 使用${adaptiveDelay}ms自适应延迟`, {
            updateCount,
            timeSinceLastUpdate,
            entriesLength: entries.length
          });
          
          // 使用自适应延迟的防抖处理
          debounceTimer = window.setTimeout(() => {
            try {
              // 检查字幕元素是否仍然存在
              if (this.subtitleElement && this.subtitleElement.parentElement) {
                // 验证观察的条目是否有效
                const validEntry = entries.find(entry => {
                  const target = entry.target as HTMLElement;
                  return target && this.isValidVideoContainer(target);
                });
                
                if (validEntry) {
                  this.updateSubtitleWindowSize();
                  lastUpdateTime = Date.now();
                  
                  console.log('ResizeObserver处理完成 - 容器尺寸变化:', {
                    width: validEntry.contentRect.width,
                    height: validEntry.contentRect.height,
                    target: (validEntry.target as HTMLElement).id || (validEntry.target as HTMLElement).className,
                    delay: adaptiveDelay
                  });
                } else {
                  console.warn('ResizeObserver: 未找到有效的视频容器条目');
                }
              } else {
                console.warn('ResizeObserver: 字幕元素不存在，停止观察');
                // 字幕元素已被移除，可以考虑断开观察器
                if (this.resizeObserver) {
                  this.resizeObserver.disconnect();
                }
              }
              
              debounceTimer = null;
            } catch (updateError) {
              console.error('ResizeObserver更新过程中发生错误:', updateError);
              // 不重置debounceTimer，避免无限递归
            }
          }, adaptiveDelay);
          
        } catch (error) {
          console.error('ResizeObserver处理过程中发生错误:', error);
          
          // 错误恢复：尝试重新初始化观察器
          this.handleResizeObserverError();
        }
      });
      
      console.log('ResizeObserver初始化成功 - 使用增强防抖机制');
    } else {
      console.warn('当前浏览器不支持ResizeObserver，启用fallback方案');
      this.setupFallbackResizeHandler();
    }
  }

  /**
   * 更新字幕窗口尺寸以匹配视频容器
   * 改进版本：添加错误检查、边界条件处理和性能优化
   */
  private updateSubtitleWindowSize() {
    try {
      // 验证必要元素是否存在
      if (!this.subtitleElement) {
        console.warn('updateSubtitleWindowSize: 字幕元素不存在，跳过尺寸更新');
        return;
      }

      // 检查字幕元素是否仍在DOM中
      if (!this.subtitleElement.parentElement) {
        console.warn('updateSubtitleWindowSize: 字幕元素不在DOM中，跳过尺寸更新');
        return;
      }

      const videoContainer = this.findVideoContainer();
      if (!videoContainer) {
        console.warn('updateSubtitleWindowSize: 未找到视频容器，使用默认尺寸');
        this.applyDefaultSubtitleSize();
        return;
      }

      const containerRect = videoContainer.getBoundingClientRect();
      
      // 验证容器尺寸的有效性
      if (containerRect.width <= 0 || containerRect.height <= 0) {
        console.warn('updateSubtitleWindowSize: 视频容器尺寸无效', {
          width: containerRect.width,
          height: containerRect.height
        });
        return;
      }

      // 检查尺寸是否合理（防止异常大小）
      const maxSize = Math.max(window.innerWidth, window.innerHeight);
      if (containerRect.width > maxSize * 2 || containerRect.height > maxSize * 2) {
        console.warn('updateSubtitleWindowSize: 容器尺寸异常大，跳过更新', {
          containerSize: { width: containerRect.width, height: containerRect.height },
          windowSize: { width: window.innerWidth, height: window.innerHeight }
        });
        return;
      }

      // 获取当前字幕窗口尺寸，避免不必要的DOM操作
      const currentStyle = this.subtitleElement.style;
      const currentWidth = parseInt(currentStyle.width) || 0;
      const currentHeight = parseInt(currentStyle.height) || 0;

      // 设置尺寸变化阈值（5px），避免微小变化引起频繁更新
      const threshold = 5;
      const widthDiff = Math.abs(containerRect.width - currentWidth);
      const heightDiff = Math.abs(containerRect.height - currentHeight);

      if (widthDiff < threshold && heightDiff < threshold) {
        // 尺寸变化很小，跳过更新
        return;
      }

      // 使用requestAnimationFrame优化DOM操作
      requestAnimationFrame(() => {
        if (this.subtitleElement && this.subtitleElement.parentElement) {
          // 动态设置字幕窗口尺寸
          this.subtitleElement.style.width = `${containerRect.width}px`;
          this.subtitleElement.style.height = `${containerRect.height}px`;
          
          console.log('字幕窗口尺寸已更新:', {
            width: containerRect.width,
            height: containerRect.height,
            container: videoContainer.id || videoContainer.className || 'unknown'
          });
        }
      });
      
    } catch (error) {
      console.error('updateSubtitleWindowSize: 更新字幕窗口尺寸时发生错误:', error);
      // 尝试应用默认尺寸作为fallback
      this.applyDefaultSubtitleSize();
    }
  }

  /**
   * 应用默认字幕尺寸（fallback方案）
   */
  private applyDefaultSubtitleSize() {
    if (!this.subtitleElement) return;
    
    try {
      // 使用视窗尺寸作为默认值
      const defaultWidth = Math.min(window.innerWidth * 0.8, 800);
      const defaultHeight = Math.min(window.innerHeight * 0.6, 600);
      
      this.subtitleElement.style.width = `${defaultWidth}px`;
      this.subtitleElement.style.height = `${defaultHeight}px`;
      
      console.log('应用默认字幕窗口尺寸:', {
        width: defaultWidth,
        height: defaultHeight
      });
    } catch (error) {
      console.error('applyDefaultSubtitleSize: 应用默认尺寸时发生错误:', error);
    }
  }

  /**
   * 处理ResizeObserver错误并实现恢复机制
   */
  private handleResizeObserverError() {
    try {
      this.resizeObserverErrorCount++;
      
      console.warn(`ResizeObserver发生错误，错误次数: ${this.resizeObserverErrorCount}/${this.maxResizeObserverErrors}`);

      // 如果错误次数超过阈值，切换到fallback方案
      if (this.resizeObserverErrorCount >= this.maxResizeObserverErrors) {
        console.error('ResizeObserver错误次数过多，切换到fallback方案');
        
        // 清理现有的ResizeObserver
        if (this.resizeObserver) {
          try {
            this.resizeObserver.disconnect();
          } catch (disconnectError) {
            console.warn('断开ResizeObserver时发生错误:', disconnectError);
          }
          this.resizeObserver = null;
        }
        
        // 启用fallback处理器
        this.setupFallbackResizeHandler();
        return;
      }

      // 尝试重新初始化ResizeObserver（延迟重试）
      setTimeout(() => {
        try {
          console.log('尝试重新初始化ResizeObserver...');
          
          // 清理现有观察器
          if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
          }
          
          // 重新初始化
          this.initializeResizeObserver();
          
          // 如果字幕正在显示，重新开始观察
          if (this.subtitleElement && this.subtitleElement.parentElement) {
            const videoContainer = this.findVideoContainer();
            if (videoContainer && this.resizeObserver) {
              (this.resizeObserver as ResizeObserver).observe(videoContainer);
              console.log('ResizeObserver重新初始化成功');
            }
          }
          
        } catch (retryError) {
          console.error('重新初始化ResizeObserver失败:', retryError);
          this.handleResizeObserverError(); // 递归调用处理重试失败
        }
      }, 1000 * this.resizeObserverErrorCount); // 递增延迟重试时间
      
    } catch (error) {
      console.error('handleResizeObserverError: 处理ResizeObserver错误时发生异常:', error);
      // 强制切换到fallback方案
      this.setupFallbackResizeHandler();
    }
  }

  /**
   * 设置fallback尺寸处理器（用于不支持ResizeObserver的浏览器或错误恢复）
   */
  private setupFallbackResizeHandler() {
    try {
      console.log('设置fallback resize处理器...');
      
      // 清理现有的fallback处理器
      this.cleanupFallbackHandler();

      // 防抖计时器
      let debounceTimer: number | null = null;
      
      // 创建防抖的更新函数
      const debouncedUpdate = () => {
        if (debounceTimer) {
          clearTimeout(debounceTimer);
        }
        
        debounceTimer = window.setTimeout(() => {
          if (this.subtitleElement && this.subtitleElement.parentElement) {
            this.updateSubtitleWindowSize();
          }
          debounceTimer = null;
        }, 200); // 200ms防抖延迟
      };

      // 方法1: Window resize事件监听
      this.fallbackResizeHandler = debouncedUpdate;
      window.addEventListener('resize', this.fallbackResizeHandler);
      console.log('已设置window resize事件监听');

      // 方法2: MutationObserver监听DOM变化（作为补充）
      if ('MutationObserver' in window) {
        const mutationObserver = new MutationObserver((mutations) => {
          let shouldUpdate = false;
          
          for (const mutation of mutations) {
            // 检查是否有影响视频容器的变化
            if (mutation.type === 'attributes' && 
                (mutation.attributeName === 'style' || 
                 mutation.attributeName === 'class')) {
              const target = mutation.target as HTMLElement;
              if (this.isVideoContainerOrParent(target)) {
                shouldUpdate = true;
                break;
              }
            } else if (mutation.type === 'childList') {
              // 检查是否有视频容器相关的DOM结构变化
              for (const addedNode of Array.from(mutation.addedNodes)) {
                if (addedNode.nodeType === Node.ELEMENT_NODE) {
                  const element = addedNode as HTMLElement;
                  if (this.isVideoContainerOrParent(element) ||
                      element.querySelector('video, [class*="video"], [id*="video"]')) {
                    shouldUpdate = true;
                    break;
                  }
                }
              }
            }
          }
          
          if (shouldUpdate) {
            debouncedUpdate();
          }
        });

        // 观察document body的变化
        mutationObserver.observe(document.body, {
          attributes: true,
          attributeFilter: ['style', 'class'],
          childList: true,
          subtree: true
        });

        // 将MutationObserver绑定到fallback处理器，以便清理
        (this.fallbackResizeHandler as any).mutationObserver = mutationObserver;
        console.log('已设置MutationObserver监听');
      }

      // 方法3: 定时轮询作为最后的备选方案（降低频率以节省性能）
      const pollingInterval = setInterval(() => {
        if (this.subtitleElement && this.subtitleElement.parentElement) {
          // 检查视频容器是否存在且尺寸发生变化
          const videoContainer = this.findVideoContainer();
          if (videoContainer) {
            const currentRect = videoContainer.getBoundingClientRect();
            const currentWidth = parseInt(this.subtitleElement.style.width) || 0;
            const currentHeight = parseInt(this.subtitleElement.style.height) || 0;
            
            // 如果尺寸有明显差异，则更新
            if (Math.abs(currentRect.width - currentWidth) > 10 || 
                Math.abs(currentRect.height - currentHeight) > 10) {
              this.updateSubtitleWindowSize();
            }
          }
        } else {
          // 如果字幕不再存在，清理轮询
          clearInterval(pollingInterval);
        }
      }, 2000); // 每2秒检查一次

      // 将轮询定时器绑定到fallback处理器，以便清理
      (this.fallbackResizeHandler as any).pollingInterval = pollingInterval;
      console.log('已设置定时轮询监听');

      console.log('Fallback resize处理器设置完成');
      
    } catch (error) {
      console.error('setupFallbackResizeHandler: 设置fallback处理器时发生错误:', error);
    }
  }

  /**
   * 清理fallback处理器
   */
  private cleanupFallbackHandler() {
    if (this.fallbackResizeHandler) {
      try {
        // 移除window resize事件监听
        window.removeEventListener('resize', this.fallbackResizeHandler);
        
        // 清理MutationObserver
        const mutationObserver = (this.fallbackResizeHandler as any).mutationObserver;
        if (mutationObserver) {
          mutationObserver.disconnect();
        }
        
        // 清理定时轮询
        const pollingInterval = (this.fallbackResizeHandler as any).pollingInterval;
        if (pollingInterval) {
          clearInterval(pollingInterval);
        }
        
        console.log('Fallback resize处理器已清理');
      } catch (error) {
        console.warn('清理fallback处理器时发生错误:', error);
      }
      
      this.fallbackResizeHandler = null;
    }
  }

  /**
   * 检查元素是否为视频容器或其父元素
   */
  private isVideoContainerOrParent(element: HTMLElement): boolean {
    try {
      // 检查元素本身是否为视频容器
      if (this.isValidVideoContainer(element)) {
        return true;
      }
      
      // 检查元素是否包含视频容器
      const videoSelectors = [
        '#movie_player',
        '.html5-video-container',
        '.html5-video-player',
        'video'
      ];
      
      for (const selector of videoSelectors) {
        if (element.querySelector && element.querySelector(selector)) {
          return true;
        }
      }
      
      // 检查元素是否为已知视频容器的父元素
      if (element.id && element.id.includes('player')) return true;
      if (element.className && (element.className.includes('video') || element.className.includes('player'))) return true;
      
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 查找视频容器元素 - 增强版本支持YouTube等主流平台
   * 基于.claude/design/youtubeplayer.html结构优化
   */
  private findVideoContainer(): HTMLElement | null {
    // 按优先级排序的视频容器选择器
    const videoSelectors = [
      // YouTube 特定选择器（优先级最高）
      '#movie_player',                    // YouTube主播放器容器
      '#player-container',                // YouTube播放器容器
      '#player',                          // 通用播放器ID
      
      // YouTube字幕相关容器（基于参考HTML）
      '.ytp-caption-window-container',    // YouTube原生字幕容器
      '.ytp-caption-window-bottom',       // YouTube底部字幕区域
      '.ytp-caption-segment',             // YouTube字幕段落
      
      // Trancy相关容器已移除
            // YouTube播放器核心组件
      '.html5-video-container',           // HTML5视频容器
      '.html5-video-player',              // HTML5播放器
      '.ytp-chrome-bottom',               // YouTube控制栏区域
      '.ytp-player-content',              // YouTube播放器内容区
      '.ytp-iv-video-content',            // YouTube交互视频内容
      
      // 通用视频播放器选择器
      'video',                            // 视频元素本身
      '.video-player',                    // 通用视频播放器类名
      '.video-container',                 // 通用视频容器类名
      '.player-container',                // 通用播放器容器类名
      '.video-wrapper',                   // 通用视频包装器
      '.video-frame',                     // 视频框架
      '.media-player',                    // 媒体播放器
      '.player-wrapper',                  // 播放器包装器
      
      // 其他视频平台选择器
      '.player',                          // 通用播放器类名
      '#video-player',                    // 视频播放器ID
      '#media-player',                    // 媒体播放器ID
      '#player-wrapper',                  // 播放器包装器ID
      
      // 数据属性选择器
      '[data-testid*="video"]',           // 测试ID包含video
      '[data-testid*="player"]',          // 测试ID包含player
      '[data-player-id]',                 // 有播放器ID的元素
      '[data-video-id]',                  // 有视频ID的元素
      
      // 类名模糊匹配（降低优先级）
      '[class*="video-container"]',       // 类名包含video-container
      '[class*="player-container"]',      // 类名包含player-container
      '[class*="video-player"]',          // 类名包含video-player
      '[class*="media-player"]',          // 类名包含media-player
      '[class*="video"]',                 // 类名包含video
      '[class*="player"]',                // 类名包含player
      '[id*="video"]',                    // ID包含video
      '[id*="player"]',                   // ID包含player
      
      // 最后的备选方案
      'iframe[src*="youtube"]',           // YouTube嵌入框架
      'iframe[src*="video"]',             // 视频嵌入框架
      'embed[src*="video"]',              // 视频嵌入元素
    ];

    // 缓存查找结果以提高性能
    let bestContainer: HTMLElement | null = null;
    let bestScore = 0;

    for (let i = 0; i < videoSelectors.length; i++) {
      const selector = videoSelectors[i];
      try {
        const elements = document.querySelectorAll(selector) as NodeListOf<HTMLElement>;
        
        for (const element of elements) {
          if (this.isValidVideoContainer(element)) {
            // 计算容器的匹配分数（优先级 + 尺寸 + 位置）
            const score = this.calculateContainerScore(element, i, videoSelectors.length);
            
            if (score > bestScore) {
              bestContainer = element;
              bestScore = score;
            }
            
            console.log(`找到视频容器候选: ${selector}`, {
              width: element.getBoundingClientRect().width,
              height: element.getBoundingClientRect().height,
              className: element.className,
              id: element.id,
              score: score
            });
          }
        }
        
        // 如果找到高分容器，可以提前返回
        if (bestScore > 80) {
          break;
        }
      } catch (error) {
        console.warn(`选择器 "${selector}" 查询失败:`, error);
        continue;
      }
    }

    if (bestContainer) {
      console.log(`选择最佳视频容器: ${bestContainer.tagName}`, {
        width: bestContainer.getBoundingClientRect().width,
        height: bestContainer.getBoundingClientRect().height,
        className: bestContainer.className,
        id: bestContainer.id,
        finalScore: bestScore
      });
      return bestContainer;
    }

    console.warn('未找到合适的视频容器，将使用body作为回退');
    return null;
  }

  /**
   * 计算视频容器的匹配分数
   */
  private calculateContainerScore(element: HTMLElement, selectorIndex: number, totalSelectors: number): number {
    let score = 0;
    
    // 基础优先级分数（选择器位置越靠前分数越高）
    const priorityScore = ((totalSelectors - selectorIndex) / totalSelectors) * 40;
    score += priorityScore;
    
    const rect = element.getBoundingClientRect();
    
    // 尺寸分数（更大的容器得分更高，但不能过大）
    const area = rect.width * rect.height;
    const viewportArea = window.innerWidth * window.innerHeight;
    const areaRatio = area / viewportArea;
    
    if (areaRatio > 0.1 && areaRatio < 0.9) {
      score += 30; // 合理的尺寸比例
    } else if (areaRatio >= 0.05 && areaRatio <= 0.1) {
      score += 20; // 稍小但可接受
    } else if (areaRatio >= 0.9) {
      score += 10; // 过大，扣分
    }
    
    // 宽高比分数（16:9等常见视频比例得分更高）
    const aspectRatio = rect.width / rect.height;
    if (aspectRatio >= 1.6 && aspectRatio <= 1.8) {
      score += 20; // 16:9 理想比例
    } else if (aspectRatio >= 1.3 && aspectRatio <= 2.0) {
      score += 15; // 常见视频比例
    } else if (aspectRatio >= 1.0 && aspectRatio <= 2.5) {
      score += 5; // 可接受比例
    }
    
    // 位置分数（居中的容器得分更高）
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const elementCenterX = rect.left + rect.width / 2;
    const elementCenterY = rect.top + rect.height / 2;
    
    const distanceFromCenter = Math.sqrt(
      Math.pow(elementCenterX - centerX, 2) + Math.pow(elementCenterY - centerY, 2)
    );
    const maxDistance = Math.sqrt(Math.pow(centerX, 2) + Math.pow(centerY, 2));
    const centerScore = (1 - (distanceFromCenter / maxDistance)) * 10;
    score += centerScore;
    
    return Math.round(score);
  }

  /**
   * 验证是否为有效的视频容器
   */
  private isValidVideoContainer(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    
    // 基本尺寸检查 - 更严格的最小尺寸
    if (rect.width < 300 || rect.height < 200) {
      return false;
    }
    
    // 检查元素是否可见
    if (rect.width === 0 || rect.height === 0) {
      return false;
    }
    
    // 检查元素是否在视窗内（至少部分可见）
    if (rect.bottom < 0 || rect.top > window.innerHeight || 
        rect.right < 0 || rect.left > window.innerWidth) {
      return false;
    }
    
    // 检查元素的显示状态
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.display === 'none' || 
        computedStyle.visibility === 'hidden' || 
        computedStyle.opacity === '0') {
      return false;
    }
    
    // 检查是否符合常见视频播放器的宽高比（16:9, 4:3等）
    const aspectRatio = rect.width / rect.height;
    if (aspectRatio < 1.2 || aspectRatio > 2.5) {
      return false;
    }
    
    // 对于YouTube特定容器的额外验证
    if (element.id === 'movie_player') {
      // YouTube播放器容器通常包含video元素
      const videoElement = element.querySelector('video');
      return videoElement !== null;
    }
    
    return true;
  }
  public hideSubtitle() {
    if (this.subtitleElement) {
      // 停止监听容器尺寸变化
      if (this.resizeObserver && this.subtitleElement.parentElement) {
        this.resizeObserver.unobserve(this.subtitleElement.parentElement);
      }
      
      this.subtitleElement.classList.remove('visible');
      
      setTimeout(() => {
        if (this.subtitleElement?.parentNode) {
          this.subtitleElement.parentNode.removeChild(this.subtitleElement);
        }
        this.subtitleElement = null;
      }, 300);
    }
    
    this.currentSubtitle = null;
  }

  /**
   * 切换设置面板
   */
  public toggleSettings() {
    if (this.isSettingsVisible) {
      this.hideSettings();
    } else {
      this.showSettings();
    }
  }

  /**
   * 显示设置面板
   */
  private showSettings() {
    // 移除现有设置面板
    this.hideSettings();

    // 创建设置面板元素
    this.settingsElement = document.createElement('div');
    this.settingsElement.className = 'lucid-settings-panel';
    this.settingsElement.id = 'lucid-settings-panel';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'lucid-settings-content';

    // 创建设置项
    const settingItems = this.createSettingItems();
    settingItems.forEach((item, index) => {
      contentDiv.appendChild(item);
      if (index < settingItems.length - 1) {
        const separator = document.createElement('div');
        separator.className = 'lucid-setting-separator';
        contentDiv.appendChild(separator);
      }
    });

    this.settingsElement.appendChild(contentDiv);
    document.body.appendChild(this.settingsElement);

    // 触发显示动画
    requestAnimationFrame(() => {
      if (this.settingsElement) {
        this.settingsElement.classList.add('visible');
      }
    });

    this.isSettingsVisible = true;
  }

  /**
   * 隐藏设置面板
   */
  private hideSettings() {
    if (this.settingsElement) {
      this.settingsElement.classList.remove('visible');
      
      setTimeout(() => {
        if (this.settingsElement?.parentNode) {
          this.settingsElement.parentNode.removeChild(this.settingsElement);
        }
        this.settingsElement = null;
      }, 200);
    }
    
    this.isSettingsVisible = false;
  }

  /**
   * 创建设置项元素数组
   */
  private createSettingItems(): HTMLElement[] {
    const items: HTMLElement[] = [];

    // Lucid 字幕开关
    items.push(this.createToggleItem(
      'subtitle-enabled',
      '⚙️',
      'Lucid 字幕',
      this.config.enabled,
      (enabled) => {
        this.config.enabled = enabled;
        this.saveConfig();
      }
    ));

    // 主字幕语言
    items.push(this.createSelectItem(
      'primary-language',
      '🌐',
      '主字幕',
      this.config.primaryLanguage,
      [
        { label: 'English', value: 'English' },
        { label: '中文', value: '中文' },
        { label: '日本語', value: '日本語' }
      ]
    ));

    // 翻译字幕语言
    items.push(this.createSelectItem(
      'translation-language',
      '🔤',
      '翻译字幕',
      this.config.translationLanguage,
      [
        { label: '中文', value: '中文' },
        { label: 'English', value: 'English' },
        { label: '日本語', value: '日本語' }
      ]
    ));

    // 翻译引擎
    items.push(this.createSelectItem(
      'translation-engine',
      '⚡',
      '翻译引擎',
      this.config.translationEngine,
      [
        { label: 'Microsoft', value: 'Microsoft' },
        { label: 'Google', value: 'Google' },
        { label: 'DeepL', value: 'DeepL' }
      ]
    ));

    // 字幕显示模式
    items.push(this.createSelectItem(
      'display-mode',
      '📺',
      '字幕显示',
      this.getDisplayModeLabel(),
      [
        { label: '双语字幕', value: 'bilingual' },
        { label: '仅原文', value: 'original' },
        { label: '仅译文', value: 'translation' }
      ]
    ));

    // 字幕样式设置
    items.push(this.createActionItem(
      'subtitle-style',
      '🎨',
      '字幕样式',
      () => {
        console.log('打开字幕样式设置');
      }
    ));

    // 快捷键设置
    items.push(this.createActionItem(
      'keyboard-shortcuts',
      '⌨️',
      '设置快捷键',
      () => {
        console.log('打开快捷键设置');
      }
    ));

    return items;
  }

  /**
   * 创建开关设置项
   */
  private createToggleItem(
    id: string,
    icon: string,
    label: string,
    enabled: boolean,
    onChange: (enabled: boolean) => void
  ): HTMLElement {
    const item = document.createElement('div');
    item.className = 'lucid-setting-item';

    const iconDiv = document.createElement('div');
    iconDiv.className = 'lucid-setting-icon';
    iconDiv.textContent = icon;

    const labelDiv = document.createElement('div');
    labelDiv.className = 'lucid-setting-label';
    labelDiv.textContent = label;

    const toggle = this.createToggle(enabled, (newEnabled) => {
      onChange(newEnabled);
    });

    item.appendChild(iconDiv);
    item.appendChild(labelDiv);
    item.appendChild(toggle);

    return item;
  }

  /**
   * 创建选择设置项
   */
  private createSelectItem(
    id: string,
    icon: string,
    label: string,
    value: string,
    options: { label: string; value: string }[]
  ): HTMLElement {
    const item = document.createElement('div');
    item.className = 'lucid-setting-item clickable';

    const iconDiv = document.createElement('div');
    iconDiv.className = 'lucid-setting-icon';
    iconDiv.textContent = icon;

    const labelDiv = document.createElement('div');
    labelDiv.className = 'lucid-setting-label';
    labelDiv.textContent = label;

    const valueDiv = document.createElement('div');
    valueDiv.className = 'lucid-setting-value';
    valueDiv.textContent = value;

    // 添加箭头图标
    const chevron = document.createElement('div');
    chevron.innerHTML = `
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
      </svg>
    `;
    valueDiv.appendChild(chevron);

    item.appendChild(iconDiv);
    item.appendChild(labelDiv);
    item.appendChild(valueDiv);

    return item;
  }

  /**
   * 创建操作设置项
   */
  private createActionItem(
    id: string,
    icon: string,
    label: string,
    onClick: () => void
  ): HTMLElement {
    const item = document.createElement('div');
    item.className = 'lucid-setting-item clickable';

    const iconDiv = document.createElement('div');
    iconDiv.className = 'lucid-setting-icon';
    iconDiv.textContent = icon;

    const labelDiv = document.createElement('div');
    labelDiv.className = 'lucid-setting-label';
    labelDiv.textContent = label;

    const valueDiv = document.createElement('div');
    valueDiv.className = 'lucid-setting-value';
    
    // 添加箭头图标
    const chevron = document.createElement('div');
    chevron.innerHTML = `
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
      </svg>
    `;
    valueDiv.appendChild(chevron);

    item.addEventListener('click', onClick);

    item.appendChild(iconDiv);
    item.appendChild(labelDiv);
    item.appendChild(valueDiv);

    return item;
  }

  /**
   * 创建开关组件
   */
  private createToggle(enabled: boolean, onChange: (enabled: boolean) => void): HTMLElement {
    const toggle = document.createElement('div');
    toggle.className = 'lucid-toggle';

    const track = document.createElement('div');
    track.className = `lucid-toggle-track ${enabled ? 'enabled' : 'disabled'}`;

    const thumb = document.createElement('div');
    thumb.className = `lucid-toggle-thumb ${enabled ? 'enabled' : 'disabled'}`;

    track.appendChild(thumb);
    toggle.appendChild(track);

    // 点击切换
    toggle.addEventListener('click', () => {
      const newEnabled = !enabled;
      
      track.className = `lucid-toggle-track ${newEnabled ? 'enabled' : 'disabled'}`;
      thumb.className = `lucid-toggle-thumb ${newEnabled ? 'enabled' : 'disabled'}`;
      
      enabled = newEnabled;
      onChange(newEnabled);
    });

    return toggle;
  }

  /**
   * 获取显示模式标签
   */
  private getDisplayModeLabel(): string {
    switch (this.config.displayMode) {
      case 'bilingual': return '双语字幕';
      case 'original': return '仅原文';
      case 'translation': return '仅译文';
      default: return '双语字幕';
    }
  }

  /**
   * 保存配置
   */
  private saveConfig() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.set({ 'lucid-subtitle-config': this.config });
      } else {
        localStorage.setItem('lucid-subtitle-config', JSON.stringify(this.config));
      }
    } catch (error) {
      console.error('Failed to save subtitle config:', error);
    }
  }

  /**
   * 加载配置
   */
  private async loadConfig() {
    try {
      let savedConfig;
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get('lucid-subtitle-config');
        savedConfig = result['lucid-subtitle-config'];
      } else {
        const saved = localStorage.getItem('lucid-subtitle-config');
        savedConfig = saved ? JSON.parse(saved) : null;
      }
      
      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      console.error('Failed to load subtitle config:', error);
    }
  }

  /**
   * 获取配置
   */
  public getConfig(): SubtitleTranslationConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(updates: Partial<SubtitleTranslationConfig>) {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
  }

  /**
   * 销毁管理器
   */
  public destroy() {
    this.hideSubtitle();
    this.hideSettings();
    
    // 清理ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
    
    // 移除样式
    const styleElement = document.getElementById('lucid-subtitle-styles');
    if (styleElement) {
      styleElement.remove();
    }
  }
}

// 创建全局实例
export const directSubtitleManager = new DirectSubtitleManager();