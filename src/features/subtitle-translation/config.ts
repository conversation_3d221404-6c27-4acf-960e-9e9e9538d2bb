/**
 * 字幕翻译配置管理
 * 
 * 提供字幕翻译功能的配置管理，包括：
 * - 默认配置定义
 * - 配置验证规则
 * - 配置持久化
 * - 配置迁移
 */

import {
  SubtitleTranslationConfig,
  DisplayConfig,
  SupportedPlatform,
  PlatformConfig,
  SubtitlePosition,
  SubtitleStyle
} from './types';

import { IStorageAdapter, storageAdapter } from '@services/browser-storage.service';

/**
 * 默认字幕翻译配置
 */
export const DEFAULT_SUBTITLE_CONFIG: SubtitleTranslationConfig = {
  enabled: true,
  sourceLang: 'auto',
  targetLang: 'zh-CN',
  showOriginal: false,
  showTranslated: true,
  batchSize: 20,
  maxConcurrency: 3,
  cacheEnabled: true,
  retryCount: 2,
  
  // 显示配置
  display: {
    position: {
      x: 50,    // 居中显示 (%)
      y: 85,    // 靠近底部 (%)
      align: 'center',
      vertical: 'bottom'
    },
    style: {
      fontSize: '16px',
      fontFamily: 'Arial, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      color: '#ffffff',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      fontWeight: 'normal',
      textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'
    },
    showDuration: 0,      // 跟随字幕时间
    fadeInDuration: 200,
    fadeOutDuration: 200,
    maxWidth: '80%',
    zIndex: 2147483647    // 最高层级
  },
  
  // 平台特定配置
  platforms: {
    [SupportedPlatform.YOUTUBE]: {
      interceptUrl: /.*\/api\/timedtext.*/,
      enabled: true,
      timeout: 10000
    },
    [SupportedPlatform.NETFLIX]: {
      interceptUrl: /.*\/nq\/cadmium-playercore.*/,
      enabled: true,
      timeout: 15000
    },
    [SupportedPlatform.PRIME_VIDEO]: {
      interceptUrl: /.*\.amazon\.com.*subtitle.*/,
      enabled: true,
      timeout: 10000
    },
    [SupportedPlatform.DISNEY_PLUS]: {
      interceptUrl: /.*\.disney.*caption.*/,
      enabled: true,
      timeout: 10000
    },
    [SupportedPlatform.GENERIC]: {
      interceptUrl: /.*\.(vtt|srt|ass)(\?.*)?$/,
      enabled: true,
      timeout: 8000
    }
  }
};

/**
 * 配置验证规则
 */
export const SUBTITLE_CONFIG_VALIDATION = {
  // 语言代码验证
  sourceLang: {
    type: 'string' as const,
    required: true,
    pattern: /^[a-z]{2}(-[A-Z]{2})?$|^auto$/,
    message: '源语言格式无效，应为 ISO 639-1 格式（如 en, zh-CN）或 auto'
  },
  
  targetLang: {
    type: 'string' as const,
    required: true,
    pattern: /^[a-z]{2}(-[A-Z]{2})?$/,
    message: '目标语言格式无效，应为 ISO 639-1 格式（如 en, zh-CN）'
  },
  
  // 数值范围验证
  batchSize: {
    type: 'number' as const,
    min: 1,
    max: 50,
    default: 20,
    message: '批处理大小应在 1-50 之间'
  },
  
  maxConcurrency: {
    type: 'number' as const,
    min: 1,
    max: 10,
    default: 3,
    message: '最大并发数应在 1-10 之间'
  },
  
  retryCount: {
    type: 'number' as const,
    min: 0,
    max: 5,
    default: 2,
    message: '重试次数应在 0-5 之间'
  },

  // 显示配置验证
  'display.position.x': {
    type: 'number' as const,
    min: 0,
    max: 100,
    message: 'X 位置应在 0-100 之间（百分比）'
  },

  'display.position.y': {
    type: 'number' as const,
    min: 0,
    max: 100,
    message: 'Y 位置应在 0-100 之间（百分比）'
  },

  'display.fadeInDuration': {
    type: 'number' as const,
    min: 0,
    max: 2000,
    message: '淡入时间应在 0-2000ms 之间'
  },

  'display.fadeOutDuration': {
    type: 'number' as const,
    min: 0,
    max: 2000,
    message: '淡出时间应在 0-2000ms 之间'
  },

  'display.zIndex': {
    type: 'number' as const,
    min: 1,
    max: 2147483647,
    message: 'z-index 应为正整数'
  }
};

/**
 * 支持的语言列表
 */
export const SUPPORTED_LANGUAGES = {
  'auto': '自动检测',
  'en': 'English',
  'zh-CN': '中文（简体）',
  'zh-TW': '中文（繁體）',
  'ja': '日本語',
  'ko': '한국어',
  'es': 'Español',
  'fr': 'Français',
  'de': 'Deutsch',
  'ru': 'Русский',
  'ar': 'العربية',
  'hi': 'हिन्दी',
  'pt': 'Português',
  'it': 'Italiano',
  'nl': 'Nederlands',
  'pl': 'Polski',
  'tr': 'Türkçe',
  'th': 'ไทย',
  'vi': 'Tiếng Việt'
};

/**
 * 配置管理器类
 */
export class SubtitleConfigManager {
  private static readonly STORAGE_KEY = 'lucid-subtitle-translation-config';
  private static readonly VERSION = '1.0.0';
  
  /**
   * 存储适配器实例
   * 可以通过依赖注入替换，便于测试
   */
  private static storageAdapter: IStorageAdapter = storageAdapter;

  /**
   * 设置存储适配器（主要用于测试）
   */
  static setStorageAdapter(adapter: IStorageAdapter): void {
    this.storageAdapter = adapter;
  }

  /**
   * 加载配置
   */
  static async loadConfig(): Promise<SubtitleTranslationConfig> {
    try {
      const stored = await this.storageAdapter.get<any>(this.STORAGE_KEY);
      
      if (stored) {
        const config = this.migrateConfig(stored);
        return this.validateAndMergeConfig(config);
      }

      return { ...DEFAULT_SUBTITLE_CONFIG };

    } catch (error) {
      console.error('❌ [SubtitleConfigManager] 加载配置失败:', error);
      return { ...DEFAULT_SUBTITLE_CONFIG };
    }
  }

  /**
   * 保存配置
   */
  static async saveConfig(config: SubtitleTranslationConfig): Promise<void> {
    try {
      const validatedConfig = this.validateAndMergeConfig(config);
      const configWithMeta = {
        ...validatedConfig,
        _meta: {
          version: this.VERSION,
          timestamp: Date.now()
        }
      };

      await this.storageAdapter.set(this.STORAGE_KEY, configWithMeta);
      console.log('✅ [SubtitleConfigManager] 配置已保存');

    } catch (error) {
      console.error('❌ [SubtitleConfigManager] 保存配置失败:', error);
      throw error;
    }
  }

  /**
   * 验证并合并配置
   */
  private static validateAndMergeConfig(config: Partial<SubtitleTranslationConfig>): SubtitleTranslationConfig {
    const mergedConfig = this.deepMerge(DEFAULT_SUBTITLE_CONFIG, config);
    
    // 恢复正则表达式（JSON序列化后会丢失）
    this.restoreRegexPatterns(mergedConfig);
    
    const validationResult = this.validateConfig(mergedConfig);

    if (!validationResult.valid) {
      console.warn('⚠️ [SubtitleConfigManager] 配置验证失败:', validationResult.errors);
      // 对于验证失败的字段，使用默认值
      for (const error of validationResult.errors) {
        this.applyDefaultForInvalidField(mergedConfig, error.field);
      }
    }

    return mergedConfig;
  }

  /**
   * 验证配置
   */
  static validateConfig(config: SubtitleTranslationConfig): { valid: boolean; errors: Array<{ field: string; message: string }> } {
    const errors: Array<{ field: string; message: string }> = [];

    for (const [field, rule] of Object.entries(SUBTITLE_CONFIG_VALIDATION)) {
      const value = this.getNestedValue(config, field);

      // 检查必需字段
      if ('required' in rule && rule.required && (value === undefined || value === null)) {
        errors.push({ field, message: `${field} 是必需字段` });
        continue;
      }

      if (value !== undefined && value !== null) {
        // 类型检查
        if (rule.type && typeof value !== rule.type) {
          errors.push({ field, message: rule.message || `${field} 类型错误` });
          continue;
        }

        // 数值范围检查
        if (rule.type === 'number') {
          if (rule.min !== undefined && value < rule.min) {
            errors.push({ field, message: rule.message || `${field} 小于最小值 ${rule.min}` });
          }
          if (rule.max !== undefined && value > rule.max) {
            errors.push({ field, message: rule.message || `${field} 大于最大值 ${rule.max}` });
          }
        }

        // 正则表达式检查
        if ('pattern' in rule && rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
          errors.push({ field, message: rule.message || `${field} 格式不正确` });
        }
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 配置迁移
   */
  private static migrateConfig(config: any): SubtitleTranslationConfig {
    // 如果没有版本信息，认为是旧版本
    if (!config._meta?.version) {
      console.log('🔄 [SubtitleConfigManager] 迁移旧版本配置');
      // 这里可以添加具体的迁移逻辑
      return this.migrateFromV0ToV1(config);
    }

    return config;
  }

  /**
   * 从 V0 迁移到 V1
   */
  private static migrateFromV0ToV1(oldConfig: any): SubtitleTranslationConfig {
    const newConfig = { ...DEFAULT_SUBTITLE_CONFIG };

    // 迁移基础设置
    if (oldConfig.enabled !== undefined) newConfig.enabled = oldConfig.enabled;
    if (oldConfig.sourceLang) newConfig.sourceLang = oldConfig.sourceLang;
    if (oldConfig.targetLang) newConfig.targetLang = oldConfig.targetLang;

    // 迁移显示设置
    if (oldConfig.display) {
      newConfig.display = this.deepMerge(newConfig.display!, oldConfig.display);
    }

    return newConfig;
  }

  /**
   * 深度合并对象
   */
  private static deepMerge(target: any, source: any): any {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  }

  /**
   * 恢复正则表达式模式（JSON序列化后会丢失）
   */
  private static restoreRegexPatterns(config: SubtitleTranslationConfig): void {
    if (config.platforms) {
      // 从默认配置恢复正则表达式
      const defaultPlatforms = DEFAULT_SUBTITLE_CONFIG.platforms;
      
      for (const platform in config.platforms) {
        const platformKey = platform as SupportedPlatform;
        if (defaultPlatforms?.[platformKey]) {
          config.platforms[platformKey].interceptUrl = defaultPlatforms[platformKey].interceptUrl;
        }
      }
    }
  }

  /**
   * 获取嵌套值
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 为无效字段应用默认值
   */
  private static applyDefaultForInvalidField(config: any, field: string): void {
    const rule = SUBTITLE_CONFIG_VALIDATION[field as keyof typeof SUBTITLE_CONFIG_VALIDATION];
    if (rule && 'default' in rule && rule.default !== undefined) {
      const keys = field.split('.');
      let current = config;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = rule.default;
    }
  }

  /**
   * 重置为默认配置
   */
  static async resetToDefault(): Promise<SubtitleTranslationConfig> {
    const defaultConfig = { ...DEFAULT_SUBTITLE_CONFIG };
    await this.saveConfig(defaultConfig);
    return defaultConfig;
  }

  /**
   * 获取支持的语言列表
   */
  static getSupportedLanguages(): typeof SUPPORTED_LANGUAGES {
    return { ...SUPPORTED_LANGUAGES };
  }

  /**
   * 获取语言显示名称
   */
  static getLanguageDisplayName(code: string): string {
    return SUPPORTED_LANGUAGES[code as keyof typeof SUPPORTED_LANGUAGES] || code;
  }
}