/**
 * VTT (WebVTT) 格式字幕解析器
 * 
 * 支持 WebVTT 格式的字幕文件解析
 * 格式规范: https://www.w3.org/TR/webvtt1/
 */

import { StandardSubtitle, SubtitleFormat } from '../types';
import { BaseSubtitleParser } from './base-parser';

export class VTTParser extends BaseSubtitleParser {
  constructor() {
    super(SubtitleFormat.VTT);
  }

  /**
   * 解析 VTT 格式字幕
   * 移除静默失败，正确抛出异常
   */
  async parse(data: string): Promise<StandardSubtitle[]> {
    return this.safeParseWrapper(data, this.parseVTT.bind(this));
  }

  /**
   * VTT 解析核心逻辑
   */
  private async parseVTT(data: string): Promise<StandardSubtitle[]> {
    const lines = data.split('\n').map(line => line.trim());
    
    // 验证 VTT 头部
    if (!lines[0].startsWith('WEBVTT')) {
      throw new Error('无效的 VTT 格式：缺少 WEBVTT 标识');
    }

    const subtitles: StandardSubtitle[] = [];
    let currentIndex = 0;
    
    // 跳过头部和空行
    let i = 1;
    while (i < lines.length) {
      const line = lines[i];
      
      // 跳过空行和特殊块（NOTE, STYLE等）
      if (!line || line.startsWith('NOTE') || line.startsWith('STYLE') || line.startsWith('::cue')) {
        i++;
        continue;
      }
      
      // 检查是否是时间轴行
      if (this.isTimingLine(line)) {
        const timing = this.parseTimingLine(line);
        if (timing) {
          // 收集字幕文本（可能多行）
          const textLines: string[] = [];
          i++;
          
          while (i < lines.length && lines[i] && !this.isTimingLine(lines[i])) {
            const textLine = lines[i];
            // 跳过特殊控制行
            if (!textLine.startsWith('NOTE') && !textLine.startsWith('STYLE') && !textLine.startsWith('::cue')) {
              textLines.push(textLine);
            }
            i++;
          }
          
          if (textLines.length > 0) {
            const text = this.cleanText(textLines.join(' '));
            
            if (text) {
              const subtitle: StandardSubtitle = {
                id: this.generateId(currentIndex, timing.startTime),
                startTime: timing.startTime,
                endTime: timing.endTime,
                text: text
              };

              // 解析 VTT 位置信息
              const positionInfo = this.parseVTTPosition(line);
              if (positionInfo) {
                subtitle.position = positionInfo;
              }

              subtitles.push(subtitle);
              currentIndex++;
            }
          }
        }
      } else {
        // 可能是字幕ID行，检查下一行是否是时间轴
        const nextLineIndex = i + 1;
        if (nextLineIndex < lines.length && this.isTimingLine(lines[nextLineIndex])) {
          // 当前行是ID，下一行是时间轴
          const cueId = line;
          const timingLine = lines[nextLineIndex];
          const timing = this.parseTimingLine(timingLine);
          
          if (timing) {
            // 收集字幕文本
            const textLines: string[] = [];
            let j = nextLineIndex + 1;
            
            while (j < lines.length && lines[j] && !this.isTimingLine(lines[j])) {
              const textLine = lines[j];
              // 跳过特殊控制行  
              if (!textLine.startsWith('NOTE') && !textLine.startsWith('STYLE') && !textLine.startsWith('::cue')) {
                textLines.push(textLine);
              }
              j++;
            }
            
            if (textLines.length > 0) {
              const text = this.cleanText(textLines.join(' '));
              
              if (text) {
                const subtitle: StandardSubtitle = {
                  id: cueId, // 使用实际的ID
                  startTime: timing.startTime,
                  endTime: timing.endTime,
                  text: text
                };

                // 解析 VTT 位置信息
                const positionInfo = this.parseVTTPosition(timingLine);
                if (positionInfo) {
                  subtitle.position = positionInfo;
                }

                subtitles.push(subtitle);
              }
            }
            
            i = j; // 跳转到处理过的位置
            continue;
          }
        }
        i++;
      }
    }

    return this.postProcessSubtitles(subtitles);
  }

  /**
   * 检查是否为时间轴行
   */
  private isTimingLine(line: string): boolean {
    // VTT 时间格式: 00:00:01.000 --> 00:00:04.000
    return /\d{2}:\d{2}:\d{2}\.\d{3}\s*-->\s*\d{2}:\d{2}:\d{2}\.\d{3}/.test(line);
  }

  /**
   * 解析时间轴行
   */
  private parseTimingLine(line: string): { startTime: number; endTime: number } | null {
    try {
      // 匹配时间格式，支持样式设置
      const match = line.match(/(\d{2}:\d{2}:\d{2}\.\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}\.\d{3})/);
      
      if (match) {
        const [, startTimeStr, endTimeStr] = match;
        
        return {
          startTime: this.timeToMs(startTimeStr),
          endTime: this.timeToMs(endTimeStr)
        };
      }
      
      return null;
    } catch (error) {
      console.error('⚠️ [VTTParser] 时间轴解析失败:', line, error);
      return null;
    }
  }

  /**
   * 解析 VTT 位置信息
   */
  private parseVTTPosition(timingLine: string): any {
    // 解析 WebVTT 位置设置，如: position:50% line:0%
    const positionMatch = timingLine.match(/position:(\d+)%/);
    const lineMatch = timingLine.match(/line:(\d+)%/);
    const alignMatch = timingLine.match(/align:(start|center|end)/);
    const verticalMatch = timingLine.match(/vertical:(lr|rl)/);
    
    if (positionMatch || lineMatch || alignMatch || verticalMatch) {
      const position: any = {};
      
      if (positionMatch) {
        position.x = parseInt(positionMatch[1]);
      }
      
      if (lineMatch) {
        position.y = parseInt(lineMatch[1]);
      }
      
      // 设置默认值
      position.align = alignMatch ? alignMatch[1] : 'center';
      position.vertical = verticalMatch ? (verticalMatch[1] === 'lr' ? 'left' : 'right') : 'top';
      
      return position;
    }
    
    return null;
  }

  /**
   * 解析 VTT 样式和位置信息
   */
  private parseVTTStyle(text: string): { position?: any; cleanText?: string } {
    // VTT 支持位置标记，如: <v Speaker>Hello world</v>
    // 这里简化处理，主要清理标记
    const cleanText = text.replace(/<v[^>]*>/g, '').replace(/<\/v>/g, '');
    
    // 可以扩展支持更多 VTT 特定样式解析
    return { cleanText };
  }

  /**
   * 检测是否为 VTT 格式
   */
  static detectFormat(data: string): boolean {
    const trimmedData = data.trim();
    return trimmedData.startsWith('WEBVTT');
  }
}