/**
 * 基础字幕解析器抽象类
 * 
 * 为所有字幕格式解析器提供通用接口和工具方法
 */

import { 
  StandardSubtitle, 
  SubtitleFormat, 
  ISubtitleParser, 
  ValidationResult,
  SubtitleError,
  SubtitleErrorType
} from '../types';

import { StandardTimeParser, TimeParsingException } from './time-parser';

export abstract class BaseSubtitleParser implements ISubtitleParser {
  protected format: SubtitleFormat;

  constructor(format: SubtitleFormat) {
    this.format = format;
  }

  /**
   * 解析字幕数据 - 抽象方法，由子类实现
   */
  abstract parse(data: string): Promise<StandardSubtitle[]>;

  /**
   * 验证字幕数据完整性
   */
  validate(subtitles: StandardSubtitle[]): ValidationResult {
    const errors: string[] = [];
    
    if (!subtitles || subtitles.length === 0) {
      errors.push('字幕数组为空');
      return { valid: false, errors };
    }

    for (let i = 0; i < subtitles.length; i++) {
      const subtitle = subtitles[i];
      
      if (!subtitle.id) {
        errors.push(`字幕 ${i} 缺少 ID`);
      }
      
      if (typeof subtitle.startTime !== 'number' || subtitle.startTime < 0) {
        errors.push(`字幕 ${i} 开始时间无效: ${subtitle.startTime}`);
      }
      
      if (typeof subtitle.endTime !== 'number' || subtitle.endTime <= subtitle.startTime) {
        errors.push(`字幕 ${i} 结束时间无效: ${subtitle.endTime} <= ${subtitle.startTime}`);
      }
      
      if (!subtitle.text || typeof subtitle.text !== 'string' || subtitle.text.trim().length === 0) {
        errors.push(`字幕 ${i} 文本内容为空`);
      }

      // 检查时间轴是否合理（不超过24小时）
      if (subtitle.endTime > 24 * 60 * 60 * 1000) {
        errors.push(`字幕 ${i} 时间轴异常: ${subtitle.endTime}ms`);
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 时间字符串转毫秒 - 使用标准化时间解析器
   * @param timeStr 时间字符串
   * @returns 毫秒数
   * @throws TimeParsingException 格式不匹配时抛出异常
   */
  protected timeToMs(timeStr: string): number {
    try {
      return StandardTimeParser.parseToMs(timeStr);
    } catch (error) {
      // 将时间解析异常转换为字幕解析错误
      if (error instanceof TimeParsingException) {
        console.error(`⚠️ [${this.format}Parser] 时间解析失败:`, error.message);
        throw new SubtitleError(
          SubtitleErrorType.PARSING_ERROR,
          error.message,
          error,
          { format: this.format, timeString: timeStr }
        );
      }
      throw error;
    }
  }

  /**
   * 毫秒转时间字符串 - 使用标准化时间格式器
   * @param ms 毫秒数
   * @param useComma 是否使用逗号分隔符（SRT格式）
   * @returns 格式化的时间字符串
   */
  protected msToTime(ms: number, useComma = false): string {
    try {
      const format = useComma ? 'SRT' : 'VTT';
      return StandardTimeParser.msToTimeString(ms, format);
    } catch (error) {
      console.error(`⚠️ [${this.format}Parser] 时间格式化失败:`, error);
      return '00:00:00,000';
    }
  }

  /**
   * 清理文本内容 - 移除 HTML 标签和多余空白
   */
  protected cleanText(text: string): string {
    return text
      .replace(/<[^>]*>/g, '') // 移除 HTML 标签
      .replace(/&lt;/g, '<')   // 反转义 HTML 实体
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ')     // 合并多个空白字符
      .trim();
  }

  /**
   * 生成字幕 ID
   */
  protected generateId(index: number, startTime: number): string {
    return `${this.format}_${index}_${startTime}`;
  }

  /**
   * 错误处理包装器
   */
  protected async safeParseWrapper(data: string, parseFunction: (data: string) => Promise<StandardSubtitle[]>): Promise<StandardSubtitle[]> {
    try {
      if (!data || typeof data !== 'string') {
        throw new Error('输入数据无效');
      }

      const trimmedData = data.trim();
      if (trimmedData.length === 0) {
        throw new Error('输入数据为空');
      }

      const result = await parseFunction(trimmedData);
      
      // 验证解析结果
      const validation = this.validate(result);
      if (!validation.valid) {
        console.warn(`⚠️ [${this.format}Parser] 解析结果验证失败:`, validation.errors);
        // 对于验证失败，我们仍然返回结果，但会记录警告
      }

      return result;

    } catch (error) {
      throw new SubtitleError(
        SubtitleErrorType.PARSING_ERROR,
        `${this.format} 格式解析失败: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error : new Error(String(error)),
        { format: this.format, dataLength: data?.length || 0 }
      );
    }
  }

  /**
   * 后处理字幕数据 - 排序和去重
   */
  protected postProcessSubtitles(subtitles: StandardSubtitle[]): StandardSubtitle[] {
    // 按开始时间排序
    const sorted = subtitles.sort((a, b) => a.startTime - b.startTime);
    
    // 去重 - 基于时间和文本内容
    const deduplicated: StandardSubtitle[] = [];
    const seen = new Set<string>();
    
    for (const subtitle of sorted) {
      const key = `${subtitle.startTime}-${subtitle.endTime}-${subtitle.text}`;
      if (!seen.has(key)) {
        seen.add(key);
        deduplicated.push(subtitle);
      }
    }
    
    return deduplicated;
  }
}