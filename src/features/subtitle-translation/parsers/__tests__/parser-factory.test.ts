/**
 * 字幕解析器工厂类单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SubtitleParserFactory } from '../parser-factory';
import { SubtitleFormat } from '../../types';

describe('SubtitleParserFactory', () => {

  describe('format detection', () => {
    it('应该正确检测 VTT 格式', () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Hello World`;

      expect(SubtitleParserFactory.detectFormat(vttContent)).toBe(SubtitleFormat.VTT);
    });

    it('应该正确检测 SRT 格式', () => {
      const srtContent = `1
00:00:00,000 --> 00:00:03,000
Hello World`;

      expect(SubtitleParserFactory.detectFormat(srtContent)).toBe(SubtitleFormat.SRT);
    });

    it('应该正确检测 YouTube JSON 格式', () => {
      const youtubeContent = `{
        "events": [
          {
            "tStartMs": 0,
            "dDurationMs": 3000,
            "segs": [{"utf8": "Hello World"}]
          }
        ]
      }`;

      expect(SubtitleParserFactory.detectFormat(youtubeContent)).toBe(SubtitleFormat.YOUTUBE_JSON);
    });

    it('应该正确检测 ASS 格式', () => {
      const assContent = `[Script Info]
Title: Test

[V4+ Styles]
Format: Name, Fontname

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:00.00,0:00:03.00,Default,,0,0,0,,Hello World`;

      expect(SubtitleParserFactory.detectFormat(assContent)).toBe(SubtitleFormat.ASS);
    });

    it('应该返回 UNKNOWN 对于无法识别的格式', () => {
      const unknownContent = `This is not a subtitle format`;
      expect(SubtitleParserFactory.detectFormat(unknownContent)).toBe(SubtitleFormat.UNKNOWN);
    });

    it('应该返回 UNKNOWN 对于空内容', () => {
      expect(SubtitleParserFactory.detectFormat('')).toBe(SubtitleFormat.UNKNOWN);
      expect(SubtitleParserFactory.detectFormat('   ')).toBe(SubtitleFormat.UNKNOWN);
    });

    it('应该返回 UNKNOWN 对于 null 或 undefined', () => {
      expect(SubtitleParserFactory.detectFormat(null as any)).toBe(SubtitleFormat.UNKNOWN);
      expect(SubtitleParserFactory.detectFormat(undefined as any)).toBe(SubtitleFormat.UNKNOWN);
    });
  });

  describe('parsing', () => {
    it('应该使用正确的解析器解析 VTT 格式', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Hello World`;

      const result = await SubtitleParserFactory.parse(vttContent, SubtitleFormat.VTT);
      
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Hello World');
      expect(result[0].startTime).toBe(0);
      expect(result[0].endTime).toBe(3000);
    });

    it('应该使用正确的解析器解析 SRT 格式', async () => {
      const srtContent = `1
00:00:00,000 --> 00:00:03,000
Hello World`;

      const result = await SubtitleParserFactory.parse(srtContent, SubtitleFormat.SRT);
      
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Hello World');
      expect(result[0].id).toBe('1');
    });

    it('应该抛出错误对于不支持的格式', async () => {
      await expect(
        SubtitleParserFactory.parse('test', SubtitleFormat.UNKNOWN)
      ).rejects.toThrow('不支持的字幕格式: unknown');
    });

    it('应该记录解析时间', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Hello World`;

      await SubtitleParserFactory.parse(vttContent, SubtitleFormat.VTT);
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('🔧 [SubtitleParserFactory] VTT 解析完成')
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('auto parsing', () => {
    it('应该自动检测并解析 VTT 格式', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Hello World`;

      const result = await SubtitleParserFactory.autoParse(vttContent);
      
      expect(result.format).toBe(SubtitleFormat.VTT);
      expect(result.subtitles).toHaveLength(1);
      expect(result.subtitles[0].text).toBe('Hello World');
    });

    it('应该自动检测并解析 SRT 格式', async () => {
      const srtContent = `1
00:00:00,000 --> 00:00:03,000
Hello World`;

      const result = await SubtitleParserFactory.autoParse(srtContent);
      
      expect(result.format).toBe(SubtitleFormat.SRT);
      expect(result.subtitles).toHaveLength(1);
      expect(result.subtitles[0].text).toBe('Hello World');
    });

    it('应该抛出错误对于无法识别的格式', async () => {
      const unknownContent = `This is not a subtitle format`;
      
      await expect(
        SubtitleParserFactory.autoParse(unknownContent)
      ).rejects.toThrow('无法识别的字幕格式');
    });
  });

  describe('validation', () => {
    it('应该验证有效的字幕数组', () => {
      const validSubtitles = [
        {
          id: '1',
          startTime: 0,
          endTime: 3000,
          text: 'Hello World'
        },
        {
          id: '2',
          startTime: 3000,
          endTime: 6000,
          text: 'Second subtitle'
        }
      ];

      const result = SubtitleParserFactory.validate(validSubtitles);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该检测空数组', () => {
      const result = SubtitleParserFactory.validate([]);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('字幕数组为空');
    });

    it('应该检测缺少 ID 的字幕', () => {
      const invalidSubtitles = [
        {
          id: '',
          startTime: 0,
          endTime: 3000,
          text: 'Hello World'
        }
      ];

      const result = SubtitleParserFactory.validate(invalidSubtitles);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('字幕 0 缺少 ID');
    });

    it('应该检测无效的开始时间', () => {
      const invalidSubtitles = [
        {
          id: '1',
          startTime: -100,
          endTime: 3000,
          text: 'Hello World'
        }
      ];

      const result = SubtitleParserFactory.validate(invalidSubtitles);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('字幕 0 开始时间无效: -100');
    });

    it('应该检测无效的结束时间', () => {
      const invalidSubtitles = [
        {
          id: '1',
          startTime: 5000,
          endTime: 3000,  // 结束时间小于开始时间
          text: 'Hello World'
        }
      ];

      const result = SubtitleParserFactory.validate(invalidSubtitles);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('字幕 0 结束时间无效: 3000 <= 5000');
    });

    it('应该检测空的文本内容', () => {
      const invalidSubtitles = [
        {
          id: '1',
          startTime: 0,
          endTime: 3000,
          text: '   '  // 只有空格
        }
      ];

      const result = SubtitleParserFactory.validate(invalidSubtitles);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('字幕 0 文本内容为空');
    });

    it('应该检测时间轴异常（超过24小时）', () => {
      const invalidSubtitles = [
        {
          id: '1',
          startTime: 0,
          endTime: 25 * 60 * 60 * 1000,  // 25小时
          text: 'Hello World'
        }
      ];

      const result = SubtitleParserFactory.validate(invalidSubtitles);
      
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('字幕 0 时间轴异常');
    });

    it('应该检测时间重叠', () => {
      const invalidSubtitles = [
        {
          id: '1',
          startTime: 0,
          endTime: 5000,
          text: 'First subtitle'
        },
        {
          id: '2',
          startTime: 3000,  // 重叠：3000 < 5000
          endTime: 8000,
          text: 'Second subtitle'
        }
      ];

      const result = SubtitleParserFactory.validate(invalidSubtitles);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('字幕 0 与 1 时间重叠');
    });
  });

  describe('batch parsing', () => {
    it('应该批量解析多个字幕文件', async () => {
      const dataList = [
        {
          id: 'vtt-file',
          data: `WEBVTT

00:00:00.000 --> 00:00:03.000
VTT subtitle`,
          format: SubtitleFormat.VTT
        },
        {
          id: 'srt-file',
          data: `1
00:00:00,000 --> 00:00:03,000
SRT subtitle`,
          format: SubtitleFormat.SRT
        }
      ];

      const results = await SubtitleParserFactory.parseMultiple(dataList);
      
      expect(results).toHaveLength(2);
      expect(results[0].id).toBe('vtt-file');
      expect(results[0].format).toBe(SubtitleFormat.VTT);
      expect(results[0].subtitles).toHaveLength(1);
      expect(results[0].error).toBeUndefined();
      
      expect(results[1].id).toBe('srt-file');
      expect(results[1].format).toBe(SubtitleFormat.SRT);
      expect(results[1].subtitles).toHaveLength(1);
      expect(results[1].error).toBeUndefined();
    });

    it('应该自动检测格式当未指定时', async () => {
      const dataList = [
        {
          id: 'auto-detect',
          data: `WEBVTT

00:00:00.000 --> 00:00:03.000
Auto detected VTT`
        }
      ];

      const results = await SubtitleParserFactory.parseMultiple(dataList);
      
      expect(results[0].format).toBe(SubtitleFormat.VTT);
      expect(results[0].subtitles).toHaveLength(1);
    });

    it('应该处理解析错误', async () => {
      const dataList = [
        {
          id: 'invalid-data',
          data: 'This is not subtitle content',
          format: SubtitleFormat.VTT
        }
      ];

      const results = await SubtitleParserFactory.parseMultiple(dataList);
      
      expect(results[0].id).toBe('invalid-data');
      expect(results[0].subtitles).toHaveLength(0);
      expect(results[0].error).toBeDefined();
    });
  });

  describe('utility methods', () => {
    it('应该返回支持的格式列表', () => {
      const formats = SubtitleParserFactory.getSupportedFormats();
      
      expect(formats).toContain(SubtitleFormat.VTT);
      expect(formats).toContain(SubtitleFormat.SRT);
      expect(formats).toContain(SubtitleFormat.YOUTUBE_JSON);
      expect(formats).toContain(SubtitleFormat.ASS);
    });

    it('应该返回解析器统计信息', () => {
      const stats = SubtitleParserFactory.getParserStats();
      
      expect(stats).toEqual(
        expect.arrayContaining([
          { format: SubtitleFormat.VTT, available: true },
          { format: SubtitleFormat.SRT, available: true },
          { format: SubtitleFormat.YOUTUBE_JSON, available: true },
          { format: SubtitleFormat.ASS, available: true }
        ])
      );
    });

    it('应该允许注册新的解析器', () => {
      const mockParser = {
        parse: vi.fn().mockResolvedValue([]),
        format: 'CUSTOM' as any
      };

      SubtitleParserFactory.registerParser('CUSTOM' as any, mockParser);
      
      const formats = SubtitleParserFactory.getSupportedFormats();
      expect(formats).toContain('CUSTOM');
    });
  });

  describe('format conversion', () => {
    it('应该转换 SRT 到 VTT 格式', async () => {
      const srtContent = `1
00:00:00,000 --> 00:00:03,000
Hello World

2
00:00:03,000 --> 00:00:06,000
Second subtitle`;

      const vttContent = await SubtitleParserFactory.convertFormat(
        srtContent, 
        SubtitleFormat.SRT, 
        SubtitleFormat.VTT
      );

      expect(vttContent).toContain('WEBVTT');
      expect(vttContent).toContain('00:00:00.000 --> 00:00:03.000');
      expect(vttContent).toContain('Hello World');
    });

    it('应该转换 VTT 到 SRT 格式', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Hello World

00:00:03.000 --> 00:00:06.000
Second subtitle`;

      const srtContent = await SubtitleParserFactory.convertFormat(
        vttContent,
        SubtitleFormat.VTT,
        SubtitleFormat.SRT
      );

      expect(srtContent).toContain('1\n00:00:00,000 --> 00:00:03,000');
      expect(srtContent).toContain('2\n00:00:03,000 --> 00:00:06,000');
      expect(srtContent).toContain('Hello World');
    });

    it('应该返回原内容当格式相同时', async () => {
      const content = 'test content';
      const result = await SubtitleParserFactory.convertFormat(
        content,
        SubtitleFormat.VTT,
        SubtitleFormat.VTT
      );

      expect(result).toBe(content);
    });
  });
});