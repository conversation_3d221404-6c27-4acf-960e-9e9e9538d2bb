/**
 * SRT解析器测试文件 - 基于新的标准化时间解析器
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { SRTParser } from '../srt-parser';
import { StandardSubtitle } from '../../types';

describe('SRTParser', () => {
  let parser: SRTParser;

  beforeEach(() => {
    parser = new SRTParser();
  });

  describe('基本SRT解析功能', () => {
    it('应该解析标准SRT格式', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
Hello World

2
00:00:05,500 --> 00:00:08,250
This is a test`;

      const result = await parser.parse(srtContent);

      expect(result).toHaveLength(2);
      
      expect(result[0]).toEqual({
        id: '1',
        startTime: 1000,
        endTime: 4000,
        text: 'Hello World'
      });

      expect(result[1]).toEqual({
        id: '2',
        startTime: 5500,
        endTime: 8250,
        text: 'This is a test'
      });
    });

    it('应该处理多行字幕文本', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
Hello World
This is line 2
And line 3

2
00:00:05,000 --> 00:00:07,000
Single line`;

      const result = await parser.parse(srtContent);

      expect(result).toHaveLength(2);
      expect(result[0].text).toBe('Hello World This is line 2 And line 3');
      expect(result[1].text).toBe('Single line');
    });

    it('应该处理不同的换行符格式', async () => {
      const srtContent = `1\r\n00:00:01,000 --> 00:00:04,000\r\nHello World\r\n\r\n2\r\n00:00:05,000 --> 00:00:07,000\r\nTest`;

      const result = await parser.parse(srtContent);

      expect(result).toHaveLength(2);
      expect(result[0].text).toBe('Hello World');
      expect(result[1].text).toBe('Test');
    });
  });

  describe('严格时间格式验证', () => {
    it('应该要求严格的SRT时间格式（HH:MM:SS,mmm）', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
Hello World`;

      const result = await parser.parse(srtContent);

      expect(result).toHaveLength(1);
      expect(result[0].startTime).toBe(1000);
      expect(result[0].endTime).toBe(4000);
    });

    it('应该拒绝不标准的毫秒格式', async () => {
      const invalidSrtContent = `1
00:00:01,1 --> 00:00:04,12
Hello World`;

      // 使用严格的格式验证，格式不正确的块应该被跳过
      const result = await parser.parse(invalidSrtContent);
      expect(result).toHaveLength(0); // 无效块被跳过，返回空数组
    });

    it('应该拒绝VTT格式的点分隔符', async () => {
      const invalidSrtContent = `1
00:00:01.000 --> 00:00:04.000
Hello World`;

      // SRT格式必须使用逗号分隔符，点分隔符的块应该被跳过
      const result = await parser.parse(invalidSrtContent);
      expect(result).toHaveLength(0); // 无效块被跳过，返回空数组
    });

    it('应该验证时间轴的逻辑正确性', async () => {
      const invalidSrtContent = `1
00:00:04,000 --> 00:00:01,000
Invalid timing`;

      // 结束时间不能早于开始时间，这种块应该被跳过
      const result = await parser.parse(invalidSrtContent);
      expect(result).toHaveLength(0); // 无效块被跳过，返回空数组
    });
  });

  describe('HTML实体和特殊字符处理', () => {
    it('应该正确解码HTML实体', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
Special chars: &amp; &lt; &gt; &quot; &#39;`;

      const result = await parser.parse(srtContent);
      
      expect(result[0].text).toBe('Special chars: & < > " \'');
    });

    it('应该移除HTML标签', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
<i>Italic text</i> and <b>bold text</b>`;

      const result = await parser.parse(srtContent);
      
      expect(result[0].text).toBe('Italic text and bold text');
    });

    it('应该处理复杂的嵌套HTML', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
<font color="red"><i>Red italic</i></font> text`;

      const result = await parser.parse(srtContent);
      
      expect(result[0].text).toBe('Red italic text');
    });
  });

  describe('错误处理和容错性', () => {
    it('应该跳过格式错误的字幕块但继续处理其他块', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
Good subtitle

invalid_block
This is not valid

2
00:00:05,000 --> 00:00:07,000
Another good subtitle`;

      // 应该只解析有效的块
      const result = await parser.parse(srtContent);

      expect(result).toHaveLength(2);
      expect(result[0].text).toBe('Good subtitle');
      expect(result[1].text).toBe('Another good subtitle');
    });

    it('应该处理空的字幕文本', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000


2
00:00:05,000 --> 00:00:07,000
Valid text`;

      const result = await parser.parse(srtContent);

      // 空文本的字幕块应该被跳过
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Valid text');
    });

    it('应该处理完全空的输入', async () => {
      await expect(parser.parse('')).rejects.toThrow();
    });

    it('应该处理只有空白的输入', async () => {
      await expect(parser.parse('   \n\n   ')).rejects.toThrow();
    });
  });

  describe('字幕后处理', () => {
    it('应该按时间顺序排序字幕', async () => {
      const srtContent = `2
00:00:05,000 --> 00:00:07,000
Second subtitle

1
00:00:01,000 --> 00:00:04,000
First subtitle`;

      const result = await parser.parse(srtContent);

      expect(result).toHaveLength(2);
      expect(result[0].startTime).toBe(1000); // 应该被排序到前面
      expect(result[1].startTime).toBe(5000);
    });

    it('应该去除重复的字幕', async () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
Duplicate subtitle

2
00:00:01,000 --> 00:00:04,000
Duplicate subtitle

3
00:00:05,000 --> 00:00:07,000
Unique subtitle`;

      const result = await parser.parse(srtContent);

      expect(result).toHaveLength(2); // 重复的应该被去除
      expect(result[0].text).toBe('Duplicate subtitle');
      expect(result[1].text).toBe('Unique subtitle');
    });
  });

  describe('格式检测', () => {
    it('应该正确检测SRT格式', () => {
      const srtContent = `1
00:00:01,000 --> 00:00:04,000
Hello World`;

      expect(SRTParser.detectFormat(srtContent)).toBe(true);
    });

    it('应该拒绝VTT格式', () => {
      const vttContent = `WEBVTT

00:00:01.000 --> 00:00:04.000
Hello World`;

      expect(SRTParser.detectFormat(vttContent)).toBe(false);
    });

    it('应该拒绝格式不完整的内容', () => {
      const incompleteContent = `1
Just one line`;

      expect(SRTParser.detectFormat(incompleteContent)).toBe(false);
    });
  });
});