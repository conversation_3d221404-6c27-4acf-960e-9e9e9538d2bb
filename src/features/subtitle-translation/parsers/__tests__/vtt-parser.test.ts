/**
 * VTT 字幕解析器单元测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { VTTParser } from '../vtt-parser';
import { SubtitleFormat } from '../../types';

describe('VTTParser', () => {
  let parser: VTTParser;

  beforeEach(() => {
    parser = new VTTParser();
  });

  describe('format detection', () => {
    it('应该正确识别 VTT 格式', () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Hello World

00:00:03.000 --> 00:00:06.000
This is a test`;

      expect(VTTParser.detectFormat(vttContent)).toBe(true);
    });

    it('应该正确识别带有 NOTE 的 VTT 格式', () => {
      const vttContent = `WEBVTT

NOTE
This is a note

00:00:00.000 --> 00:00:03.000
Hello World`;

      expect(VTTParser.detectFormat(vttContent)).toBe(true);
    });

    it('应该拒绝非 VTT 格式', () => {
      const srtContent = `1
00:00:00,000 --> 00:00:03,000
Hello World`;

      expect(VTTParser.detectFormat(srtContent)).toBe(false);
    });

    it('应该拒绝空内容', () => {
      expect(VTTParser.detectFormat('')).toBe(false);
      expect(VTTParser.detectFormat('   ')).toBe(false);
    });
  });

  describe('parsing', () => {
    it('应该正确解析基本的 VTT 字幕', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Hello World

00:00:03.000 --> 00:00:06.000
This is a test`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        id: expect.any(String),
        startTime: 0,
        endTime: 3000,
        text: 'Hello World'
      });
      expect(result[1]).toEqual({
        id: expect.any(String),
        startTime: 3000,
        endTime: 6000,
        text: 'This is a test'
      });
    });

    it('应该正确解析带有 ID 的 VTT 字幕', async () => {
      const vttContent = `WEBVTT

1
00:00:00.000 --> 00:00:03.000
Hello World

2
00:00:03.000 --> 00:00:06.000
This is a test`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('1');
      expect(result[1].id).toBe('2');
    });

    it('应该正确处理多行字幕文本', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
Line 1
Line 2
Line 3`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Line 1 Line 2 Line 3');
    });

    it('应该跳过 NOTE 块', async () => {
      const vttContent = `WEBVTT

NOTE
This is a note

00:00:00.000 --> 00:00:03.000
Hello World

NOTE
Another note

00:00:03.000 --> 00:00:06.000
This is a test`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(2);
      expect(result[0].text).toBe('Hello World');
      expect(result[1].text).toBe('This is a test');
    });

    it('应该跳过 STYLE 块', async () => {
      const vttContent = `WEBVTT

STYLE
::cue {
  color: white;
}

00:00:00.000 --> 00:00:03.000
Hello World`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Hello World');
    });

    it('应该正确解析带有位置信息的字幕', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000 position:50% line:0%
Hello World`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(1);
      expect(result[0].position).toEqual({
        x: 50,
        y: 0,
        align: 'center',
        vertical: 'top'
      });
    });

    it('应该处理格式错误并抛出异常', async () => {
      const invalidContent = `This is not VTT content`;
      
      // 修复后的VTT解析器对于格式错误应该抛出异常，不再静默失败
      await expect(parser.parse(invalidContent)).rejects.toThrow('无效的 VTT 格式：缺少 WEBVTT 标识');
    });

    it('应该处理时间格式错误', async () => {
      const vttContent = `WEBVTT

invalid:time:format --> 00:00:03.000
Hello World

00:00:00.000 --> invalid:time
This should be skipped

00:00:03.000 --> 00:00:06.000
This is valid`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(1);  // 只有最后一个有效的字幕
      expect(result[0].text).toBe('This is valid');
    });

    it('应该过滤空的字幕文本', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000


00:00:03.000 --> 00:00:06.000
   

00:00:06.000 --> 00:00:09.000
Valid text`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Valid text');
    });

    it('应该清理 VTT 标记', async () => {
      const vttContent = `WEBVTT

00:00:00.000 --> 00:00:03.000
<c>Hello</c> <v Speaker>World</v>

00:00:03.000 --> 00:00:06.000
<u>Underlined</u> text`;

      const result = await parser.parse(vttContent);
      
      expect(result).toHaveLength(2);
      expect(result[0].text).toBe('Hello World');
      expect(result[1].text).toBe('Underlined text');
    });
  });

  describe('time parsing', () => {
    it('应该正确解析毫秒时间', async () => {
      const vttContent = `WEBVTT

00:00:00.123 --> 00:00:03.456
Test`;

      const result = await parser.parse(vttContent);
      
      expect(result[0].startTime).toBe(123);
      expect(result[0].endTime).toBe(3456);
    });

    it('应该正确解析小时时间', async () => {
      const vttContent = `WEBVTT

01:30:45.123 --> 01:30:48.456
Test`;

      const result = await parser.parse(vttContent);
      
      // 1 小时 30 分 45.123 秒 = 90*60*1000 + 45*1000 + 123 = 5445123 毫秒
      expect(result[0].startTime).toBe(5445123);
      // 1 小时 30 分 48.456 秒 = 90*60*1000 + 48*1000 + 456 = 5448456 毫秒
      expect(result[0].endTime).toBe(5448456);
    });
  });

  describe('edge cases', () => {
    it('应该处理只有 WEBVTT 标题的文件', async () => {
      const vttContent = `WEBVTT`;
      
      const result = await parser.parse(vttContent);
      expect(result).toHaveLength(0);
    });

    it('应该处理 Windows 风格的换行符', async () => {
      const vttContent = `WEBVTT\r\n\r\n00:00:00.000 --> 00:00:03.000\r\nHello World`;
      
      const result = await parser.parse(vttContent);
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Hello World');
    });

    it('应该处理混合的换行符', async () => {
      const vttContent = `WEBVTT\n\r\n00:00:00.000 --> 00:00:03.000\r\nHello World\n`;
      
      const result = await parser.parse(vttContent);
      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Hello World');
    });
  });
});