/**
 * 字幕解析器工厂类
 * 
 * 统一管理所有字幕格式解析器，提供格式检测和解析功能
 * 支持的格式：VTT, SRT, YouTube JSON, ASS
 */

import { SubtitleFormat, StandardSubtitle, ValidationResult, ISubtitleParser } from '../types';
import { VTTParser } from './vtt-parser';
import { SRTParser } from './srt-parser';
import { YouTubeParser } from './youtube-parser';
import { ASSParser } from './ass-parser';

export class SubtitleParserFactory {
  private static parsers = new Map<SubtitleFormat, ISubtitleParser>([
    [SubtitleFormat.VTT, new VTTParser()],
    [SubtitleFormat.SRT, new SRTParser()],
    [SubtitleFormat.YOUTUBE_JSON, new YouTubeParser()],
    [SubtitleFormat.ASS, new ASSParser()]
  ]);

  /**
   * 解析字幕数据
   */
  static async parse(data: string, format: SubtitleFormat): Promise<StandardSubtitle[]> {
    const parser = this.parsers.get(format);
    
    if (!parser) {
      throw new Error(`不支持的字幕格式: ${format}`);
    }

    const startTime = performance.now();
    const result = await parser.parse(data);
    const duration = performance.now() - startTime;

    console.log(`🔧 [SubtitleParserFactory] ${format.toUpperCase()} 解析完成: ${result.length} 条字幕, ${duration.toFixed(2)}ms`);
    
    return result;
  }

  /**
   * 智能检测字幕格式
   */
  static detectFormat(data: string): SubtitleFormat {
    if (!data || typeof data !== 'string') {
      return SubtitleFormat.UNKNOWN;
    }

    const trimmedData = data.trim();
    
    if (trimmedData.length === 0) {
      return SubtitleFormat.UNKNOWN;
    }

    // 按检测优先级排序，提高检测准确性

    // 1. 检测 VTT 格式 - 特征最明显
    if (VTTParser.detectFormat(trimmedData)) {
      return SubtitleFormat.VTT;
    }
    
    // 2. 检测 ASS 格式 - 结构特征明显
    if (ASSParser.detectFormat(trimmedData)) {
      return SubtitleFormat.ASS;
    }

    // 3. 检测 YouTube JSON 格式 - JSON 结构检测
    if (YouTubeParser.detectFormat(trimmedData)) {
      return SubtitleFormat.YOUTUBE_JSON;
    }
    
    // 4. 检测 SRT 格式 - 最后检测，避免误判
    if (SRTParser.detectFormat(trimmedData)) {
      return SubtitleFormat.SRT;
    }
    
    console.warn('⚠️ [SubtitleParserFactory] 无法识别字幕格式，数据样本:', trimmedData.substring(0, 200));
    return SubtitleFormat.UNKNOWN;
  }

  /**
   * 自动解析 - 先检测格式再解析
   */
  static async autoParse(data: string): Promise<{ format: SubtitleFormat; subtitles: StandardSubtitle[] }> {
    const format = this.detectFormat(data);
    
    if (format === SubtitleFormat.UNKNOWN) {
      throw new Error('无法识别的字幕格式');
    }

    const subtitles = await this.parse(data, format);
    
    return { format, subtitles };
  }

  /**
   * 验证解析结果
   */
  static validate(subtitles: StandardSubtitle[]): ValidationResult {
    const errors: string[] = [];
    
    if (!subtitles || subtitles.length === 0) {
      errors.push('字幕数组为空');
      return { valid: false, errors };
    }

    for (let i = 0; i < subtitles.length; i++) {
      const subtitle = subtitles[i];
      
      if (!subtitle.id) {
        errors.push(`字幕 ${i} 缺少 ID`);
      }
      
      if (typeof subtitle.startTime !== 'number' || subtitle.startTime < 0) {
        errors.push(`字幕 ${i} 开始时间无效: ${subtitle.startTime}`);
      }
      
      if (typeof subtitle.endTime !== 'number' || subtitle.endTime <= subtitle.startTime) {
        errors.push(`字幕 ${i} 结束时间无效: ${subtitle.endTime} <= ${subtitle.startTime}`);
      }
      
      if (!subtitle.text || typeof subtitle.text !== 'string' || subtitle.text.trim().length === 0) {
        errors.push(`字幕 ${i} 文本内容为空`);
      }

      // 检查时间轴是否合理（不超过24小时）
      if (subtitle.endTime > 24 * 60 * 60 * 1000) {
        errors.push(`字幕 ${i} 时间轴异常: ${subtitle.endTime}ms`);
      }

      // 检查时间重叠（与下一个字幕）
      if (i < subtitles.length - 1) {
        const nextSubtitle = subtitles[i + 1];
        if (subtitle.endTime > nextSubtitle.startTime) {
          errors.push(`字幕 ${i} 与 ${i + 1} 时间重叠`);
        }
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 获取支持的格式列表
   */
  static getSupportedFormats(): SubtitleFormat[] {
    return Array.from(this.parsers.keys());
  }

  /**
   * 注册新的解析器
   */
  static registerParser(format: SubtitleFormat, parser: ISubtitleParser): void {
    this.parsers.set(format, parser);
    console.log(`📝 [SubtitleParserFactory] 注册解析器: ${format}`);
  }

  /**
   * 获取解析器统计信息
   */
  static getParserStats(): { format: SubtitleFormat; available: boolean }[] {
    return Array.from(this.parsers.entries()).map(([format, parser]) => ({
      format,
      available: parser !== null
    }));
  }

  /**
   * 批量解析多个字幕文件
   */
  static async parseMultiple(
    dataList: Array<{ data: string; format?: SubtitleFormat; id?: string }>
  ): Promise<Array<{ id?: string; format: SubtitleFormat; subtitles: StandardSubtitle[]; error?: string }>> {
    const results = await Promise.allSettled(
      dataList.map(async ({ data, format, id }) => {
        try {
          if (format) {
            const subtitles = await this.parse(data, format);
            return { id, format, subtitles };
          } else {
            const result = await this.autoParse(data);
            return { id, format: result.format, subtitles: result.subtitles };
          }
        } catch (error) {
          return {
            id,
            format: format || SubtitleFormat.UNKNOWN,
            subtitles: [],
            error: error instanceof Error ? error.message : String(error)
          };
        }
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          id: dataList[index].id,
          format: dataList[index].format || SubtitleFormat.UNKNOWN,
          subtitles: [],
          error: result.reason instanceof Error ? result.reason.message : String(result.reason)
        };
      }
    });
  }

  /**
   * 格式转换 - 将一种格式转换为另一种格式
   */
  static async convertFormat(
    data: string, 
    fromFormat: SubtitleFormat, 
    toFormat: SubtitleFormat
  ): Promise<string> {
    if (fromFormat === toFormat) {
      return data;
    }

    // 先解析原格式
    const subtitles = await this.parse(data, fromFormat);
    
    // 转换为目标格式
    return this.serialize(subtitles, toFormat);
  }

  /**
   * 将字幕数据序列化为指定格式
   */
  static serialize(subtitles: StandardSubtitle[], format: SubtitleFormat): string {
    switch (format) {
      case SubtitleFormat.VTT:
        return this.serializeToVTT(subtitles);
      
      case SubtitleFormat.SRT:
        return this.serializeToSRT(subtitles);
      
      default:
        throw new Error(`不支持序列化为格式: ${format}`);
    }
  }

  /**
   * 序列化为 VTT 格式
   */
  private static serializeToVTT(subtitles: StandardSubtitle[]): string {
    let content = 'WEBVTT\n\n';
    
    for (const subtitle of subtitles) {
      const startTime = this.msToVTTTime(subtitle.startTime);
      const endTime = this.msToVTTTime(subtitle.endTime);
      
      content += `${startTime} --> ${endTime}\n`;
      content += `${subtitle.text}\n\n`;
    }
    
    return content;
  }

  /**
   * 序列化为 SRT 格式
   */
  private static serializeToSRT(subtitles: StandardSubtitle[]): string {
    let content = '';
    
    for (let i = 0; i < subtitles.length; i++) {
      const subtitle = subtitles[i];
      const startTime = this.msToSRTTime(subtitle.startTime);
      const endTime = this.msToSRTTime(subtitle.endTime);
      
      content += `${i + 1}\n`;
      content += `${startTime} --> ${endTime}\n`;
      content += `${subtitle.text}\n\n`;
    }
    
    return content;
  }

  /**
   * 毫秒转 VTT 时间格式
   */
  private static msToVTTTime(ms: number): string {
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    const milliseconds = ms % 1000;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
  }

  /**
   * 毫秒转 SRT 时间格式
   */
  private static msToSRTTime(ms: number): string {
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    const milliseconds = ms % 1000;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
  }
}