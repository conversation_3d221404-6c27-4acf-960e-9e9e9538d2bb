/**
 * ASS (Advanced SubStation Alpha) 格式字幕解析器
 * 
 * 支持 ASS/SSA 格式的字幕文件解析
 * 这是一种功能丰富的字幕格式，支持复杂的样式和定位
 */

import { StandardSubtitle, SubtitleFormat, SubtitlePosition, SubtitleStyle } from '../types';
import { BaseSubtitleParser } from './base-parser';

/**
 * ASS 字幕事件结构
 */
interface ASSEvent {
  layer: number;
  start: string;
  end: string;
  style: string;
  name: string;
  marginL: number;
  marginR: number;
  marginV: number;
  effect: string;
  text: string;
}

/**
 * ASS 样式信息
 */
interface ASSStyle {
  name: string;
  fontname: string;
  fontsize: number;
  primarycolour: string;
  secondarycolour: string;
  tertiarycolour: string;
  backcolour: string;
  bold: boolean;
  italic: boolean;
  alignment: number;
  [key: string]: any;
}

export class ASSParser extends BaseSubtitleParser {
  private styles: Map<string, ASSStyle> = new Map();

  constructor() {
    super(SubtitleFormat.ASS);
  }

  /**
   * 解析 ASS 格式字幕
   */
  async parse(data: string): Promise<StandardSubtitle[]> {
    return this.safeParseWrapper(data, this.parseASS.bind(this));
  }

  /**
   * ASS 解析核心逻辑
   */
  private async parseASS(data: string): Promise<StandardSubtitle[]> {
    const lines = data.split('\n').map(line => line.trim());
    
    // 解析样式信息
    this.parseStyles(lines);
    
    // 解析事件信息
    const subtitles = this.parseEvents(lines);
    
    return this.postProcessSubtitles(subtitles);
  }

  /**
   * 解析样式部分
   */
  private parseStyles(lines: string[]): void {
    let inStylesSection = false;
    let styleFormat: string[] = [];

    for (const line of lines) {
      if (line === '[V4+ Styles]' || line === '[V4 Styles]') {
        inStylesSection = true;
        continue;
      }
      
      if (line.startsWith('[') && line.endsWith(']')) {
        inStylesSection = false;
        continue;
      }

      if (inStylesSection) {
        if (line.startsWith('Format:')) {
          styleFormat = line.substring(7).split(',').map(s => s.trim().toLowerCase());
        } else if (line.startsWith('Style:') && styleFormat.length > 0) {
          const styleData = line.substring(6).split(',');
          const style = this.parseStyleLine(styleData, styleFormat);
          if (style) {
            this.styles.set(style.name, style);
          }
        }
      }
    }
  }

  /**
   * 解析单个样式行
   */
  private parseStyleLine(data: string[], format: string[]): ASSStyle | null {
    try {
      const style: Partial<ASSStyle> = {};
      
      for (let i = 0; i < Math.min(data.length, format.length); i++) {
        const key = format[i];
        const value = data[i].trim();
        
        switch (key) {
          case 'name':
            style.name = value;
            break;
          case 'fontname':
            style.fontname = value;
            break;
          case 'fontsize':
            style.fontsize = parseInt(value, 10) || 16;
            break;
          case 'primarycolour':
            style.primarycolour = this.convertASSColor(value);
            break;
          case 'bold':
            style.bold = value === '1' || value === '-1';
            break;
          case 'italic':
            style.italic = value === '1' || value === '-1';
            break;
          case 'alignment':
            style.alignment = parseInt(value, 10) || 2;
            break;
        }
      }

      return style.name ? style as ASSStyle : null;
    } catch (error) {
      console.error('⚠️ [ASSParser] 样式解析失败:', error);
      return null;
    }
  }

  /**
   * 解析事件部分
   */
  private parseEvents(lines: string[]): StandardSubtitle[] {
    let inEventsSection = false;
    let eventFormat: string[] = [];
    const subtitles: StandardSubtitle[] = [];
    let currentIndex = 0;

    for (const line of lines) {
      if (line === '[Events]') {
        inEventsSection = true;
        continue;
      }
      
      if (line.startsWith('[') && line.endsWith(']')) {
        inEventsSection = false;
        continue;
      }

      if (inEventsSection) {
        if (line.startsWith('Format:')) {
          eventFormat = line.substring(7).split(',').map(s => s.trim().toLowerCase());
        } else if (line.startsWith('Dialogue:') && eventFormat.length > 0) {
          const eventData = line.substring(9).split(',');
          const subtitle = this.parseEventLine(eventData, eventFormat, currentIndex);
          if (subtitle) {
            subtitles.push(subtitle);
            currentIndex++;
          }
        }
      }
    }

    return subtitles;
  }

  /**
   * 解析单个事件行
   */
  private parseEventLine(data: string[], format: string[], index: number): StandardSubtitle | null {
    try {
      const event: Partial<ASSEvent> = {};
      
      // ASS 格式的 Text 字段可能包含逗号，需要特殊处理
      for (let i = 0; i < format.length; i++) {
        const key = format[i];
        let value: string;
        
        if (key === 'text') {
          // Text 字段是最后一个，包含剩余所有内容
          value = data.slice(i).join(',');
        } else {
          value = data[i]?.trim() || '';
        }
        
        switch (key) {
          case 'start':
            event.start = value;
            break;
          case 'end':
            event.end = value;
            break;
          case 'style':
            event.style = value;
            break;
          case 'text':
            event.text = value;
            break;
        }
      }

      if (event.start && event.end && event.text) {
        const startTime = this.parseASSTime(event.start);
        const endTime = this.parseASSTime(event.end);
        const text = this.cleanASSText(event.text);

        if (startTime >= 0 && endTime > startTime && text) {
          const subtitle: StandardSubtitle = {
            id: this.generateId(index, startTime),
            startTime: startTime,
            endTime: endTime,
            text: text
          };

          // 应用样式信息
          if (event.style) {
            const style = this.styles.get(event.style);
            if (style) {
              subtitle.style = this.convertASSStyleToSubtitleStyle(style);
              subtitle.position = this.convertASSAlignmentToPosition(style.alignment);
            }
          }

          return subtitle;
        }
      }

      return null;
    } catch (error) {
      console.error('⚠️ [ASSParser] 事件解析失败:', error);
      return null;
    }
  }

  /**
   * 解析 ASS 时间格式 (H:MM:SS.CC)
   */
  private parseASSTime(timeStr: string): number {
    try {
      const match = timeStr.match(/(\d+):(\d{2}):(\d{2})\.(\d{2})/);
      if (match) {
        const [, hours, minutes, seconds, centiseconds] = match;
        return (
          parseInt(hours) * 3600000 +
          parseInt(minutes) * 60000 +
          parseInt(seconds) * 1000 +
          parseInt(centiseconds) * 10
        );
      }
      return -1;
    } catch (error) {
      console.error('⚠️ [ASSParser] 时间解析失败:', timeStr, error);
      return -1;
    }
  }

  /**
   * 清理 ASS 文本，移除格式标记
   */
  private cleanASSText(text: string): string {
    if (!text) return '';
    
    return text
      .replace(/\\[nN]/g, ' ')        // 换行符
      .replace(/\\h/g, ' ')           // 硬空格
      .replace(/{[^}]*}/g, '')        // 覆盖标签 {\\tag}
      .replace(/\\[a-zA-Z]+/g, '')    // 其他转义序列
      .trim();
  }

  /**
   * 转换 ASS 颜色格式
   */
  private convertASSColor(color: string): string {
    try {
      // ASS 颜色格式: &Hbbggrr (BGR) 或 &Haabbggrr (ABGR)
      if (color.startsWith('&H')) {
        const hex = color.substring(2);
        if (hex.length === 6) {
          // BGR -> RGB
          const r = hex.substring(4, 6);
          const g = hex.substring(2, 4);
          const b = hex.substring(0, 2);
          return `#${r}${g}${b}`;
        }
      }
      
      return '#ffffff'; // 默认白色
    } catch (error) {
      return '#ffffff';
    }
  }

  /**
   * 将 ASS 样式转换为标准字幕样式
   */
  private convertASSStyleToSubtitleStyle(assStyle: ASSStyle): SubtitleStyle {
    return {
      fontSize: `${assStyle.fontsize}px`,
      fontFamily: assStyle.fontname || 'Arial, sans-serif',
      color: assStyle.primarycolour || '#ffffff',
      fontWeight: assStyle.bold ? 'bold' : 'normal'
    };
  }

  /**
   * 将 ASS 对齐方式转换为位置信息
   */
  private convertASSAlignmentToPosition(alignment: number): SubtitlePosition {
    // ASS 对齐：1-3 底部，4-6 中部，7-9 顶部
    // 1,4,7 左对齐，2,5,8 居中，3,6,9 右对齐
    const vertical = alignment <= 3 ? 'bottom' : alignment <= 6 ? 'middle' : 'top';
    const horizontal = alignment % 3 === 1 ? 'left' : alignment % 3 === 0 ? 'right' : 'center';
    
    return {
      x: horizontal === 'left' ? 10 : horizontal === 'right' ? 90 : 50,
      y: vertical === 'top' ? 10 : vertical === 'bottom' ? 90 : 50,
      align: horizontal,
      vertical: vertical
    };
  }

  /**
   * 检测是否为 ASS 格式
   */
  static detectFormat(data: string): boolean {
    const trimmedData = data.trim();
    return trimmedData.includes('[Script Info]') && 
           (trimmedData.includes('[V4+ Styles]') || trimmedData.includes('[V4 Styles]')) &&
           trimmedData.includes('[Events]');
  }
}