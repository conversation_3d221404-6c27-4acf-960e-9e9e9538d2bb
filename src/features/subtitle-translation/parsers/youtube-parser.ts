/**
 * YouTube JSON 格式字幕解析器
 * 
 * 专门处理 YouTube 字幕 API 返回的 JSON 格式数据
 * 支持自动生成字幕和手动字幕两种格式
 */

import { StandardSubtitle, SubtitleFormat } from '../types';
import { BaseSubtitleParser } from './base-parser';

/**
 * YouTube 字幕 JSON 数据结构
 */
interface YouTubeSubtitleEvent {
  tStartMs?: number;     // 开始时间（毫秒）
  dDurationMs?: number;  // 持续时间（毫秒）
  aAppend?: number;      // 是否追加到前一个事件
  segs?: Array<{        // 文本片段
    utf8: string;
    tOffsetMs?: number;  // 偏移时间
  }>;
}

interface YouTubeSubtitleData {
  events?: YouTubeSubtitleEvent[];
  wpWinPositions?: any[];
  [key: string]: any;
}

export class YouTubeParser extends BaseSubtitleParser {
  constructor() {
    super(SubtitleFormat.YOUTUBE_JSON);
  }

  /**
   * 解析 YouTube JSON 格式字幕
   */
  async parse(data: string): Promise<StandardSubtitle[]> {
    return this.safeParseWrapper(data, this.parseYouTubeJSON.bind(this));
  }

  /**
   * YouTube JSON 解析核心逻辑
   */
  private async parseYouTubeJSON(data: string): Promise<StandardSubtitle[]> {
    let jsonData: YouTubeSubtitleData;
    
    try {
      jsonData = JSON.parse(data);
    } catch (error) {
      throw new Error(`无效的 JSON 格式: ${error instanceof Error ? error.message : String(error)}`);
    }

    if (!jsonData.events || !Array.isArray(jsonData.events)) {
      throw new Error('YouTube 字幕数据缺少 events 字段');
    }

    const subtitles: StandardSubtitle[] = [];
    let currentIndex = 0;

    for (const event of jsonData.events) {
      try {
        const parsedSubtitle = this.parseYouTubeEvent(event, currentIndex);
        if (parsedSubtitle) {
          subtitles.push(parsedSubtitle);
          currentIndex++;
        }
      } catch (error) {
        console.warn(`⚠️ [YouTubeParser] 跳过无效事件 ${currentIndex}:`, error);
        continue;
      }
    }

    return this.postProcessSubtitles(subtitles);
  }

  /**
   * 解析单个 YouTube 字幕事件
   */
  private parseYouTubeEvent(event: YouTubeSubtitleEvent, index: number): StandardSubtitle | null {
    // 检查必需字段
    if (typeof event.tStartMs !== 'number' || typeof event.dDurationMs !== 'number') {
      return null;
    }

    // 提取文本内容
    let text = '';
    if (event.segs && Array.isArray(event.segs)) {
      text = event.segs
        .map(seg => seg.utf8 || '')
        .join('')
        .trim();
    }

    if (!text) {
      return null;
    }

    // 清理文本
    const cleanedText = this.cleanText(text);
    if (!cleanedText) {
      return null;
    }

    const startTime = event.tStartMs;
    const endTime = startTime + event.dDurationMs;

    return {
      id: this.generateId(index, startTime),
      startTime: startTime,
      endTime: endTime,
      text: cleanedText
    };
  }

  /**
   * 处理 YouTube 特殊的追加模式
   * YouTube 有时会将长字幕分割成多个事件，需要合并
   */
  private mergeAppendedEvents(subtitles: StandardSubtitle[]): StandardSubtitle[] {
    const merged: StandardSubtitle[] = [];
    let currentGroup: StandardSubtitle[] = [];

    for (const subtitle of subtitles) {
      // 简单的时间连续性检查
      const lastSubtitle = currentGroup[currentGroup.length - 1];
      
      if (lastSubtitle && 
          subtitle.startTime <= lastSubtitle.endTime + 100 && // 100ms 容忍度
          this.shouldMergeText(lastSubtitle.text, subtitle.text)) {
        
        // 合并到当前组
        currentGroup.push(subtitle);
      } else {
        // 完成当前组的合并
        if (currentGroup.length > 0) {
          merged.push(this.mergeTextGroup(currentGroup));
        }
        
        // 开始新组
        currentGroup = [subtitle];
      }
    }

    // 处理最后一组
    if (currentGroup.length > 0) {
      merged.push(this.mergeTextGroup(currentGroup));
    }

    return merged;
  }

  /**
   * 判断是否应该合并文本
   */
  private shouldMergeText(text1: string, text2: string): boolean {
    // 如果第二个文本包含第一个文本的结尾，可能是追加模式
    const words1 = text1.split(' ');
    const words2 = text2.split(' ');
    
    if (words1.length > 0 && words2.length > 0) {
      const lastWord1 = words1[words1.length - 1];
      const firstWord2 = words2[0];
      
      // 检查是否有词汇重叠
      return lastWord1 === firstWord2 || text2.includes(text1.slice(-10));
    }
    
    return false;
  }

  /**
   * 合并文本组
   */
  private mergeTextGroup(group: StandardSubtitle[]): StandardSubtitle {
    if (group.length === 1) {
      return group[0];
    }

    const first = group[0];
    const last = group[group.length - 1];
    
    // 智能合并文本，避免重复
    let mergedText = first.text;
    
    for (let i = 1; i < group.length; i++) {
      const currentText = group[i].text;
      
      // 检查重叠并合并
      if (!mergedText.includes(currentText.slice(0, 10))) {
        mergedText += ' ' + currentText;
      }
    }

    return {
      id: first.id,
      startTime: first.startTime,
      endTime: last.endTime,
      text: this.cleanText(mergedText)
    };
  }

  /**
   * 检测是否为 YouTube JSON 格式
   */
  static detectFormat(data: string): boolean {
    try {
      const parsed = JSON.parse(data.trim());
      return parsed.events && Array.isArray(parsed.events) && 
             parsed.events.some((event: any) => 
               typeof event.tStartMs === 'number' && 
               typeof event.dDurationMs === 'number'
             );
    } catch {
      return false;
    }
  }

  /**
   * 转换为标准 VTT 格式
   */
  async toVTT(data: string): Promise<string> {
    try {
      const subtitles = await this.parseYouTubeJSON(data);
      let vttContent = 'WEBVTT\n\n';
      
      for (const subtitle of subtitles) {
        const startTime = this.msToTime(subtitle.startTime);
        const endTime = this.msToTime(subtitle.endTime);
        
        vttContent += `${startTime} --> ${endTime}\n`;
        vttContent += `${subtitle.text}\n\n`;
      }
      
      return vttContent;
    } catch (error) {
      console.error('⚠️ [YouTubeParser] 转换 VTT 失败:', error);
      return '';
    }
  }
}