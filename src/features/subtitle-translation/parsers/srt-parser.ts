/**
 * SRT (SubRip) 格式字幕解析器
 * 
 * 支持标准 SRT 格式的字幕文件解析
 * 格式特点：序号 + 时间轴 + 文本内容
 */

import { StandardSubtitle, SubtitleFormat } from '../types';
import { BaseSubtitleParser } from './base-parser';
import { StandardTimeParser, TIME_FORMAT_STANDARDS } from './time-parser';

export class SRTParser extends BaseSubtitleParser {
  constructor() {
    super(SubtitleFormat.SRT);
  }

  /**
   * 解析 SRT 格式字幕
   */
  async parse(data: string): Promise<StandardSubtitle[]> {
    return this.safeParseWrapper(data, this.parseSRT.bind(this));
  }

  /**
   * SRT 解析核心逻辑 - 简化版本
   * 回归简洁的块分割方法，但保持错误处理的健壮性
   */
  private async parseSRT(data: string): Promise<StandardSubtitle[]> {
    const subtitles: StandardSubtitle[] = [];
    
    // 标准化换行符并按双换行符分割字幕块
    const normalizedData = data.replace(/\r\n?/g, '\n');
    const blocks = normalizedData.split(/\n\s*\n/).filter(block => block.trim());
    
    for (const [blockIndex, block] of blocks.entries()) {
      try {
        const subtitle = this.parseSubtitleBlock(block, blockIndex);
        if (subtitle) {
          subtitles.push(subtitle);
        }
      } catch (error) {
        // 具体的错误处理，而不是静默跳过
        console.warn(`⚠️ [SRTParser] 字幕块 ${blockIndex + 1} 解析失败:`, error);
        // 继续处理其他块，但记录错误
        continue;
      }
    }
    
    return this.postProcessSubtitles(subtitles);
  }

  /**
   * 解析单个字幕块
   * @param block 字幕块文本
   * @param blockIndex 块索引（用于错误报告）
   * @returns 解析后的字幕对象，解析失败返回null
   */
  private parseSubtitleBlock(block: string, blockIndex: number): StandardSubtitle | null {
    const lines = block.split('\n').map(line => line.trim()).filter(line => line);
    
    if (lines.length < 3) {
      throw new Error(`字幕块格式不完整，至少需要3行（序号、时间轴、文本），实际 ${lines.length} 行`);
    }

    // 第一行：序号
    const sequenceNumber = lines[0];
    if (!/^\d+$/.test(sequenceNumber)) {
      throw new Error(`序号格式错误: "${sequenceNumber}"，应为纯数字`);
    }

    // 第二行：时间轴
    const timing = this.parseTimingLine(lines[1]);
    if (!timing) {
      throw new Error(`时间轴格式错误: "${lines[1]}"`);
    }

    // 第三行及以后：字幕文本（可能多行）
    const textLines = lines.slice(2);
    const text = this.cleanText(textLines.join(' '));
    
    if (!text) {
      throw new Error('字幕文本为空');
    }

    return {
      id: sequenceNumber,
      startTime: timing.startTime,
      endTime: timing.endTime,
      text: text
    };
  }

  /**
   * 解析 SRT 时间轴行
   * 格式: 00:00:01,000 --> 00:00:04,000
   * 严格要求SRT标准格式：HH:MM:SS,mmm（3位毫秒）
   */
  private parseTimingLine(line: string): { startTime: number; endTime: number } | null {
    try {
      // SRT 严格格式：HH:MM:SS,mmm --> HH:MM:SS,mmm
      const match = line.match(/^(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})$/);
      
      if (!match) {
        throw new Error(`时间轴格式不符合SRT标准: "${line}"`);
      }

      const [, startTimeStr, endTimeStr] = match;
      
      const startTime = StandardTimeParser.parseToMs(startTimeStr, [TIME_FORMAT_STANDARDS.find(f => f.name === 'SRT_FLEXIBLE')!]);
      const endTime = StandardTimeParser.parseToMs(endTimeStr, [TIME_FORMAT_STANDARDS.find(f => f.name === 'SRT_FLEXIBLE')!]);
      
      if (endTime <= startTime) {
        throw new Error(`结束时间必须大于开始时间: ${startTimeStr} --> ${endTimeStr}`);
      }
      
      return { startTime, endTime };
      
    } catch (error) {
      console.error('⚠️ [SRTParser] 时间轴解析失败:', line, error);
      return null;
    }
  }

  /**
   * 检测是否为 SRT 格式
   */
  static detectFormat(data: string): boolean {
    const trimmedData = data.trim();
    
    // SRT 格式特征：以数字开头，第二行包含时间轴
    const lines = trimmedData.split('\n').map(line => line.trim()).filter(line => line);
    
    if (lines.length < 3) return false;
    
    // 检查第一行是否为数字
    if (!/^\d+$/.test(lines[0])) return false;
    
    // 检查第二行是否为 SRT 时间格式
    return /^\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}/.test(lines[1]);
  }

  /**
   * 将 SRT 格式转换为 VTT 格式（工具方法）
   */
  toVTT(data: string): string {
    try {
      const subtitles = this.parseSRT(data);
      let vttContent = 'WEBVTT\n\n';
      
      subtitles.then(subs => {
        for (const subtitle of subs) {
          const startTime = this.msToTime(subtitle.startTime);
          const endTime = this.msToTime(subtitle.endTime);
          
          vttContent += `${startTime} --> ${endTime}\n`;
          vttContent += `${subtitle.text}\n\n`;
        }
      });
      
      return vttContent;
    } catch (error) {
      console.error('⚠️ [SRTParser] SRT 转 VTT 失败:', error);
      return '';
    }
  }
}