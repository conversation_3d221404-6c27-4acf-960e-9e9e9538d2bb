import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { SubtitleTranslationManager } from '../translation/translation-manager';
import { StandardSubtitle, SubtitleTranslationConfig } from '../types';

// Mock TranslateService
vi.mock('@features/translate', () => ({
  translateService: {
    translateText: vi.fn(),
    translateTexts: vi.fn()
  }
}));

describe('SubtitleTranslationManager', () => {
  let manager: SubtitleTranslationManager;
  let config: SubtitleTranslationConfig;
  let mockTranslateService: any;

  beforeEach(async () => {
    // 设置测试配置
    config = {
      enabled: true,
      sourceLang: 'en',
      targetLang: 'zh-CN',
      showOriginal: true,
      showTranslated: true,
      batchSize: 5,
      maxConcurrency: 2,
      cacheEnabled: true,
      retryCount: 1
    };

    // 获取 mock 的 translateService
    const { translateService } = await import('@features/translate');
    mockTranslateService = translateService;

    // 创建管理器实例
    manager = new SubtitleTranslationManager(config);
    await manager.initialize();
  });

  afterEach(() => {
    manager.destroy();
    vi.clearAllMocks();
  });

  describe('初始化', () => {
    test('应该成功初始化翻译管理器', async () => {
      const newManager = new SubtitleTranslationManager(config);
      await expect(newManager.initialize()).resolves.not.toThrow();
      newManager.destroy();
    });

    test('应该获取正确的配置', () => {
      const managerConfig = manager.getConfig();
      expect(managerConfig).toEqual(config);
    });
  });

  describe('字幕翻译', () => {
    const mockSubtitles: StandardSubtitle[] = [
      {
        id: '1',
        startTime: 1000,
        endTime: 3000,
        text: 'Hello world'
      },
      {
        id: '2',
        startTime: 3000,
        endTime: 5000,
        text: 'How are you?'
      },
      {
        id: '3',
        startTime: 5000,
        endTime: 7000,
        text: 'Nice to meet you'
      }
    ];

    test('应该成功翻译字幕列表（批量模式）', async () => {
      // Mock 批量翻译返回
      mockTranslateService.translateTexts.mockResolvedValue([
        '你好世界',
        '你好吗？',
        '很高兴见到你'
      ]);

      const result = await manager.translateSubtitles(mockSubtitles, {
        sourceLang: 'en',
        targetLang: 'zh-CN'
      });

      expect(result).toHaveLength(3);
      expect(result[0].translatedText).toBe('你好世界');
      expect(result[1].translatedText).toBe('你好吗？');
      expect(result[2].translatedText).toBe('很高兴见到你');

      // 验证批量翻译被调用
      expect(mockTranslateService.translateTexts).toHaveBeenCalledWith(
        ['Hello world', 'How are you?', 'Nice to meet you'],
        expect.objectContaining({
          from: 'en',
          to: 'zh-CN'
        })
      );
    });

    test('应该成功翻译字幕列表（单条模式）', async () => {
      // 设置单条翻译模式
      manager.updateConfig({ ...config, batchSize: 1 });

      // Mock 单条翻译返回
      mockTranslateService.translateText
        .mockResolvedValueOnce('你好世界')
        .mockResolvedValueOnce('你好吗？')
        .mockResolvedValueOnce('很高兴见到你');

      const result = await manager.translateSubtitles(mockSubtitles, {
        sourceLang: 'en',
        targetLang: 'zh-CN'
      });

      expect(result).toHaveLength(3);
      expect(result[0].translatedText).toBe('你好世界');
      expect(result[1].translatedText).toBe('你好吗？');
      expect(result[2].translatedText).toBe('很高兴见到你');

      // 验证单条翻译被调用3次
      expect(mockTranslateService.translateText).toHaveBeenCalledTimes(3);
    });

    test('应该处理空字幕列表', async () => {
      const result = await manager.translateSubtitles([]);
      expect(result).toEqual([]);
      expect(mockTranslateService.translateTexts).not.toHaveBeenCalled();
    });

    test('应该处理翻译失败的情况', async () => {
      // Mock 翻译失败
      mockTranslateService.translateTexts.mockRejectedValue(new Error('翻译服务不可用'));

      await expect(
        manager.translateSubtitles(mockSubtitles, {
          sourceLang: 'en',
          targetLang: 'zh-CN'
        })
      ).rejects.toThrow('字幕翻译失败');
    });

    test('应该处理部分翻译失败的情况', async () => {
      // 设置批量模式，第一次批量失败，然后单条补偿
      mockTranslateService.translateTexts.mockRejectedValue(new Error('批量翻译失败'));
      mockTranslateService.translateText
        .mockResolvedValueOnce('你好世界')
        .mockRejectedValueOnce(new Error('单条翻译失败'))
        .mockResolvedValueOnce('很高兴见到你');

      const result = await manager.translateSubtitles(mockSubtitles, {
        sourceLang: 'en',
        targetLang: 'zh-CN'
      });

      expect(result).toHaveLength(3);
      expect(result[0].translatedText).toBe('你好世界');
      expect(result[1].translatedText).toBe(''); // 翻译失败，返回空字符串
      expect(result[2].translatedText).toBe('很高兴见到你');
    });
  });

  describe('翻译进度管理', () => {
    test('应该跟踪翻译进度', async () => {
      const mockSubtitles: StandardSubtitle[] = [
        { id: '1', startTime: 1000, endTime: 3000, text: 'Hello' },
        { id: '2', startTime: 3000, endTime: 5000, text: 'World' }
      ];

      mockTranslateService.translateTexts.mockResolvedValue(['你好', '世界']);

      // 开始翻译（异步）
      const translatePromise = manager.translateSubtitles(mockSubtitles);

      // 获取所有进度
      const allProgress = manager.getTranslationProgress() as any[];
      expect(allProgress).toHaveLength(1);
      expect(allProgress[0].status).toBe('processing');
      expect(allProgress[0].total).toBe(2);

      // 等待翻译完成
      await translatePromise;

      // 检查完成状态
      const completedProgress = manager.getTranslationProgress() as any[];
      expect(completedProgress[0].status).toBe('completed');
      expect(completedProgress[0].completed).toBe(2);
    });

    test('应该支持取消翻译任务', async () => {
      const mockSubtitles: StandardSubtitle[] = [
        { id: '1', startTime: 1000, endTime: 3000, text: 'Hello' }
      ];

      // 模拟慢翻译
      mockTranslateService.translateTexts.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(['你好']), 1000))
      );

      // 开始翻译
      const translatePromise = manager.translateSubtitles(mockSubtitles);

      // 获取任务ID并取消
      const progress = manager.getTranslationProgress() as any[];
      const taskId = progress[0].taskId;
      manager.cancelTranslation(taskId);

      // 检查取消状态
      const cancelledProgress = manager.getTranslationProgress(taskId) as any;
      expect(cancelledProgress.status).toBe('cancelled');

      // 等待翻译完成（虽然已取消）
      await expect(translatePromise).resolves.toBeDefined();
    });
  });

  describe('配置管理', () => {
    test('应该支持更新配置', () => {
      const newConfig = {
        ...config,
        batchSize: 10,
        targetLang: 'ja'
      };

      manager.updateConfig(newConfig);
      const updatedConfig = manager.getConfig();

      expect(updatedConfig.batchSize).toBe(10);
      expect(updatedConfig.targetLang).toBe('ja');
    });

    test('应该保持其他配置项不变', () => {
      manager.updateConfig({ batchSize: 15 });
      const updatedConfig = manager.getConfig();

      expect(updatedConfig.batchSize).toBe(15);
      expect(updatedConfig.sourceLang).toBe(config.sourceLang);
      expect(updatedConfig.targetLang).toBe(config.targetLang);
    });
  });

  describe('错误处理', () => {
    test('应该正确处理网络错误', async () => {
      mockTranslateService.translateTexts.mockRejectedValue(
        new Error('网络连接失败')
      );

      const mockSubtitles: StandardSubtitle[] = [
        { id: '1', startTime: 1000, endTime: 3000, text: 'Hello' }
      ];

      await expect(
        manager.translateSubtitles(mockSubtitles)
      ).rejects.toThrow('字幕翻译失败: 网络连接失败');
    });

    test('应该正确处理服务器错误', async () => {
      mockTranslateService.translateTexts.mockRejectedValue(
        new Error('服务器内部错误')
      );

      const mockSubtitles: StandardSubtitle[] = [
        { id: '1', startTime: 1000, endTime: 3000, text: 'Hello' }
      ];

      await expect(
        manager.translateSubtitles(mockSubtitles)
      ).rejects.toThrow('字幕翻译失败: 服务器内部错误');
    });
  });

  describe('性能测试', () => {
    test('批量翻译应该比单条翻译更高效', async () => {
      const largeSubtitles: StandardSubtitle[] = Array.from({ length: 20 }, (_, i) => ({
        id: String(i + 1),
        startTime: i * 2000,
        endTime: (i + 1) * 2000,
        text: `Test subtitle ${i + 1}`
      }));

      // Mock 批量翻译响应
      mockTranslateService.translateTexts.mockResolvedValue(
        largeSubtitles.map(s => `翻译: ${s.text}`)
      );

      const startTime = Date.now();
      await manager.translateSubtitles(largeSubtitles);
      const duration = Date.now() - startTime;

      // 批量翻译应该在合理时间内完成
      expect(duration).toBeLessThan(1000); // 1秒内

      // 验证使用了批量翻译接口
      expect(mockTranslateService.translateTexts).toHaveBeenCalled();
    });
  });
});