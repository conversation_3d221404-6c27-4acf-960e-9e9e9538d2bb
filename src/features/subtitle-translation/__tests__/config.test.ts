/**
 * 字幕翻译配置管理器单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SubtitleConfigManager, DEFAULT_SUBTITLE_CONFIG, SUPPORTED_LANGUAGES } from '../config';
import { SupportedPlatform } from '../types';
import { MockStorageAdapter } from '@services/browser-storage.service';

describe('SubtitleConfigManager', () => {
  let mockStorageAdapter: MockStorageAdapter;

  beforeEach(() => {
    // 创建新的模拟存储适配器
    mockStorageAdapter = new MockStorageAdapter();
    
    // 为配置管理器设置模拟存储适配器
    SubtitleConfigManager.setStorageAdapter(mockStorageAdapter);
  });

  afterEach(() => {
    // 清理存储适配器
    mockStorageAdapter.clear();
  });

  describe('loadConfig', () => {
    it('应该从存储适配器加载配置', async () => {
      const storedConfig = {
        ...DEFAULT_SUBTITLE_CONFIG,
        enabled: false,
        targetLang: 'en',
        _meta: {
          version: '1.0.0',
          timestamp: Date.now()
        }
      };

      await mockStorageAdapter.set('lucid-subtitle-translation-config', storedConfig);

      const config = await SubtitleConfigManager.loadConfig();

      expect(config.enabled).toBe(false);
      expect(config.targetLang).toBe('en');
    });

    it('应该返回默认配置当没有存储数据时', async () => {
      const config = await SubtitleConfigManager.loadConfig();

      expect(config).toEqual(DEFAULT_SUBTITLE_CONFIG);
    });

    it('应该处理存储读取错误', async () => {
      // 创建一个会抛出错误的存储适配器
      const errorStorageAdapter = {
        get: vi.fn().mockRejectedValue(new Error('Storage error')),
        set: vi.fn(),
        remove: vi.fn(),
        clear: vi.fn()
      };

      SubtitleConfigManager.setStorageAdapter(errorStorageAdapter);
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const config = await SubtitleConfigManager.loadConfig();

      expect(config).toEqual(DEFAULT_SUBTITLE_CONFIG);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('❌ [SubtitleConfigManager] 加载配置失败:'),
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });

    it('应该迁移旧版本配置', async () => {
      const oldConfig = {
        enabled: false,
        sourceLang: 'en',
        targetLang: 'zh-CN'
        // 缺少新版本的字段和_meta
      };

      await mockStorageAdapter.set('lucid-subtitle-translation-config', oldConfig);

      const config = await SubtitleConfigManager.loadConfig();

      // 应该包含旧配置的值
      expect(config.enabled).toBe(false);
      expect(config.sourceLang).toBe('en');
      expect(config.targetLang).toBe('zh-CN');
      
      // 应该包含默认值的新字段
      expect(config.batchSize).toBeDefined();
      expect(config.maxConcurrency).toBeDefined();
      expect(config.display).toBeDefined();
      expect(config.platforms).toBeDefined();
    });
  });

  describe('saveConfig', () => {
    it('应该保存配置到存储适配器', async () => {
      const config = {
        ...DEFAULT_SUBTITLE_CONFIG,
        enabled: false,
        targetLang: 'ja'
      };

      await SubtitleConfigManager.saveConfig(config);

      const stored = await mockStorageAdapter.get('lucid-subtitle-translation-config');
      expect(stored).toEqual(expect.objectContaining({
        ...config,
        _meta: expect.objectContaining({
          version: '1.0.0',
          timestamp: expect.any(Number)
        })
      }));
    });

    it('应该验证配置并修复无效值', async () => {
      const invalidConfig = {
        ...DEFAULT_SUBTITLE_CONFIG,
        batchSize: -5,  // 无效值
        maxConcurrency: 999,  // 超出范围
        sourceLang: 'invalid-lang'  // 无效格式
      };

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      await SubtitleConfigManager.saveConfig(invalidConfig);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚠️ [SubtitleConfigManager] 配置验证失败:'),
        expect.any(Array)
      );
      
      consoleSpy.mockRestore();
    });

    it('应该处理保存错误', async () => {
      const errorStorageAdapter = {
        get: vi.fn(),
        set: vi.fn().mockRejectedValue(new Error('Save error')),
        remove: vi.fn(),
        clear: vi.fn()
      };

      SubtitleConfigManager.setStorageAdapter(errorStorageAdapter);
      
      const config = DEFAULT_SUBTITLE_CONFIG;

      await expect(SubtitleConfigManager.saveConfig(config)).rejects.toThrow('Save error');
    });
  });

  describe('validateConfig', () => {
    it('应该验证有效配置', () => {
      const result = SubtitleConfigManager.validateConfig(DEFAULT_SUBTITLE_CONFIG);
      
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('应该检测无效的语言代码格式', () => {
      const invalidConfig = {
        ...DEFAULT_SUBTITLE_CONFIG,
        sourceLang: 'invalid-format',
        targetLang: 'also-invalid'
      };

      const result = SubtitleConfigManager.validateConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'sourceLang',
            message: expect.stringContaining('源语言格式无效')
          }),
          expect.objectContaining({
            field: 'targetLang',
            message: expect.stringContaining('目标语言格式无效')
          })
        ])
      );
    });

    it('应该检测数值范围错误', () => {
      const invalidConfig = {
        ...DEFAULT_SUBTITLE_CONFIG,
        batchSize: -1,
        maxConcurrency: 999,
        retryCount: 10
      };

      const result = SubtitleConfigManager.validateConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'batchSize',
            message: expect.stringContaining('批处理大小应在 1-50 之间')
          }),
          expect.objectContaining({
            field: 'maxConcurrency',
            message: expect.stringContaining('最大并发数应在 1-10 之间')
          }),
          expect.objectContaining({
            field: 'retryCount',
            message: expect.stringContaining('重试次数应在 0-5 之间')
          })
        ])
      );
    });

    it('应该检测显示配置错误', () => {
      const invalidConfig = {
        ...DEFAULT_SUBTITLE_CONFIG,
        display: {
          ...DEFAULT_SUBTITLE_CONFIG.display!,
          position: {
            ...DEFAULT_SUBTITLE_CONFIG.display!.position,
            x: -10,  // 无效值
            y: 150   // 超出范围
          },
          fadeInDuration: 5000,  // 超出范围
          zIndex: 0  // 无效值
        }
      };

      const result = SubtitleConfigManager.validateConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('应该允许 sourceLang 为 auto', () => {
      const config = {
        ...DEFAULT_SUBTITLE_CONFIG,
        sourceLang: 'auto'
      };

      const result = SubtitleConfigManager.validateConfig(config);

      expect(result.valid).toBe(true);
    });
  });

  describe('utility methods', () => {
    it('应该重置为默认配置', async () => {
      const config = await SubtitleConfigManager.resetToDefault();

      expect(config).toEqual(DEFAULT_SUBTITLE_CONFIG);
      
      // 验证是否保存到存储适配器
      const stored = await mockStorageAdapter.get('lucid-subtitle-translation-config');
      expect(stored).toEqual(expect.objectContaining({
        ...DEFAULT_SUBTITLE_CONFIG,
        _meta: expect.objectContaining({
          version: '1.0.0',
          timestamp: expect.any(Number)
        })
      }));
    });

    it('应该返回支持的语言列表', () => {
      const languages = SubtitleConfigManager.getSupportedLanguages();

      expect(languages).toEqual(SUPPORTED_LANGUAGES);
      expect(languages['zh-CN']).toBe('中文（简体）');
      expect(languages['en']).toBe('English');
      expect(languages['auto']).toBe('自动检测');
    });

    it('应该获取语言显示名称', () => {
      expect(SubtitleConfigManager.getLanguageDisplayName('zh-CN')).toBe('中文（简体）');
      expect(SubtitleConfigManager.getLanguageDisplayName('en')).toBe('English');
      expect(SubtitleConfigManager.getLanguageDisplayName('unknown')).toBe('unknown');
    });
  });

  describe('configuration migration', () => {
    it('应该识别需要迁移的旧配置', async () => {
      const oldConfigWithoutMeta = {
        enabled: true,
        sourceLang: 'auto',
        targetLang: 'zh-CN'
      };

      await mockStorageAdapter.set('lucid-subtitle-translation-config', oldConfigWithoutMeta);

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const config = await SubtitleConfigManager.loadConfig();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('🔄 [SubtitleConfigManager] 迁移旧版本配置')
      );
      
      consoleSpy.mockRestore();
    });

    it('应该保留新版本配置不变', async () => {
      const newConfigWithMeta = {
        ...DEFAULT_SUBTITLE_CONFIG,
        _meta: {
          version: '1.0.0',
          timestamp: Date.now()
        }
      };

      await mockStorageAdapter.set('lucid-subtitle-translation-config', newConfigWithMeta);

      const config = await SubtitleConfigManager.loadConfig();

      expect(config.enabled).toBe(DEFAULT_SUBTITLE_CONFIG.enabled);
      expect(config.targetLang).toBe(DEFAULT_SUBTITLE_CONFIG.targetLang);
    });
  });

  describe('deep merge functionality', () => {
    it('应该深度合并嵌套配置', async () => {
      const partialConfig = {
        enabled: false,
        display: {
          position: {
            x: 25  // 只修改 x 值
          },
          style: {
            fontSize: '20px'  // 只修改字体大小
          }
        }
      };

      await mockStorageAdapter.set('lucid-subtitle-translation-config', partialConfig);

      const config = await SubtitleConfigManager.loadConfig();

      // 应该保留修改的值
      expect(config.enabled).toBe(false);
      expect(config.display!.position.x).toBe(25);
      expect(config.display!.style.fontSize).toBe('20px');

      // 应该保留默认值
      expect(config.display!.position.y).toBe(DEFAULT_SUBTITLE_CONFIG.display!.position.y);
      expect(config.display!.style.color).toBe(DEFAULT_SUBTITLE_CONFIG.display!.style.color);
      expect(config.batchSize).toBe(DEFAULT_SUBTITLE_CONFIG.batchSize);
    });
  });

  describe('error handling and edge cases', () => {
    it('应该处理存储服务异常', async () => {
      const errorStorageAdapter = {
        get: vi.fn().mockRejectedValue(new Error('Storage unavailable')),
        set: vi.fn(),
        remove: vi.fn(),
        clear: vi.fn()
      };

      SubtitleConfigManager.setStorageAdapter(errorStorageAdapter);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const config = await SubtitleConfigManager.loadConfig();

      expect(config).toEqual(DEFAULT_SUBTITLE_CONFIG);
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('应该处理 null 和 undefined 配置值', () => {
      const configWithNulls = {
        ...DEFAULT_SUBTITLE_CONFIG,
        sourceLang: null as any,
        batchSize: undefined as any
      };

      const result = SubtitleConfigManager.validateConfig(configWithNulls);

      expect(result.valid).toBe(false);
      expect(result.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'sourceLang',
            message: expect.stringContaining('是必需字段')
          })
        ])
      );
    });
  });
});