/**
 * 字幕管理器单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SubtitleManager } from '../subtitle-manager';
import { SubtitleNetworkInterceptor } from '../network/interceptor';
import { SubtitleParserFactory } from '../parsers/parser-factory';
import { 
  SubtitleTranslationConfig, 
  SubtitleErrorType,
  SubtitleFormat,
  SupportedPlatform 
} from '../types';

// Mock dependencies
vi.mock('../network/interceptor');
vi.mock('../parsers/parser-factory');

const MockSubtitleNetworkInterceptor = SubtitleNetworkInterceptor as any;
const MockSubtitleParserFactory = SubtitleParserFactory as any;

describe('SubtitleManager', () => {
  let manager: SubtitleManager;
  let mockInterceptor: any;
  let mockConfig: SubtitleTranslationConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock interceptor instance
    mockInterceptor = {
      initialize: vi.fn().mockResolvedValue(undefined),
      startInterception: vi.fn().mockResolvedValue(undefined),
      stopInterception: vi.fn(),
      destroy: vi.fn(),
      setCaptureCallback: vi.fn(),
      getInterceptionStatus: vi.fn().mockReturnValue('ready')
    };
    
    MockSubtitleNetworkInterceptor.mockImplementation(() => mockInterceptor);
    
    // Mock parser factory
    MockSubtitleParserFactory.autoParse = vi.fn();
    MockSubtitleParserFactory.validate = vi.fn().mockReturnValue({ valid: true, errors: [] });
    
    // Test configuration
    mockConfig = {
      enabled: true,
      sourceLang: 'auto',
      targetLang: 'zh-CN',
      showOriginal: false,
      showTranslated: true,
      batchSize: 20,
      maxConcurrency: 3,
      cacheEnabled: true,
      retryCount: 2
    };

    manager = new SubtitleManager(mockConfig);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('initialization', () => {
    it('应该正确初始化管理器', async () => {
      await manager.initialize();

      expect(mockInterceptor.initialize).toHaveBeenCalled();
      expect(mockInterceptor.setCaptureCallback).toHaveBeenCalled();
      expect(manager.getStatus()).toBe('ready');
    });

    it('应该防止重复初始化', async () => {
      await manager.initialize();
      await manager.initialize(); // 第二次调用

      expect(mockInterceptor.initialize).toHaveBeenCalledTimes(1);
    });

    it('应该处理初始化错误', async () => {
      const initError = new Error('Initialization failed');
      mockInterceptor.initialize.mockRejectedValue(initError);

      await expect(manager.initialize()).rejects.toThrow();
      expect(manager.getStatus()).toBe('error');
    });

    it('应该正确设置事件监听器', async () => {
      const eventSpy = vi.fn();
      manager.on('initialized', eventSpy);

      await manager.initialize();

      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('service lifecycle', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    it('应该启动字幕翻译服务', async () => {
      await manager.start();

      expect(mockInterceptor.startInterception).toHaveBeenCalled();
      expect(manager.getStatus()).toBe('running');
    });

    it('应该处理服务已启动的情况', async () => {
      await manager.start();
      await manager.start(); // 第二次调用

      expect(mockInterceptor.startInterception).toHaveBeenCalledTimes(1);
    });

    it('应该拒绝在禁用状态下启动', async () => {
      manager.updateConfig({ ...mockConfig, enabled: false });

      await manager.start();

      expect(mockInterceptor.startInterception).not.toHaveBeenCalled();
      expect(manager.getStatus()).toBe('ready');
    });

    it('应该停止字幕翻译服务', async () => {
      await manager.start();
      
      manager.stop();

      expect(mockInterceptor.stopInterception).toHaveBeenCalled();
      expect(manager.getStatus()).toBe('ready');
      expect(manager.getCurrentSubtitles()).toHaveLength(0);
    });

    it('应该处理服务未运行时的停止调用', () => {
      manager.stop(); // 未启动就调用停止

      expect(mockInterceptor.stopInterception).not.toHaveBeenCalled();
    });

    it('应该在未初始化时拒绝启动', async () => {
      const uninitializedManager = new SubtitleManager(mockConfig);

      await expect(uninitializedManager.start()).rejects.toThrow();
    });
  });

  describe('configuration management', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    it('应该更新配置', () => {
      const newConfig = { ...mockConfig, targetLang: 'ja' as any };
      const eventSpy = vi.fn();
      
      manager.on('config-updated', eventSpy);
      manager.updateConfig(newConfig);

      expect(manager.getConfig().targetLang).toBe('ja');
      expect(eventSpy).toHaveBeenCalledWith({
        oldConfig: mockConfig,
        newConfig: expect.objectContaining({ targetLang: 'ja' })
      });
    });

    it('应该在运行时重启服务以应用新配置', async () => {
      await manager.start();
      
      const newConfig = { ...mockConfig, batchSize: 10 };
      manager.updateConfig(newConfig);

      // 等待异步重启完成
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockInterceptor.stopInterception).toHaveBeenCalled();
      expect(mockInterceptor.startInterception).toHaveBeenCalledTimes(2); // 原始启动 + 重启
    });

    it('应该获取当前配置的副本', () => {
      const config = manager.getConfig();
      
      config.enabled = false; // 修改返回的配置
      
      expect(manager.getConfig().enabled).toBe(true); // 原始配置不变
    });
  });

  describe('subtitle processing', () => {
    beforeEach(async () => {
      await manager.initialize();
      await manager.start();
    });

    it('应该处理字幕数据捕获', async () => {
      const mockSubtitles = [
        {
          id: '1',
          startTime: 0,
          endTime: 3000,
          text: 'Hello World'
        }
      ];

      MockSubtitleParserFactory.autoParse.mockResolvedValue({
        format: SubtitleFormat.VTT,
        subtitles: mockSubtitles
      });

      const eventSpy = vi.fn();
      manager.on('subtitle-parsed', eventSpy);

      // 模拟网络拦截器触发回调
      const captureCallback = mockInterceptor.setCaptureCallback.mock.calls[0][0];
      await captureCallback({
        platform: SupportedPlatform.YOUTUBE,
        format: SubtitleFormat.VTT,
        rawData: 'WEBVTT\n\n00:00:00.000 --> 00:00:03.000\nHello World',
        url: 'https://example.com/subtitles.vtt',
        timestamp: Date.now()
      });

      expect(MockSubtitleParserFactory.autoParse).toHaveBeenCalled();
      expect(eventSpy).toHaveBeenCalledWith(mockSubtitles);
      expect(manager.getCurrentSubtitles()).toEqual(mockSubtitles);
    });

    it('应该处理解析错误', async () => {
      const parseError = new Error('Parse failed');
      MockSubtitleParserFactory.autoParse.mockRejectedValue(parseError);

      const errorSpy = vi.fn();
      manager.on('error', errorSpy);

      const captureCallback = mockInterceptor.setCaptureCallback.mock.calls[0][0];
      await captureCallback({
        platform: SupportedPlatform.YOUTUBE,
        format: SubtitleFormat.VTT,
        rawData: 'invalid data',
        url: 'https://example.com/invalid.vtt',
        timestamp: Date.now()
      });

      expect(errorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: SubtitleErrorType.PARSING_ERROR
        })
      );
    });

    it('应该跳过空的解析结果', async () => {
      MockSubtitleParserFactory.autoParse.mockResolvedValue({
        format: SubtitleFormat.VTT,
        subtitles: []
      });

      const eventSpy = vi.fn();
      manager.on('subtitle-parsed', eventSpy);

      const captureCallback = mockInterceptor.setCaptureCallback.mock.calls[0][0];
      await captureCallback({
        platform: SupportedPlatform.YOUTUBE,
        format: SubtitleFormat.VTT,
        rawData: 'WEBVTT',
        url: 'https://example.com/empty.vtt',
        timestamp: Date.now()
      });

      expect(eventSpy).not.toHaveBeenCalled();
      expect(manager.getCurrentSubtitles()).toHaveLength(0);
    });

    it('应该支持手动处理字幕数据', async () => {
      const mockSubtitles = [
        {
          id: '1',
          startTime: 0,
          endTime: 3000,
          text: 'Manual subtitle'
        }
      ];

      MockSubtitleParserFactory.autoParse.mockResolvedValue({
        format: SubtitleFormat.SRT,
        subtitles: mockSubtitles
      });

      const result = await manager.processSubtitleData(
        '1\n00:00:00,000 --> 00:00:03,000\nManual subtitle',
        SubtitleFormat.SRT
      );

      expect(result).toEqual(mockSubtitles);
      expect(manager.getCurrentSubtitles()).toEqual(mockSubtitles);
    });
  });

  describe('event system', () => {
    it('应该支持事件监听和移除', () => {
      const eventSpy = vi.fn();
      
      manager.on('test', eventSpy);
      (manager as any).emit('test', 'data');
      
      expect(eventSpy).toHaveBeenCalledWith('data');
      
      manager.off('test', eventSpy);
      (manager as any).emit('test', 'data2');
      
      expect(eventSpy).toHaveBeenCalledTimes(1); // 只调用了一次
    });

    it('应该处理监听器中的错误', () => {
      const errorListener = vi.fn().mockImplementation(() => {
        throw new Error('Listener error');
      });
      const normalListener = vi.fn();
      
      manager.on('test', errorListener);
      manager.on('test', normalListener);
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      (manager as any).emit('test', 'data');

      expect(consoleSpy).toHaveBeenCalled();
      expect(normalListener).toHaveBeenCalled(); // 其他监听器仍执行
      
      consoleSpy.mockRestore();
    });
  });

  describe('utility methods', () => {
    it('应该获取管理器统计信息', async () => {
      await manager.initialize();
      
      const stats = manager.getStats();

      expect(stats).toEqual({
        status: 'ready',
        isInitialized: true,
        currentSubtitleCount: 0,
        interceptorStatus: 'ready',
        eventListenerCount: 0
      });
    });

    it('应该检查平台支持', () => {
      expect(manager.isPlatformSupported(SupportedPlatform.YOUTUBE)).toBe(true);
      expect(manager.isPlatformSupported(SupportedPlatform.NETFLIX)).toBe(true);
    });

    it('应该检查格式支持', () => {
      MockSubtitleParserFactory.getSupportedFormats = vi.fn().mockReturnValue([
        SubtitleFormat.VTT,
        SubtitleFormat.SRT
      ]);

      expect(manager.isFormatSupported(SubtitleFormat.VTT)).toBe(true);
      expect(manager.isFormatSupported(SubtitleFormat.UNKNOWN)).toBe(false);
    });

    it('应该销毁管理器', async () => {
      await manager.initialize();
      await manager.start();
      
      manager.destroy();

      expect(mockInterceptor.destroy).toHaveBeenCalled();
      expect(manager.getStatus()).toBe('idle');
      expect(manager.getCurrentSubtitles()).toHaveLength(0);
    });
  });

  describe('error handling', () => {
    it('应该处理启动错误', async () => {
      await manager.initialize();
      
      const startError = new Error('Start failed');
      mockInterceptor.startInterception.mockRejectedValue(startError);

      const errorSpy = vi.fn();
      manager.on('error', errorSpy);

      await expect(manager.start()).rejects.toThrow();
      expect(manager.getStatus()).toBe('error');
      expect(errorSpy).toHaveBeenCalled();
    });

    it('应该处理停止时的错误', async () => {
      await manager.initialize();
      await manager.start();
      
      mockInterceptor.stopInterception.mockImplementation(() => {
        throw new Error('Stop failed');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      manager.stop();

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('应该处理销毁时的错误', async () => {
      await manager.initialize();
      
      mockInterceptor.destroy.mockImplementation(() => {
        throw new Error('Destroy failed');
      });

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      manager.destroy();

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('default configuration', () => {
    it('应该使用默认配置创建管理器', () => {
      const defaultManager = new SubtitleManager();
      const config = defaultManager.getConfig();

      expect(config.enabled).toBe(true);
      expect(config.sourceLang).toBe('auto');
      expect(config.targetLang).toBe('zh-CN');
      expect(config.batchSize).toBe(20);
    });
  });

  describe('validation warnings', () => {
    it('应该记录验证警告但继续处理', async () => {
      await manager.initialize();
      await manager.start();

      const mockSubtitles = [
        {
          id: '1',
          startTime: 0,
          endTime: 3000,
          text: 'Test subtitle'
        }
      ];

      MockSubtitleParserFactory.autoParse.mockResolvedValue({
        format: SubtitleFormat.VTT,
        subtitles: mockSubtitles
      });

      // Mock validation with warnings
      MockSubtitleParserFactory.validate.mockReturnValue({
        valid: false,
        errors: ['Time overlap detected']
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const eventSpy = vi.fn();
      manager.on('subtitle-parsed', eventSpy);

      const captureCallback = mockInterceptor.setCaptureCallback.mock.calls[0][0];
      await captureCallback({
        platform: SupportedPlatform.YOUTUBE,
        format: SubtitleFormat.VTT,
        rawData: 'test data',
        url: 'https://example.com/test.vtt',
        timestamp: Date.now()
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚠️ [SubtitleManager] 字幕验证警告:'),
        ['Time overlap detected']
      );
      expect(eventSpy).toHaveBeenCalledWith(mockSubtitles); // 仍然继续处理

      consoleSpy.mockRestore();
    });
  });
});