/**
 * 字幕翻译功能模块统一导出
 * 
 * 第一阶段：核心基础架构
 * - 网络拦截系统
 * - 字幕解析系统
 * - 配置管理系统
 * - 错误处理系统
 * 
 * 第三阶段：UI渲染系统
 * - 字幕显示组件
 * - 设置面板组件
 * - UI管理器
 */

// 主要管理器类
export { SubtitleManager } from './subtitle-manager';
export { SubtitleTranslationManager, subtitleTranslationManager } from './SubtitleTranslationManager';
export { DirectSubtitleManager, directSubtitleManager } from './DirectSubtitleManager';

// 网络拦截
export { SubtitleNetworkInterceptor } from './network';

// 字幕解析器
export {
  BaseSubtitleParser,
  VTTParser,
  SRTParser,
  YouTubeParser,
  ASSParser,
  SubtitleParserFactory
} from './parsers';

// 配置管理
export {
  DEFAULT_SUBTITLE_CONFIG,
  SUBTITLE_CONFIG_VALIDATION,
  SUPPORTED_LANGUAGES,
  SubtitleConfigManager
} from './config';

// 错误处理工具
export {
  SubtitleErrorHandler,
  handleSubtitleErrors,
  subtitleErrorHandler,
  criticalErrorHandler,
  silentErrorHandler
} from './utils';

// 核心类型定义
export type {
  // 基础接口
  ISubtitleManager,
  ISubtitleNetworkInterceptor,
  ISubtitleParser,
  
  // 数据结构
  StandardSubtitle,
  SubtitleData,
  SubtitleTranslationConfig,
  DisplayConfig,
  PlatformConfig,
  SubtitlePosition,
  SubtitleStyle,
  
  // 枚举类型
  SubtitleFormat,
  SupportedPlatform,
  SubtitleManagerStatus,
  SubtitleErrorType,
  
  // 错误和验证
  SubtitleError,
  ValidationResult,
  
  // 事件类型
  SubtitleEventMap
} from './types';

// 默认导出主管理器
import { SubtitleManager } from './subtitle-manager';
export default SubtitleManager;