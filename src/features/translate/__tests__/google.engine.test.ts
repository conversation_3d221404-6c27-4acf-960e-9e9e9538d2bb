/**
 * Google翻译引擎测试
 * 测试Google引擎的核心功能：API调用、分块处理、降级策略等
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GoogleTranslateEngine } from '../engines/google';
import { TranslateErrorType, GoogleEngineConfig } from '../types';

// 检查是否启用网络测试
const NETWORK_TESTS_ENABLED = process.env.NETWORK_TESTS_ENABLED === 'true';

// Mock browser runtime API
const mockRuntimeAPI = {
  sendMessage: vi.fn().mockImplementation((message: any, callback?: (response: any) => void) => {
    // Use setTimeout to simulate async behavior and ensure callback is called
    setTimeout(() => {
      if (callback) {
        // Handle TRANSLATE_REQUEST format from engines
        if (message.type === 'TRANSLATE_REQUEST' && message.payload) {
          const { url, options } = message.payload;
          
          // Mock Google Translate API responses
          if (url.includes('translate-pa.googleapis.com/v1/translateHtml')) {
            // Parse request body to get texts
            let texts = ['Hello']; // Default fallback
            try {
              if (options.body) {
                const parsed = JSON.parse(options.body);
                if (Array.isArray(parsed) && Array.isArray(parsed[0])) {
                  texts = parsed[0];
                }
              }
            } catch (e) {
              // Use default texts
            }
            
            const translations = texts.map(text => [
              `翻译: ${text}`,
              text,
              null,
              null,
              1
            ]);
            
            callback({
              success: true,
              data: JSON.stringify([
                [translations],
                'te_lib'
              ]),
              status: 200
            });
            return;
          }
          
          if (url.includes('translate.googleapis.com/translate_a/t')) {
            // Handle form data format
            let texts = ['Hello']; // Default fallback
            try {
              if (options.body) {
                const formData = new URLSearchParams(options.body);
                texts = formData.getAll('q').filter(t => t.trim());
              }
            } catch (e) {
              // Use default texts
            }
            
            const translations = texts.map(text => [
              `翻译: ${text}`,
              text,
              null,
              null,
              1
            ]);
            
            callback({
              success: true,
              data: JSON.stringify([translations]),
              status: 200
            });
            return;
          }
        }
        
        // Default mock response
        callback({ success: true, data: 'mock response' });
      }
    }, 10); // Very small delay to simulate async behavior
    
    // For Promise compatibility, also return a Promise (though engines use callback)
    return Promise.resolve({ success: true, data: 'mock response' });
  }),
  lastError: null
};

// Mock global APIs
(global as any).browser = {
  runtime: mockRuntimeAPI
};

(global as any).chrome = {
  runtime: mockRuntimeAPI
};

describe('GoogleTranslateEngine', () => {
  let engine: GoogleTranslateEngine;
  let mockConfig: GoogleEngineConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    mockRuntimeAPI.sendMessage.mockClear();
    
    mockConfig = {
      name: 'google',
      enabled: true,
      priority: 1,
      maxChunkSize: 5000,
      maxBatchSize: 128,
      timeout: 10000,
      retryCount: 2,
      apis: [
        {
          name: 'google-translate-api-1',
          url: 'https://translate-pa.googleapis.com/v1/translateHtml',
          priority: 1,
          enabled: true,
          timeout: 8000
        },
        {
          name: 'google-translate-api-2',
          url: 'https://translate.googleapis.com/translate_a/t',
          priority: 2,
          enabled: true,
          timeout: 8000
        }
      ]
    };
    
    engine = new GoogleTranslateEngine(mockConfig);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('基础配置', () => {
    it('应该正确设置引擎基础属性', () => {
      expect(engine.name).toBe('google');
      expect(engine.displayName).toBe('Google Translate');
      expect(engine.maxChunkSize).toBe(5000);
      expect(engine.maxBatchSize).toBe(128);
    });

    it('应该继承配置信息', () => {
      const config = (engine as any).config;
      expect(config.name).toBe('google');
      expect(config.enabled).toBe(true);
      expect(config.apis).toHaveLength(2);
    });
  });

  describe('支持的语言', () => {
    it('应该返回支持的语言列表', () => {
      const languages = engine.getSupportedLanguages();
      expect(languages).toContain('en');
      expect(languages).toContain('zh');
      expect(languages).toContain('auto');
      expect(languages.length).toBeGreaterThan(50);
    });

    it('应该验证语言代码', () => {
      const validOptions = (engine as any).validateOptions({ from: 'en', to: 'zh' });
      expect(validOptions.from).toBe('en');
      expect(validOptions.to).toBe('zh');
    });

    it('应该处理无效语言代码', () => {
      const validOptions = (engine as any).validateOptions({ from: 'invalid', to: 'zh' });
      expect(validOptions.from).toBe('auto');
      expect(validOptions.to).toBe('zh');
    });
  });

  describe('文本分块处理', () => {
    it('应该处理小量文本', () => {
      const texts = ['Hello', 'World'];
      const chunks = (engine as any).chunkTexts(texts);
      expect(chunks).toHaveLength(1);
      expect(chunks[0]).toEqual(['Hello', 'World']);
    });

    it('应该按数量分块', () => {
      const texts = Array.from({ length: 200 }, (_, i) => `Text ${i}`);
      const chunks = (engine as any).chunkTexts(texts);
      expect(chunks.length).toBeGreaterThan(1);
      chunks.forEach((chunk: string[]) => {
        expect(chunk.length).toBeLessThanOrEqual(128);
      });
    });

    it('应该按大小分块', () => {
      const longText = 'a'.repeat(2000);
      const texts = [longText, longText, longText]; // 6000 chars total
      const chunks = (engine as any).chunkTexts(texts);
      expect(chunks.length).toBeGreaterThan(1);
    });
  });

  describe('API请求构建', () => {
    it('应该正确构建API 1请求体', () => {
      const body = (engine as any).buildGoogleApi1Body(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(typeof body).toBe('string');
      const parsed = JSON.parse(body);
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed[0][0]).toEqual(['Hello']);
      expect(parsed[0][1]).toBe('en');
      expect(parsed[0][2]).toBe('zh');
      expect(parsed[1]).toBe('te_lib');
    });

    it('应该正确构建API 2请求体', () => {
      const body = (engine as any).buildGoogleApi2Body(['Hello', 'World'], {
        from: 'en',
        to: 'zh'
      });

      expect(typeof body).toBe('string');
      const params = new URLSearchParams(body);
      const qParams = params.getAll('q');
      expect(qParams).toContain('Hello');
      expect(qParams).toContain('World');
    });

    it('应该正确构建API 2 URL', () => {
      const url = (engine as any).buildGoogleApi2Url({
        from: 'en',
        to: 'zh',
        format: 'text'
      }, 'https://translate.googleapis.com/translate_a/t');

      expect(url).toContain('client=gtx');
      expect(url).toContain('dt=t');
      expect(url).toContain('sl=en');
      expect(url).toContain('tl=zh');
      expect(url).toContain('format=text');
    });
  });

  describe('翻译功能', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该成功翻译单个文本', async () => {
      const result = await engine.translateBatch(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(result.translations).toHaveLength(1);
      expect(result.translations[0]).toBe('你好，世界！');
      expect(result.duration).toBeGreaterThan(0);
    }, 5000);

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该成功翻译多个文本', async () => {
      const texts = ['Hello', 'World', 'test'];
      const result = await engine.translateBatch(texts, {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(result.translations).toHaveLength(texts.length);
      result.translations.forEach(translation => {
        expect(typeof translation).toBe('string');
        expect(translation.length).toBeGreaterThan(0);
      });
    });

    it('应该处理空文本数组', async () => {
      await expect(engine.translateBatch([], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理超长文本分块', async () => {
      const longTexts = Array.from({ length: 10 }, (_, i) => 
        `This is a very long text number ${i} `.repeat(50)
      );
      
      const result = await engine.translateBatch(longTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.translations).toHaveLength(longTexts.length);
    }, 10000);
  });

  describe('API降级策略', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该能够使用API 2作为降级', async () => {
      // 配置只使用API 2
      const config = { ...mockConfig };
      config.apis = config.apis.map(api => ({
        ...api,
        enabled: api.name === 'google-translate-api-2'
      }));
      
      const testEngine = new GoogleTranslateEngine(config);
      
      const result = await testEngine.translateBatch(['Hello'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(typeof result.translations[0]).toBe('string');
    });
  });

  describe('语言检测', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该检测语言', async () => {
      const detectedLang = await engine.detectLanguage('Hello World');
      
      // MSW会返回mock数据，这里检测应该能成功
      expect(typeof detectedLang).toBe('string');
      expect(detectedLang.length).toBeGreaterThan(0);
    }, 8000);

    it('应该处理空文本', async () => {
      const result = await engine.detectLanguage('');
      expect(result).toBe('auto');
    });
  });

  describe('错误处理', () => {
    it('应该正确处理网络错误', async () => {
      // 使用不存在的URL触发错误处理
      const badConfig = { ...mockConfig };
      badConfig.apis = [{
        name: 'bad-api',
        url: 'https://nonexistent-api.com/translate',
        priority: 1,
        enabled: true,
        timeout: 1000
      }];
      
      const badEngine = new GoogleTranslateEngine(badConfig);
      
      await expect(badEngine.translateBatch(['Hello'], { from: 'en', to: 'zh' }))
        .rejects.toThrow();
    });

    it('应该分类错误类型', () => {
      const networkError = new Error('Network error');
      const errorType = (engine as any).classifyError(networkError);
      expect(Object.values(TranslateErrorType)).toContain(errorType);
    });
  });

  describe('配置更新', () => {
    it('应该能够更新配置', () => {
      const newConfig = { ...mockConfig, timeout: 15000 };
      engine.updateConfig(newConfig);
      
      const currentConfig = (engine as any).config;
      expect(currentConfig.timeout).toBe(15000);
    });

    it('应该能够获取可用API', () => {
      const apis = (engine as any).getAvailableApis();
      expect(Array.isArray(apis)).toBe(true);
      expect(apis.every((api: any) => api.enabled)).toBe(true);
    });

    it('应该按字符长度分块', () => {
      const longText = 'A'.repeat(6000);
      const texts = [longText, 'Short text'];
      const chunks = engine['chunkTexts'](texts);
      expect(chunks.length).toBeGreaterThan(1);
    });
  });

  describe('API请求构建', () => {
    it('应该正确构建Google API 1请求体', () => {
      const texts = ['Hello', 'World'];
      const options = { from: 'en', to: 'zh' };
      const body = (engine as any)['buildGoogleApi1Body'](texts, options);
      
      expect(typeof body).toBe('string');
      expect(body).toContain('Hello');
      expect(body).toContain('World');
    });

    it('应该正确构建Google API 2 URL和请求体', () => {
      const texts = ['Hello', 'World'];
      const options = { from: 'en', to: 'zh' };
      const url = (engine as any)['buildGoogleApi2Url'](options, 'https://example.com');
      const body = (engine as any)['buildGoogleApi2Body'](texts, options);
      
      expect(typeof body).toBe('string');
    });

    it('应该使用默认语言', () => {
      const texts = ['Hello'];
      const options = {};
      const request = (engine as any)['buildGoogleApi1Body'](texts, options);
      const parsed = JSON.parse(request);

      expect(parsed[0][1]).toBe('en'); // from - 根据实际代码，默认是'en'
      expect(parsed[0][2]).toBe('zh-CN');   // to - 根据实际代码，默认是'zh-CN'
    });
  });

  describe('API响应解析', () => {
    it('应该正确解析Google API 1响应', () => {
      // 测试简单字符串数组格式
      const mockResponse = ['你好', '世界'];
      const result = (engine as any)['parseGoogleApi1Response'](mockResponse, ['Hello', 'World']);
      expect(result).toEqual(['你好', '世界']);
    });

    it('应该处理API 1响应异常', () => {
      // 根据实际代码，当响应为null时返回原文而不是抛出错误
      const result = (engine as any)['parseGoogleApi1Response'](null, ['test']);
      expect(result).toEqual(['test']);
    });

    it('应该补充缺失的翻译', () => {
      // 测试不完整翻译结果的补充
      const mockResponse = ['你好']; // 只有一个翻译结果
      const result = (engine as any)['parseGoogleApi1Response'](mockResponse, ['Hello', 'World']);
      expect(result).toEqual(['你好', 'World']); // 第二个应该被原文补充
    });

    it('应该正确解析Google API 2响应', () => {
      // Google API 2 的响应格式应该和 API 1 类似：嵌套数组格式
      const mockResponse = [
        [
          ['你好', 'Hello', null, null, 1],
          ['世界', 'World', null, null, 1]
        ]
      ];
      
      const result = (engine as any)['parseGoogleApi2Response'](mockResponse, ['Hello', 'World']);
      expect(result).toEqual(['你好', '世界']);
    });
  });

  describe('翻译请求', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该通过background script发送请求', async () => {
      const mockResponse = {
        success: true,
        data: [
          [
            [['你好', 'Hello', null, null, 1]]
          ]
        ],
        status: 200,
        statusText: 'OK'
      };
      
      vi.mocked(browser.runtime.sendMessage).mockResolvedValue(mockResponse);

      const result = await engine.translateBatch(['Hello'], { from: 'en', to: 'zh' });

      expect(browser.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'TRANSLATE_REQUEST',
        payload: {
          url: 'https://translate.googleapis.com/translate_a/single',
          options: {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: expect.any(String)
          }
        }
      });
      
      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(result.engine).toBe('google');
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理background请求失败', async () => {
      vi.mocked(browser.runtime.sendMessage).mockResolvedValue({
        success: false,
        error: 'Network error'
      });

      await expect(engine.translateBatch(['Hello'], {})).rejects.toThrow();
    });

    it('应该处理空文本数组', async () => {
      await expect(engine.translateBatch([], {})).rejects.toThrow(
        'Input texts must be a non-empty array'
      );
    });
  });

  describe('API降级策略', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该优先使用API 1', async () => {
      const mockResponse = {
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      };
      
      vi.mocked(browser.runtime.sendMessage).mockResolvedValue(mockResponse);

      await engine.translateBatch(['Hello'], { from: 'en', to: 'zh' });

      const call = vi.mocked(browser.runtime.sendMessage).mock.calls[0]?.[0] as any;
      expect(call?.payload?.url).toBe('https://translate.googleapis.com/translate_a/single');
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该在API 1失败时尝试API 2', async () => {
      // 启用API 2
      mockConfig.apis[1].enabled = true;
      mockConfig.apis[1].url = 'https://api2.translate.com';
      engine = new GoogleTranslateEngine(mockConfig);
      
      vi.mocked(browser.runtime.sendMessage)
        .mockRejectedValueOnce(new Error('API 1 failed'))
        .mockResolvedValueOnce({
          success: true,
          data: { data: { translations: [{ translatedText: '你好' }] } },
          status: 200
        });

      const result = await engine.translateBatch(['Hello'], { from: 'en', to: 'zh' });

      expect(browser.runtime.sendMessage).toHaveBeenCalledTimes(2);
      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该在所有API失败时抛出错误', async () => {
      vi.mocked(browser.runtime.sendMessage).mockRejectedValue(new Error('All APIs failed'));

      await expect(engine.translateBatch(['Hello'], {})).rejects.toThrow(
        'All Google APIs failed'
      );
    });
  });

  describe('语言检测', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该检测语言', async () => {
      const mockResponse = {
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      };
      
      vi.mocked(browser.runtime.sendMessage).mockResolvedValue(mockResponse);

      const language = await engine.detectLanguage('Hello');
      expect(language).toBe('auto'); // 简化实现返回auto
    });

    it('应该处理空文本', async () => {
      const language = await engine.detectLanguage('');
      expect(language).toBe('auto');
    });
  });

  describe('配置更新', () => {
    it('应该更新Google配置', () => {
      const updates = { timeout: 15000 };
      engine.updateGoogleConfig(updates);
      
      const config = engine.getGoogleConfig();
      expect(config.timeout).toBe(15000);
    });
  });

  describe('错误处理', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理网络错误', async () => {
      vi.mocked(browser.runtime.sendMessage).mockRejectedValue(new Error('Network error'));

      await expect(engine.translateBatch(['Hello'], {})).rejects.toMatchObject({
        type: TranslateErrorType.NETWORK_ERROR
      });
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理API错误', async () => {
      vi.mocked(browser.runtime.sendMessage).mockResolvedValue({
        success: false,
        error: 'API error'
      });

      await expect(engine.translateBatch(['Hello'], {})).rejects.toThrow();
    });
  });
});