/**
 * YouTube字幕翻译调试工具
 * 帮助诊断扩展在YouTube页面上的加载和运行状态
 */

// 全局调试对象
declare global {
  interface Window {
    LucidYouTubeDebug: {
      checkExtensionStatus: () => void;
      testSubtitleManager: () => void;
      findVideoContainers: () => void;
      simulateSubtitle: () => void;
      checkPageInfo: () => void;
    };
  }
}

/**
 * 检查扩展加载状态
 */
function checkExtensionStatus() {
  console.group('🔍 [YouTube调试] 扩展状态检查');

  // 检查扩展基础信息
  console.log('📱 浏览器信息:', {
    userAgent: navigator.userAgent,
    url: window.location.href,
    hostname: window.location.hostname,
    isYouTube: window.location.hostname.includes('youtube.com')
  });

  // 检查content script是否加载
  const contentScriptLoaded = document.querySelector('#lucid-subtitle-styles') !== null;
  console.log('📜 Content Script状态:', {
    stylesInjected: contentScriptLoaded,
    styleElement: document.querySelector('#lucid-subtitle-styles')
  });

  // 检查全局对象
  const globalObjects = {
    chrome: typeof chrome !== 'undefined',
    LucidDebug: typeof (window as any).LucidDebug !== 'undefined',
    lucidExt: typeof (window as any).lucidExt !== 'undefined'
  };
  console.log('🌍 全局对象:', globalObjects);

  // 检查扩展相关元素
  const extensionElements = {
    subtitleWindow: document.querySelector('#lucid-caption-window'),
    settingsPanel: document.querySelector('#lucid-settings-panel'),
    youtubeButton: document.querySelector('#lucid-youtube-subtitle-button'),
    extensionStyles: document.querySelector('#lucid-subtitle-styles')
  };
  console.log('🎭 扩展UI元素:', extensionElements);

  console.groupEnd();
}

/**
 * 测试字幕管理器
 */
function testSubtitleManager() {
  console.group('🎬 [YouTube调试] 字幕管理器测试');

  try {
    // 动态导入字幕管理器
    import('../features/subtitle-translation/DirectSubtitleManager').then(({ DirectSubtitleManager }) => {
      console.log('✅ 字幕管理器模块加载成功');

      const manager = new DirectSubtitleManager();
      console.log('✅ 字幕管理器实例创建成功', manager);

      // 测试显示字幕
      setTimeout(() => {
        manager.showSubtitle(
          'Hello, this is a test subtitle!',
          '你好，这是一个测试字幕！'
        );
        console.log('✅ 测试字幕已显示');

        // 3秒后隐藏
        setTimeout(() => {
          manager.hideSubtitle();
          console.log('✅ 测试字幕已隐藏');
        }, 3000);
      }, 1000);

    }).catch(error => {
      console.error('❌ 字幕管理器加载失败:', error);
    });

  } catch (error) {
    console.error('❌ 字幕管理器测试失败:', error);
  }

  console.groupEnd();
}

/**
 * 查找YouTube视频容器
 */
function findVideoContainers() {
  console.group('📺 [YouTube调试] 视频容器检测');

  const videoSelectors = [
    '#movie_player',
    '#player-container',
    '.html5-video-container',
    '.html5-video-player',
    'video',
    '.ytp-caption-window-container'
  ];

  const foundContainers: { selector: string; elements: HTMLElement[]; isValid: boolean }[] = [];

  videoSelectors.forEach(selector => {
    try {
      const elements = Array.from(document.querySelectorAll(selector)) as HTMLElement[];
      if (elements.length > 0) {
        elements.forEach((element, index) => {
          const rect = element.getBoundingClientRect();
          const isValid = rect.width > 300 && rect.height > 200;

          console.log(`${isValid ? '✅' : '❌'} 找到容器 ${selector}[${index}]:`, {
            element,
            size: { width: rect.width, height: rect.height },
            position: { x: rect.left, y: rect.top },
            visible: rect.width > 0 && rect.height > 0,
            className: element.className,
            id: element.id
          });

          foundContainers.push({
            selector,
            elements: [element],
            isValid
          });
        });
      }
    } catch (error) {
      console.warn(`⚠️ 选择器 ${selector} 查询失败:`, error);
    }
  });

  console.log('📊 容器检测总结:', {
    totalFound: foundContainers.length,
    validContainers: foundContainers.filter(c => c.isValid).length,
    containers: foundContainers
  });

  console.groupEnd();
  return foundContainers;
}

/**
 * 模拟字幕显示
 */
function simulateSubtitle() {
  console.group('🎭 [YouTube调试] 模拟字幕显示');

  try {
    // 首先检查是否有新的字幕按钮
    const subtitleButton = document.querySelector('#lucid-youtube-subtitle-button') as HTMLElement;
    if (subtitleButton) {
      console.log('✅ 找到Lucid字幕按钮，尝试点击测试...');
      subtitleButton.click();

      // 检查设置面板是否出现
      setTimeout(() => {
        const settingsPanel = document.querySelector('#lucid-settings-panel');
        if (settingsPanel) {
          console.log('✅ 设置面板已打开');
          // 关闭设置面板
          setTimeout(() => {
            subtitleButton.click();
            console.log('✅ 设置面板已关闭');
          }, 2000);
        } else {
          console.warn('❌ 设置面板未出现');
        }
      }, 500);
    } else {
      console.warn('❌ 未找到Lucid字幕按钮');
    }

    // 检查DirectSubtitleManager是否可用
    if (typeof (window as any).directSubtitleManager !== 'undefined') {
      console.log('✅ DirectSubtitleManager 全局实例可用');

      // 测试显示字幕功能
      setTimeout(() => {
        const manager = (window as any).directSubtitleManager;
        try {
          manager.showSubtitle(
            'Hello! This is a test subtitle from YouTube Debug.',
            '你好！这是来自YouTube调试工具的测试字幕。'
          );
          console.log('✅ DirectSubtitleManager 字幕显示测试成功');

          // 3秒后隐藏字幕
          setTimeout(() => {
            manager.hideSubtitle();
            console.log('✅ DirectSubtitleManager 字幕隐藏测试成功');
          }, 3000);
        } catch (error) {
          console.error('❌ DirectSubtitleManager 测试失败:', error);
        }
      }, 1000);
    } else {
      console.warn('❌ DirectSubtitleManager 全局实例不可用');
    }

    // 查找视频容器
    const videoContainer = document.querySelector('#movie_player') as HTMLElement;

    if (!videoContainer) {
      console.error('❌ 未找到YouTube播放器容器');
      console.groupEnd();
      return;
    }

    console.log('✅ 找到YouTube播放器:', videoContainer);

    // 创建简单的测试字幕
    const testSubtitle = document.createElement('div');
    testSubtitle.id = 'lucid-test-subtitle';
    testSubtitle.style.cssText = `
      position: absolute;
      bottom: 80px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 8px;
      font-size: 16px;
      font-family: Arial, sans-serif;
      z-index: 9999;
      pointer-events: none;
      box-shadow: 0 2px 10px rgba(0,0,0,0.5);
    `;

    testSubtitle.innerHTML = `
      <div style="text-align: center; margin-bottom: 5px;">
        🎬 YouTube Debug Test
      </div>
      <div style="text-align: center; font-size: 14px; opacity: 0.9;">
        🎯 扩展正常工作 | Extension Working
      </div>
    `;

    // 确保视频容器是相对定位
    videoContainer.style.position = 'relative';
    videoContainer.appendChild(testSubtitle);

    console.log('✅ 测试字幕已显示');

    // 5秒后自动移除
    setTimeout(() => {
      if (testSubtitle.parentNode) {
        testSubtitle.parentNode.removeChild(testSubtitle);
        console.log('✅ 测试字幕已移除');
      }
    }, 5000);

  } catch (error) {
    console.error('❌ 模拟字幕显示失败:', error);
  }

  console.groupEnd();
}

/**
 * 检查页面信息
 */
function checkPageInfo() {
  console.group('📄 [YouTube调试] 页面信息检查');

  // YouTube特定信息
  const youtubeInfo = {
    isWatchPage: window.location.pathname.includes('/watch'),
    videoId: new URLSearchParams(window.location.search).get('v'),
    hasVideo: document.querySelector('video') !== null,
    playerReady: document.querySelector('#movie_player') !== null
  };

  console.log('🎥 YouTube页面信息:', youtubeInfo);

  // DOM就绪状态
  const domInfo = {
    readyState: document.readyState,
    loaded: document.readyState === 'complete',
    bodyExists: !!document.body,
    headExists: !!document.head
  };

  console.log('📄 DOM状态:', domInfo);

  // 扩展脚本状态
  const scriptInfo = {
    hasContentScript: !!document.querySelector('script[src*="content"]'),
    hasManifest: typeof chrome !== 'undefined' && chrome.runtime,
    extensionId: typeof chrome !== 'undefined' ? chrome.runtime?.id : 'undefined'
  };

  console.log('🔌 扩展脚本状态:', scriptInfo);

  console.groupEnd();
}

/**
 * 运行完整诊断
 */
function runFullDiagnosis() {
  console.log('🚀 [YouTube调试] 开始完整诊断...');

  checkPageInfo();
  checkExtensionStatus();
  findVideoContainers();

  // 延迟测试字幕管理器
  setTimeout(() => {
    testSubtitleManager();
  }, 1000);

  // 延迟显示模拟字幕
  setTimeout(() => {
    simulateSubtitle();
  }, 2000);
}

// 导出调试函数到全局对象
if (typeof window !== 'undefined') {
  window.LucidYouTubeDebug = {
    checkExtensionStatus,
    testSubtitleManager,
    findVideoContainers,
    simulateSubtitle,
    checkPageInfo
  };

  // 控制台友好提示
  console.log(`
🎯 YouTube字幕翻译调试工具已就绪！

📝 可用的调试命令：
• LucidYouTubeDebug.checkExtensionStatus() - 检查扩展状态
• LucidYouTubeDebug.findVideoContainers() - 查找视频容器
• LucidYouTubeDebug.testSubtitleManager() - 测试字幕管理器
• LucidYouTubeDebug.simulateSubtitle() - 显示测试字幕
• LucidYouTubeDebug.checkPageInfo() - 检查页面信息

🚀 运行完整诊断：
LucidYouTubeDebug.checkExtensionStatus()
  `);
}

// 自动运行基础检查
if (document.readyState === 'complete') {
  setTimeout(runFullDiagnosis, 500);
} else {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(runFullDiagnosis, 500);
  });
}

export {
  checkExtensionStatus,
  testSubtitleManager,
  findVideoContainers,
  simulateSubtitle,
  checkPageInfo
};