/* YouTube按钮样式 - 转换自CSS Module */
.lucid-youtube-button {
  width: 48px;
  height: 100%;
  display: inline-block;
  float: right;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.lucid-button-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.lucid-button-container .lucid-button-logo {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  line-height: 1.5;
  color: #d8d8d8;
  z-index: 1000;
  position: relative;
  border-radius: 8px;
  width: 24px;
  height: 24px;
  /* 添加过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.lucid-panel-menu {
  border-radius: 12px;
  background: rgba(28, 28, 28, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 2147483647;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  user-select: none;
  position: absolute;
  bottom: 58px;
  right: 0;
  width: 280px;
  overflow: hidden;
  display: none;
}

.lucid-panel-menu-container {
  display: flex;
  flex-direction: row;
  width: 560px;
  transition: transform 0.3s ease-in-out, height 0.3s ease-in-out;
}

.lucid-panel-menu-container .lucid-primary-panel-menu,
.lucid-panel-menu-container .lucid-second-panel-menu {
  width: 280px;
  flex-shrink: 0;
  transition: all 0.2s ease-in-out;
}

/* 悬停效果 */
.lucid-youtube-button:hover {
  opacity: 1 !important;
}

/* 按钮logo的hover效果 - Figma风格魔法动画 */
.lucid-button-logo:hover {
  transform: scale(1.05);
}

/* SVG图标的hover效果 */
.lucid-button-logo:hover svg {
  transform: scale(1.1);
  transition: transform 0.2s ease-out;
}
