/**
 * UI Manager 测试示例
 * 演示如何使用Shadow DOM管理器创建各种UI组件
 */

import React from 'react';
import { uiManager } from './uiManager';

// 测试函数，用于在开发环境中验证功能
export const testUIManager = () => {
  console.log('🧪 UI Manager 测试开始');

  // 测试1: 显示简单Tooltip
  const testTooltip = async () => {
    console.log('📍 测试 Tooltip');
    
    const tooltipId = await uiManager.showTooltip({
      word: 'test',
      position: { x: 100, y: 100 },
      content: 'This is a test tooltip'
    });
    
    console.log(`✅ Tooltip 创建成功，ID: ${tooltipId}`);
    
    // 3秒后隐藏
    setTimeout(() => {
      uiManager.hide(tooltipId);
      console.log('👋 Tooltip 已隐藏');
    }, 3000);
  };

  // 测试2: 显示Toolfull
  const testToolfull = async () => {
    console.log('📍 测试 Toolfull');
    
    const toolfullId = await uiManager.showToolfull({
      word: 'example',
      position: { x: 200, y: 200 },
      data: {
        definitions: [
          'A representative form or pattern',
          'An instance serving for illustration'
        ],
        examples: [
          'This is an example sentence.',
          'For example, you might say...'
        ],
        pronunciation: 'ɪɡˈzæmpəl',
        etymology: 'From Latin exemplum'
      }
    });
    
    console.log(`✅ Toolfull 创建成功，ID: ${toolfullId}`);
    
    // 5秒后隐藏
    setTimeout(() => {
      uiManager.hide(toolfullId);
      console.log('👋 Toolfull 已隐藏');
    }, 5000);
  };

  // 测试3: 显示Slider
  const testSlider = async () => {
    console.log('📍 测试 Slider');
    
    const sliderContent = React.createElement('div', null, [
      React.createElement('p', { key: 'p1' }, 'This is slider content.'),
      React.createElement('button', { 
        key: 'btn',
        onClick: () => console.log('Button clicked!')
      }, 'Click Me')
    ]);
    
    const sliderId = await uiManager.showSlider({
      title: 'Test Slider',
      position: { x: 300, y: 300 },
      content: sliderContent,
      width: 350,
      height: 250
    });
    
    console.log(`✅ Slider 创建成功，ID: ${sliderId}`);
    
    // 7秒后隐藏
    setTimeout(() => {
      uiManager.hide(sliderId);
      console.log('👋 Slider 已隐藏');
    }, 7000);
  };

  // 测试4: 显示Modal
  const testModal = async () => {
    console.log('📍 测试 Modal');
    
    const modalContent = React.createElement('div', null, [
      React.createElement('p', { key: 'p1' }, 'This is a modal dialog.'),
      React.createElement('p', { key: 'p2' }, 'It can contain any React content.'),
      React.createElement('button', {
        key: 'btn',
        onClick: () => uiManager.hideType('modal')
      }, 'Close Modal')
    ]);
    
    const modalId = await uiManager.showModal({
      title: 'Test Modal',
      position: { x: 400, y: 150 },
      content: modalContent,
      width: 400,
      height: 300,
      centered: true,
      backdrop: true
    });
    
    console.log(`✅ Modal 创建成功，ID: ${modalId}`);
    
    // 10秒后隐藏
    setTimeout(() => {
      uiManager.hide(modalId);
      console.log('👋 Modal 已隐藏');
    }, 10000);
  };

  // 顺序执行测试
  const runTests = async () => {
    await testTooltip();
    
    setTimeout(async () => {
      await testToolfull();
      
      setTimeout(async () => {
        await testSlider();
        
        setTimeout(async () => {
          await testModal();
          
          // 显示统计信息
          setTimeout(() => {
            const stats = uiManager.getStats();
            console.log('📊 UI Manager 统计:', stats);
          }, 2000);
          
        }, 2000);
      }, 2000);
    }, 1000);
  };

  runTests();
};

// 全局函数，用于控制台调试
declare global {
  interface Window {
    testUIManager: typeof testUIManager;
    testToolfull: () => Promise<void>;
  }
}

// 导出到全局作用域供开发使用
if (typeof window !== 'undefined') {
  window.testUIManager = testUIManager;
  
  (window as any).testTooltip = async () => {
    const id = await uiManager.showTooltip({
      word: 'hello',
      position: { x: 100, y: 100 },
      content: 'Hello World!'
    });
    console.log('Tooltip shown:', id);
  };
  
  window.testToolfull = async () => {
    const id = await uiManager.showToolfull({
      word: 'world',
      position: { x: 200, y: 200 },
      data: {
        definitions: ['The earth and all its inhabitants'],
        examples: ['Hello world!'],
        pronunciation: 'wɜːrld'
      }
    });
    console.log('Toolfull shown:', id);
  };
  
  (window as any).clearAllUI = async () => {
    await uiManager.hideAll();
    console.log('All UI cleared');
  };

  (window as any).uiStats = () => {
    const stats = uiManager.getStats();
    console.log('UI Stats:', stats);
  };
}