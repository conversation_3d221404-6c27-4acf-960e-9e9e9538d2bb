/**
 * 设置面板样式
 * 基于设计稿 subtitle-setting.html
 */

export const SETTINGS_PANEL_CSS = `
/* 设置面板样式 - 基于设计稿 subtitle-setting.html */
.settings-panel {
  z-index: 10001;
  user-select: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.settings-panel .panel-container {
  width: 282px;
  padding: 1px;
  background: rgba(28, 28, 28, 0.90);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30);
  border-radius: 12px;
  outline: 1px rgba(255, 255, 255, 0.10) solid;
  outline-offset: -1px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  overflow: hidden;
}

.settings-panel .content-container {
  width: 100%;
  display: inline-flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
}

.settings-panel .setting-item {
  width: 100%;
  height: 40px;
  padding: 0 8px 0 12px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  cursor: default;
  transition: background-color 0.2s ease;
  box-sizing: border-box;
}

.settings-panel .setting-item.clickable {
  cursor: pointer;
}

.settings-panel .setting-item.clickable:hover {
  background: rgba(255, 255, 255, 0.05);
}

.settings-panel .icon-container {
  width: 28px;
  height: 20px;
  padding-right: 8px;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex-shrink: 0;
}

.settings-panel .icon-wrapper {
  width: 20px;
  height: 20px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.settings-panel .label-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.settings-panel .label {
  color: rgba(255, 255, 255, 0.80);
  font-size: 13px;
  font-family: 'Roboto', 'Microsoft YaHei', -apple-system, sans-serif;
  font-weight: 400;
  line-height: 13px;
  word-wrap: break-word;
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.settings-panel .info-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.settings-panel .control-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.settings-panel .value-container {
  max-width: 110px;
  padding-right: 4px;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.settings-panel .value {
  max-width: 106px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  color: rgba(255, 255, 255, 0.90);
  font-size: 12.90px;
  font-family: 'Roboto', 'Microsoft YaHei', -apple-system, sans-serif;
  font-weight: 400;
  line-height: 15.60px;
  word-wrap: break-word;
  text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50);
  white-space: nowrap;
  text-overflow: ellipsis;
}

.settings-panel .separator {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.08);
  margin: 0;
}

/* 开关组件样式 */
.settings-panel .toggle-container {
  width: 56px;
  height: 38px;
  padding: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  cursor: pointer;
  box-sizing: border-box;
}

.settings-panel .toggle-background {
  width: 32px;
  height: 14px;
  border-radius: 7px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
  transition: background-color 0.3s ease;
}

.settings-panel .toggle-background.enabled {
  background: rgba(255, 200, 98, 0.5);
}

.settings-panel .toggle-thumb {
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background: #FFC862;
  position: absolute;
  top: -2px;
  left: -1px;
  transition: transform 0.3s ease;
  box-shadow: 
    0px 1px 3px rgba(0, 0, 0, 0.12),
    0px 1px 1px rgba(0, 0, 0, 0.14),
    0px 2px 1px rgba(0, 0, 0, 0.2);
}

.settings-panel .toggle-thumb.thumb-enabled {
  transform: translateX(15px);
}

/* 动画效果 */
.settings-panel {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
  animation: settingsPanelFadeIn 0.2s ease-out forwards;
}

@keyframes settingsPanelFadeIn {
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.settings-panel.hiding {
  animation: settingsPanelFadeOut 0.2s ease-in forwards;
}

@keyframes settingsPanelFadeOut {
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .settings-panel .panel-container {
    width: 260px;
  }
  
  .settings-panel .label {
    font-size: 12px;
  }
  
  .settings-panel .value {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .settings-panel .panel-container {
    width: 240px;
  }
  
  .settings-panel .setting-item {
    height: 36px;
  }
  
  .settings-panel .label {
    font-size: 11px;
  }
  
  .settings-panel .value {
    font-size: 11px;
  }
  
  .settings-panel .toggle-container {
    width: 48px;
    height: 32px;
    padding: 8px;
  }
  
  .settings-panel .toggle-background {
    width: 28px;
    height: 12px;
  }
  
  .settings-panel .toggle-thumb {
    width: 16px;
    height: 16px;
  }
  
  .settings-panel .toggle-thumb.thumb-enabled {
    transform: translateX(12px);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .settings-panel .panel-container {
    background: rgba(0, 0, 0, 0.95);
    outline: 2px rgba(255, 255, 255, 0.3) solid;
  }
  
  .settings-panel .label,
  .settings-panel .value {
    text-shadow: none;
    color: rgba(255, 255, 255, 1);
  }
  
  .settings-panel .separator {
    background: rgba(255, 255, 255, 0.2);
  }
}

/* 暗色主题偏好 */
@media (prefers-color-scheme: dark) {
  .settings-panel .panel-container {
    background: rgba(20, 20, 20, 0.92);
  }
}

/* 亮色主题偏好 */
@media (prefers-color-scheme: light) {
  .settings-panel .panel-container {
    background: rgba(32, 32, 32, 0.88);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .settings-panel,
  .settings-panel.hiding,
  .settings-panel .toggle-thumb,
  .settings-panel .toggle-background {
    animation: none;
    transition: none;
  }
}
`;