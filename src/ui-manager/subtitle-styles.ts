/**
 * 字幕叠加层样式
 * 基于设计稿 subtitle.html
 */

export const SUBTITLE_OVERLAY_CSS = `
/* 字幕叠加层样式 - 基于设计稿 subtitle.html */
.subtitle-overlay {
  z-index: 10000;
  pointer-events: none;
  user-select: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.subtitle-overlay .overlay {
  padding: 5.24px 3.90px 6.24px 3.90px;
  background: rgba(0, 0, 0, 0.70);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 2.10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  min-width: 100px;
  max-width: 80vw;
  word-wrap: break-word;
}

.subtitle-overlay .container {
  width: 100%;
  padding-bottom: 0.86px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.subtitle-overlay .original-text {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 15.60px;
  font-family: 'Roboto', 'Microsoft YaHei', -apple-system, sans-serif;
  font-weight: 400;
  line-height: 21.86px;
  word-wrap: break-word;
  text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
  margin: 0;
  padding: 0;
  white-space: pre-wrap;
}

.subtitle-overlay .translated-text {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 15.60px;
  font-family: 'Inter', 'Microsoft YaHei', -apple-system, sans-serif;
  font-weight: 400;
  line-height: 21.86px;
  word-wrap: break-word;
  text-shadow: 2px 2px 5px rgba(34, 34, 34, 0.75);
  margin: 0;
  padding: 0;
  white-space: pre-wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .subtitle-overlay .original-text,
  .subtitle-overlay .translated-text {
    font-size: 14px;
    line-height: 19px;
  }
  
  .subtitle-overlay .overlay {
    max-width: 90vw;
  }
}

@media (max-width: 480px) {
  .subtitle-overlay .original-text,
  .subtitle-overlay .translated-text {
    font-size: 13px;
    line-height: 18px;
  }
  
  .subtitle-overlay .overlay {
    max-width: 95vw;
    padding: 4px 3px 5px 3px;
  }
}

/* 动画效果 */
.subtitle-overlay {
  opacity: 0;
  transform: translateY(10px);
  animation: subtitleFadeInUp 0.3s ease-out forwards;
}

@keyframes subtitleFadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subtitle-overlay.hiding {
  animation: subtitleFadeOutDown 0.3s ease-in forwards;
}

@keyframes subtitleFadeOutDown {
  to {
    opacity: 0;
    transform: translateY(10px);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .subtitle-overlay .overlay {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .subtitle-overlay .original-text,
  .subtitle-overlay .translated-text {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 1);
  }
}

/* 暗色主题偏好 */
@media (prefers-color-scheme: dark) {
  .subtitle-overlay .overlay {
    background: rgba(0, 0, 0, 0.8);
  }
}

/* 亮色主题偏好 */
@media (prefers-color-scheme: light) {
  .subtitle-overlay .overlay {
    background: rgba(0, 0, 0, 0.65);
  }
}
`;